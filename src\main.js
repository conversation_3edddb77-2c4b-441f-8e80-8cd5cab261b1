import Vue from 'vue';
import App from './App.vue';
import store from './store';
import router from './router';
import permission from './utils/permission';
import { MAP_KEY, LICENSE_CODE } from '@/config/setting';
import EleAdmin from 'ele-admin';
import './styles/index.scss';
import './styles/iconfont/iconfont.css';

import VueClipboard from 'vue-clipboard2';
import i18n from './i18n';
//import axios from 'axios';
import dataV from '@jiaminghi/data-view'

Vue.config.productionTip = false;


window._AMapSecurityConfig = {
  securityJsCode: 'ea4a9e3455fee69913cdbdbe4f8db713'
}

Vue.use(EleAdmin, {
  response: {
    dataName: 'list'
  },
  mapKey: MAP_KEY,
  license: LICENSE_CODE,
  i18n: (key, value) => i18n.t(key, value)
});

Vue.use(permission);

Vue.use(VueClipboard);

Vue.use(dataV);

new Vue({
  router,
  store,
  i18n,
  render: (h) => h(App)
}).$mount('#app');

