<template>
  <div class="ele-table-search">
    <el-form
      :model="form"
      label-width="77px"
      class="ele-table-search-form"
    >
      <el-row :gutter="15">
        <el-col :lg="6" :md="12">
          <el-form-item label="空间ID:">
            <el-select
              v-model="form.space_id"
              placeholder="请选择空间ID"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="item in spaceList"
                :key="item.space_id"
                :label="item.space_name"
                :value="item.space_id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12">
          <el-form-item label="智能体名称:">
            <el-input
              v-model="form.agent_name"
              placeholder="请输入智能体名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12">
          <el-form-item label="类型:">
            <el-select
              v-model="form.type"
              placeholder="请选择类型"
              clearable
              style="width: 100%"
            >
              <el-option label="文字" :value="1" />
              <el-option label="图片" :value="2" />
              <el-option label="视频" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12">
          <el-form-item label="状态:">
            <el-select
              v-model="form.status"
              placeholder="请选择状态"
              clearable
              style="width: 100%"
            >
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="-1" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="ele-table-search-action">
      <el-button
        type="primary"
        icon="el-icon-search"
        class="ele-btn-icon"
        @click="search"
      >
        搜索
      </el-button>
      <el-button icon="el-icon-refresh" @click="reset">重置</el-button>
    </div>
  </div>
</template>

<script>
  import { getSpaceList } from '@/api/aiAgent';

  export default {
    name: 'AgentSearch',
    data() {
      return {
        // 搜索表单数据
        form: {
          space_id: '',
          agent_name: '',
          type: '',
          status: ''
        },
        // 空间列表
        spaceList: []
      };
    },
    mounted() {
      this.loadSpaceList();
    },
    methods: {
      /* 搜索 */
      search() {
        this.$emit('search', this.form);
      },
      /* 重置 */
      reset() {
        this.form = {
          space_id: '',
          agent_name: '',
          type: '',
          status: ''
        };
        this.$emit('search', this.form);
      },
      /* 加载空间列表 */
      loadSpaceList() {
        getSpaceList()
          .then((data) => {
            this.spaceList = data || [];
          })
          .catch((e) => {
            console.error('加载空间列表失败：', e.message);
          });
      }
    }
  };
</script>
