<template>
  <el-card shadow="never">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-row :gutter="15">
        <el-col :sm="8">
          <el-form-item label="类型:" prop="type">
            <el-radio-group v-model="form.type" clearable>
              <el-radio :label="1" value="1">充值</el-radio>
              <el-radio :label="2" value="2">扣除</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="'当前' + balanceName + ':'">
            <span>{{ data.balance }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="操作金额:" prop="money">
            <el-input
              type="number"
              step="0.01"
              v-model="form.money"
              label="金额"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-button type="primary" :loading="loading" @click="save"
          >确认</el-button
        >
      </el-row>
    </el-form>

    <el-card
      :bordered="false"
      dis-hover
      class="ivu-mt"
      :header="balanceName + '记录'"
    >
      <!-- 数据表格 -->
      <balance-search @search="reload" ref="search" />
      <ele-pro-table ref="table" :datasource="datasource" :columns="columns">
        <!-- 订单编号 -->
        <template v-slot:log_no="{ row }">
          <span v-if="row.log_no"> {{ row.log_no }}</span>
          <span v-else> --- </span>
        </template>

        <template v-slot:way="{ row }">
          <ele-dot v-if="row.way == 0" text="后台操作" :ripple="true" />
          <ele-dot v-else-if="row.way == 1" text="点数充值" :ripple="true" />
          <ele-dot v-else-if="row.way == 2" text="新人赠送" :ripple="true" />
          <ele-dot v-else-if="row.way == 3" text="声音克隆" :ripple="true" />
          <ele-dot v-else-if="row.way == 4" text="形象克隆" :ripple="true" />
          <ele-dot v-else-if="row.way == 5" text="视频换脸" :ripple="true" />
          <ele-dot v-else-if="row.way == 6" text="视频剪辑" :ripple="true" />
          <ele-dot v-else-if="row.way == 7" text="购买会员" :ripple="true" />
          <ele-dot v-else-if="row.way == 8" text="AI文案" :ripple="true" />
          <ele-dot v-else-if="row.way == 9" text="照片克隆" :ripple="true" />
          <ele-dot
            v-else-if="row.way == 10"
            text="视频线路三创作"
            :ripple="true"
          />
          <ele-dot v-else-if="row.way == 11" text="二次剪辑" :ripple="true" />
          <ele-dot
            v-else-if="row.way == 12"
            text="AI视频提取文案"
            :ripple="true"
          />
          <ele-dot
            v-else-if="row.way == 13"
            text="视频线路二创作"
            :ripple="true"
          />
          <ele-dot
            v-else-if="row.way == 14"
            text="高保真声音合成"
            :ripple="true"
          />
          <ele-dot
            v-else-if="row.way == 15"
            text="专业版声音克隆"
            :ripple="true"
          />
          <ele-dot
            v-else-if="row.way == 16"
            text="专业版声音合成"
            :ripple="true"
          />
          <ele-dot v-else-if="row.way == 17" text="ai标题" :ripple="true" />
          <ele-dot
            v-else-if="row.way == 18"
            text="抖音视频扣点"
            :ripple="true"
          />
          <ele-dot
            v-else-if="row.way == 19"
            text="快手视频扣点"
            :ripple="true"
          />
          <ele-dot v-else-if="row.way == 20" text="视频号扣点" :ripple="true" />
          <ele-dot v-else-if="row.way == 21" text="小红书扣点" :ripple="true" />
          <ele-dot
            v-else-if="row.way == 22"
            text="获取抖音二维码"
            :ripple="true"
          />
          <ele-dot v-else-if="row.way == 23" text="抖音授权" :ripple="true" />
          <ele-dot
            v-else-if="row.way == 24"
            text="获取视频号二维码"
            :ripple="true"
          />
          <ele-dot v-else-if="row.way == 25" text="视频号授权" :ripple="true" />
          <ele-dot
            v-else-if="row.way == 26"
            text="获取小红薯二维码"
            :ripple="true"
          />
          <ele-dot v-else-if="row.way == 27" text="小红薯授权" :ripple="true" />
          <ele-dot
            v-else-if="row.way == 28"
            text="视频线路四创作"
            :ripple="true"
          />
          <ele-dot
            v-else-if="row.way == 31"
            text="主页视频分析"
            :ripple="true"
          />
          <ele-dot v-else-if="row.way == 32" text="IP账号采集" :ripple="true" />
        </template>

        <template v-slot:money="{ row }">
          <el-tag v-if="row.type == 1 && row.b_type == 1" type="success"
            >+{{ row.money }}点</el-tag
          >
          <el-tag v-if="row.type == 2 && row.b_type == 1" type="danger"
            >-{{ row.money }}点</el-tag
          >
          <el-tag v-if="row.type == 1 && row.b_type == 2" type="success"
            >+{{ row.money }}次</el-tag
          >
          <el-tag v-if="row.type == 2 && row.b_type == 2" type="danger"
            >-{{ row.money }}次</el-tag
          >
          <el-tag v-if="row.type == 1 && row.b_type == 3" type="success"
            >+{{ row.money }}秒</el-tag
          >
          <el-tag v-if="row.type == 2 && row.b_type == 3" type="danger"
            >-{{ row.money }}秒</el-tag
          >
          <el-tag v-if="row.type == 1 && row.b_type == 4" type="danger"
            >+{{ row.money }}字</el-tag
          >
          <el-tag v-if="row.type == 2 && row.b_type == 4" type="danger"
            >-{{ row.money }}字</el-tag
          >

          <el-tag v-if="row.type == 3 && row.b_type == 1" type="success"
            >+{{ row.money }}点</el-tag
          >
          <el-tag v-if="row.type == 3 && row.b_type == 2" type="success"
            >+{{ row.money }}次</el-tag
          >
          <el-tag v-if="row.type == 3 && row.b_type == 3" type="success"
            >+{{ row.money }}秒</el-tag
          >
          <el-tag v-if="row.type == 3 && row.b_type == 4" type="danger"
            >-{{ row.money }}字</el-tag
          >
        </template>

        <!-- 操作列 -->
        <template v-slot:action="{ row }">
          <el-popconfirm
            class="ele-action"
            title="确定要删除此条记录吗？"
            @confirm="remove(row)"
          >
            <template v-slot:reference>
              <el-link type="danger" :underline="false" icon="el-icon-delete">
                删除
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
  </el-card>
</template>

<script>
import BalanceSearch from './user-balance-log-search';
import { addBalance, balanceLog, remove } from '@/api/user/balance';

const DEFAULT_FORM = {
  uid: 0,
  type: 1,
  balance: ''
};

export default {
  name: 'UserBalance',
  components: { BalanceSearch },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: [],
    uid: {
      type: Number,
      default: 0
    }
  },
  computed: {
    balanceName() {
      return this.$store.state.user.balance_name;
    }
  },
  data() {
    return {
      columns: [
        {
          columnKey: 'selection',
          type: 'selection',
          width: 45,
          align: 'center',
          fixed: 'left'
        },
        {
          columnKey: 'index',
          type: 'index',
          width: 45,
          align: 'center',
          showOverflowTooltip: true,
          fixed: 'left'
        },
        {
          prop: 'log_no',
          label: '记录单号',
          showOverflowTooltip: true,
          minWidth: 110,
          slot: 'log_no'
        },
        {
          prop: 'way',
          label: '方式',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110,
          slot: 'way'
        },
        {
          prop: 'desc',
          label: '描述',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110
        },
        {
          prop: 'current',
          label: '操作前余额',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110
        },
        {
          prop: 'money',
          label: '操作金额',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110,
          slot: 'money'
        },

        {
          prop: 'card',
          label: '卡密',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110
        },
        {
          prop: 'create_time',
          label: '操作时间',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 100
        },
        {
          columnKey: 'action',
          label: '操作',
          width: 150,
          align: 'center',
          resizable: false,
          slot: 'action',
          hideInSetting: true,
          showOverflowTooltip: true
        }
      ],
      // 表单数据
      form: { ...DEFAULT_FORM },
      rules: {
        balance: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入充值点数'
          }
        ]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  methods: {
    /* 表格数据源 */
    datasource({ page, limit, where, order, filterValue }) {
      where.uid = this.uid;
      return balanceLog({
        ...where,
        ...order,
        ...filterValue,
        page,
        limit
      });
    },

    /* 刷新表格 */
    reload(where) {
      this.$refs.table.reload({ page: 1, where: where });
    },

    /* 保存编辑 */
    save() {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          return false;
        }
        this.loading = true;

        const data = {
          ...this.form,
          uid: this.data.id
        };

        addBalance(data)
          .then((msg) => {
            this.loading = false;
            this.$message.success(msg);
            this.reload();
            this.form.balance = '';
          })
          .catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
          });
      });
    },
    /* 更新visible */
    updateVisible(value) {
      this.form.balance = '';
      this.$emit('update:visible', value);
    },
    /* 删除 */
    remove(row) {
      const loading = this.$loading({ lock: true });
      remove(row.id)
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
    }
  },
  watch: {
    uid() {
      this.reload();
    },
    visible(visible) {
      if (visible) {
        if (this.data) {
          this.form = { ...DEFAULT_FORM };
          this.$util.assignObject(this.form, this.data);
          this.isUpdate = true;
          this.form.uid = this.data.id;
        } else {
          this.isUpdate = false;
          this.form.balance = '';
        }
      } else {
        this.$refs['search'].reset();
        this.$refs['form'].clearValidate();
        this.form = { ...DEFAULT_FORM };
        this.form.balance = '';
      }
    }
  }
};
</script>
