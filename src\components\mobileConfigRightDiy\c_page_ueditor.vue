<template>
    <div class="box" v-if="configData">
        <tinymce-editor v-model="configData.val" :init="option"  style="width: 100%; height: 60%"/>
    </div>
</template>

<script>
  import TinymceEditor from '@/components/TinymceEditor';
    export default {
        name: 'c_page_ueditor',
        props: {
            configObj: {
                type: Object
            },
            configNme: {
                type: String
            }
        },
        components: { TinymceEditor },
        data () {
            return {

                option: {
                    height: 520,
                    relative_urls : false,
                    convert_urls: false,
                    images_upload_handler: (blobInfo, success, error) => {
                        debugger;
                        const file = blobInfo.blob();
                        // 使用 axios 上传，实际开发这段建议写在 api 中再调用 api
                        const formData = new FormData();
                        formData.append('file', file, file.name);
                        request({
                        url: '/common/uploadFile',
                        method: 'post',
                        data: formData,
                        onUploadProgress: (e) => {  // 文件上传进度回调
                                
                            }
                        }).then((res) => {
                            if(res.data.code === 0) {
                                success(res.data.data.url);
                            }else{
                                error(res.data.message);
                            }
                        }).catch((e) => {
                            error('exception');
                        });


                    },
                    // 自定义文件上传(这里使用把选择的文件转成blob演示)
                    file_picker_callback: (callback, value, meta) => {
                        const input = document.createElement('input');
                        input.setAttribute('type', 'file');
                        // 设定文件可选类型
                        if (meta.filetype === 'image') {
                        input.setAttribute('accept', 'image/*');
                        } else if (meta.filetype === 'media') {
                        input.setAttribute('accept', 'video/*');
                        }
                        input.onchange = () => {
                        let file = input.files[0];
                        let reader = new FileReader();
                        reader.onload = (e) => {
                            let blob = new Blob([e.target.result], { type: file.type });
                            callback(URL.createObjectURL(blob));
                        };
                        reader.readAsArrayBuffer(file);
                        };
                        input.click();
                    }
                    },

                myConfig: {
                    autoHeightEnabled: false, // 编辑器不自动被内容撑高
                    initialFrameHeight: 350, // 初始容器高度
                    initialFrameWidth: '100%', // 初始容器宽度
                    UEDITOR_HOME_URL: '/admin/UEditor/',
                    serverUrl: ''
                },
                description: '',
                defaults: {},
                configData:{}
            }
        },
        created () {
            this.defaults = this.configObj
            this.configData = this.configObj[this.configNme]
        },
        watch: {
            configObj: {
                handler (nVal, oVal) {
                    this.defaults = nVal
                    this.configData = nVal[this.configNme]
                },
                immediate: true,
                deep: true
            }
        },
        methods: {
            // 添加自定义弹窗
            addCustomDialog (editorId) {
                window.UE.registerUI('test-dialog', function (editor, uiName) {
                    // 创建 dialog
                    let dialog = new window.UE.ui.Dialog({
                        iframeUrl: '/admin/widget.images/index.html?fodder=dialog',
                        editor: editor,
                        name: uiName,
                        title: '上传图片',
                        cssRules: 'width:1200px;height:500px;padding:20px;'
                    });
                    this.dialog = dialog;
                    let btn = new window.UE.ui.Button({
                        name: 'dialog-button',
                        title: '上传图片',
                        cssRules: `background-image: url(../../../assets/images/icons.png);background-position: -726px -77px;`,
                        onclick: function () {
                            // 渲染dialog
                            dialog.render();
                            dialog.open();
                        }
                    });
                    return btn;
                }, 37);
                window.UE.registerUI('video-dialog', function (editor, uiName) {
                    let dialog = new window.UE.ui.Dialog({
                        iframeUrl: '/admin/widget.video/index.html?fodder=video',
                        editor: editor,
                        name: uiName,
                        title: '上传视频',
                        cssRules: 'width:1000px;height:500px;padding:20px;'
                    });
                    this.dialog = dialog;
                    let btn = new window.UE.ui.Button({
                        name: 'video-button',
                        title: '上传视频',
                        cssRules: `background-image: url(../../../assets/images/icons.png);background-position: -320px -20px;`,
                        onclick: function () {
                            // 渲染dialog
                            dialog.render();
                            dialog.open();
                        }
                    });
                    return btn;
                }, 38);
            }
        }
    }
</script>

<style scoped>

</style>
