<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">转发设置</div>
      <div class="ele-page-desc">
        用于转发相关设置。
      </div>
    </div>
    <div class="ele-body" style="padding-bottom: 71px;background: var(--color-white);">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="150px"
        @keyup.enter.native="submit"
        @submit.native.prevent
      >
        <el-card shadow="never" body-style="padding: 22px 22px 0 22px;">


          <el-row>
            <el-col>

              <el-form-item label="是否开启:" prop="is_open">
                <el-radio-group v-model="form.is_open">
                  <el-radio :label="1" value="1" >开启</el-radio>
                  <el-radio :label="2" value="2" >隐藏</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row> 

          
          <el-form-item>
            <el-button type="primary" :loading="loading" @click="submit">
              提交
            </el-button>
          </el-form-item>
        </el-card>
        
      </el-form>
    </div>
  </div>
  
</template>

<script>
  import { query ,save } from '@/api/repost/set';
  const DEFAULT_FORM = {
    is_open:2
  };

  export default {
    name: 'RepostSet',
    components: {  },
    data() {
      return {
        // 表单提交状态
        loading: false,
        // 表单数据
        form: {
          ...DEFAULT_FORM
        },
        // 表单验证规则
        rules: {
          
        },
        // 表单验证信息
        validMsg: '',
      };
    },
    mounted() {
      query().then((msg) => {
        if(msg != null){
          this.form = msg;
        }
      })
      .catch((e) => {
        this.$message.error(e.message);
      });
    },
    methods: {
      /* 表单提交 */
      submit() {
        this.$refs['form'].validate((valid, obj) => {
          if (valid) {
            this.validMsg = '';
            this.loading = true;
            const data = {
            ...this.form
            };
            save(data).then((msg) => {
              this.loading = false;
              this.$message.success(msg);
            }).catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });
          } else {
            this.validMsg = ` 共有校验 ${Object.keys(obj).length} 项不通过`;
            return false;
          }
        });
      },
    }
  };
</script>
