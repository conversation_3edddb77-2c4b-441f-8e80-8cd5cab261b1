<template>
    <div>
        <div class="ele-page-header">
            <div class="ele-page-title">系统基础配置</div>
            <div class="ele-page-desc">
                用于系统基础展示等场景。
            </div>
        </div>
        <div class="ele-body" style="padding-bottom: 71px">
            <el-form
                    ref="form"
                    :model="form"
                    :rules="rules"
                    label-width="130px"
                    @keyup.enter.native="submit"
                    @submit.native.prevent
            >
                <el-card shadow="never" header="基础配置" body-style="padding: 22px 22px 0 22px;">
                    <el-row :gutter="15">
                        <el-col :lg="8" :md="8">
                            <el-form-item label="浏览器图标:" prop="site_ico">
                <span slot="label">
                  浏览器图标
                  <el-tooltip placement="top">
                    <div slot="content">
                        用于打开网站时浏览器标签页图标展示<br/>
                        大小尺寸为100px * 100px
                    </div>
                    <i class="el-icon-question"/>
                  </el-tooltip>
                </span>

                                <div class="ele-image-upload-list">
                                    <div class="ele-image-upload-item ele-image-upload-button"
                                         @click="modalPicTap('dan','site_ico','浏览器图标')">
                                        <div>
                                            <div tabindex="0" class="el-upload el-upload--text">

                                                <div class="el-upload-dragger">
                                                    <i class="el-icon-plus ele-image-upload-icon"></i>
                                                </div>

                                                <div class="ele-image-upload-item" style="margin:0 0 0 0;"
                                                     v-if="form.site_ico!=''">
                                                    <div class="el-image">
                                                        <img :src="form.site_ico" width="100%" height="100%"
                                                             class="el-image__inner" style="object-fit: cover;">
                                                    </div>
                                                    <div class="ele-image-upload-close"
                                                         @click="handleRemove('site_ico')"><i class="el-icon-close"></i>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>

                        <el-col :lg="8" :md="10">
                            <el-form-item label="系统图标:" prop="site_image">
                <span slot="label">
                  系统图标
                  <el-tooltip placement="top">
                    <div slot="content">
                        系统左侧LOGO展示<br/>
                        大小尺寸为100px * 100px
                    </div>
                    <i class="el-icon-question"/>
                  </el-tooltip>
                </span>
                                <!-- <ele-image-upload v-model="form.site_image" :limit="1" :drag="true" :multiple="false"  @upload="onUpload" /> -->
                                <div class="ele-image-upload-list">
                                    <div class="ele-image-upload-item ele-image-upload-button"
                                         @click="modalPicTap('dan','site_image','系统图标')">
                                        <div>
                                            <div tabindex="0" class="el-upload el-upload--text">

                                                <div class="el-upload-dragger">
                                                    <i class="el-icon-plus ele-image-upload-icon"></i>
                                                </div>

                                                <div class="ele-image-upload-item" style="margin:0 0 0 0;"
                                                     v-if="form.site_image!=''">
                                                    <div class="el-image">
                                                        <img :src="form.site_image" width="100%" height="100%"
                                                             class="el-image__inner" style="object-fit: cover;">
                                                    </div>
                                                    <div class="ele-image-upload-close"
                                                         @click="handleRemove('site_image')"><i
                                                            class="el-icon-close"></i></div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>

                        <el-col :lg="8" :md="12">
                            <el-form-item label="备案号:" label-width="130px" prop="site_beian">
                                <el-input v-model="form.site_beian" placeholder="请输入备案号" clearable></el-input>
                            </el-form-item>

                            <el-form-item label="京公网备案号:" label-width="130px" prop="site_jingbeian">
                                <el-input v-model="form.site_jingbeian" placeholder="请输入京公网备案号" clearable></el-input>
                            </el-form-item>

                            <el-form-item label="版权信息:" label-width="130px" prop="site_copyright">
                                <el-input v-model="form.site_copyright" placeholder="请输入版权信息" clearable/>
                            </el-form-item>
                        </el-col>


                        <!-- <el-col :lg="8" :md="12">
                          <el-form-item label="站点名称:" label-width="130px" prop="site_name">
                            <el-input v-model="form.site_name" placeholder="请输入站点名称" clearable/>
                          </el-form-item>
                        </el-col> -->
                    </el-row>


                    <el-row :gutter="15">
                        <el-col :lg="8" :md="12">
                            <el-form-item label="在线更新id:" label-width="130px" prop="site_appid">
                                <el-input v-model="form.site_appid" placeholder="请输入在线更新id" clearable/>
                            </el-form-item>
                        </el-col>

                    </el-row>

                </el-card>


                <el-card shadow="never" header="LOG配置" body-style="padding: 22px 22px 0 22px;">
                    <el-row :gutter="15">
                        <el-col :lg="8" :md="10">
                            <el-form-item label="登录背景图:" prop="site_login_background">
                <span slot="label">
                  登录背景图
                  <el-tooltip placement="top">
                    <div slot="content">
                        用于登录时的背景图片选择<br/>
                        大小尺寸跟分辨率同比例
                    </div>
                    <i class="el-icon-question"/>
                  </el-tooltip>
                </span>
                                <!--<ele-image-upload  v-model="form.site_login_background" :limit="1" :drag="true" :multiple="false"  @upload="onUpload" />-->
                                <div class="ele-image-upload-list">
                                    <div class="ele-image-upload-item ele-image-upload-button"
                                         @click="modalPicTap('dan','site_login_background','登录背景图')">
                                        <div>
                                            <div tabindex="0" class="el-upload el-upload--text">

                                                <div class="el-upload-dragger">
                                                    <i class="el-icon-plus ele-image-upload-icon"></i>
                                                </div>

                                                <div class="ele-image-upload-item" style="margin:0 0 0 0;"
                                                     v-if="form.site_login_background!=''">
                                                    <div class="el-image">
                                                        <img :src="form.site_login_background" width="100%"
                                                             height="100%" class="el-image__inner"
                                                             style="object-fit: cover;">
                                                    </div>
                                                    <div class="ele-image-upload-close"
                                                         @click="handleRemove('site_login_background')"><i
                                                            class="el-icon-close"></i></div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>

                        <el-col :lg="8" :md="10">
                            <el-form-item label="登录左侧图标:" prop="site_login_image">
                <span slot="label">
                  登录左侧图标
                  <el-tooltip placement="top">
                    <div slot="content">
                        登录页左侧展示<br/>
                        大小尺寸为510px * 420px
                    </div>
                    <i class="el-icon-question"/>
                  </el-tooltip>
                </span>
                                <!-- <ele-image-upload v-model="form.site_image" :limit="1" :drag="true" :multiple="false"  @upload="onUpload" /> -->
                                <div class="ele-image-upload-list">
                                    <div class="ele-image-upload-item ele-image-upload-button"
                                         @click="modalPicTap('dan','site_login_image','登录左侧图标')">
                                        <div>
                                            <div tabindex="0" class="el-upload el-upload--text">

                                                <div class="el-upload-dragger">
                                                    <i class="el-icon-plus ele-image-upload-icon"></i>
                                                </div>

                                                <div class="ele-image-upload-item" style="margin:0 0 0 0;"
                                                     v-if="form.site_login_image!=''">
                                                    <div class="el-image">
                                                        <img :src="form.site_login_image" width="100%" height="100%"
                                                             class="el-image__inner" style="object-fit: cover;">
                                                    </div>
                                                    <div class="ele-image-upload-close"
                                                         @click="handleRemove('site_login_image')"><i
                                                            class="el-icon-close"></i></div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>


                <el-card shadow="never" header="客服配置" body-style="padding: 22px 22px 0 22px;">
                    <el-row :gutter="15">
                        <el-col :lg="8" :md="8">
                            <el-form-item label="客服二维码:" prop="kf_img">
                <span slot="label">
                  客服二维码
                  <el-tooltip placement="top">
                    <div slot="content">
                      大小尺寸为100px * 100px
                    </div>
                    <i class="el-icon-question"/>
                  </el-tooltip>
                </span>
                                <!--<ele-image-upload v-model="form.kf_img" :limit="1" :drag="true" :multiple="false"  @upload="onUpload" />-->
                                <div class="ele-image-upload-list">
                                    <div class="ele-image-upload-item ele-image-upload-button"
                                         @click="modalPicTap('dan','kf_img','客服二维码')">
                                        <div>
                                            <div tabindex="0" class="el-upload el-upload--text">
                                                <div class="el-upload-dragger">
                                                    <i class="el-icon-plus ele-image-upload-icon"></i>
                                                </div>

                                                <div class="ele-image-upload-item" style="margin:0 0 0 0;"
                                                     v-if="form.kf_img!=''">
                                                    <div class="el-image">
                                                        <img :src="form.kf_img" width="100%" height="100%"
                                                             class="el-image__inner" style="object-fit: cover;">
                                                    </div>
                                                    <div class="ele-image-upload-close" @click="handleRemove('kf_img')">
                                                        <i class="el-icon-close"></i></div>
                                                </div>

                                            </div>

                                        </div>
                                    </div>
                                </div>

                            </el-form-item>

                            <el-form-item label="上班时间:" prop="kf_date">
                                <el-input v-model="form.kf_date" placeholder="请输入上班时间描述" clearable/>
                            </el-form-item>

                        </el-col>
                        <el-col :lg="8" :md="12">
                            <el-form-item label="手机号:" prop="kf_phone">
                                <el-input v-model="form.kf_phone" placeholder="请输入手机号" clearable/>
                            </el-form-item>
                            <el-form-item label="微信号:" prop="kf_wxno">
                                <el-input v-model="form.kf_wxno" placeholder="请输入微信号" clearable/>
                            </el-form-item>
                            <el-form-item label="邮箱:" prop="kf_mailbox">
                                <el-input v-model="form.kf_mailbox" placeholder="请输入" clearable>
                                </el-input>
                            </el-form-item>
                            <el-form-item label="地址:" prop="kf_address">
                                <el-input v-model="form.kf_address" placeholder="请输入地址" clearable/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>
                <el-card shadow="never" header="流冠IP配置" body-style="padding: 22px 22px 0 22px;">
                    <el-row :gutter="15">
                        <el-col :lg="8" :md="12">
                            <el-form-item label="流冠IP订单编号:" prop="liuguang_ordersn">
                                <el-input v-model="form.liuguang_ordersn" placeholder="订单编号" clearable/>
                            </el-form-item>
                            <el-form-item label="流冠密钥:" prop="liuguang_appsecret">
                                <el-input  v-model="form.liuguang_appsecret" placeholder="密钥" clearable/>
                            </el-form-item>
                        </el-col>

                    </el-row>

                </el-card>

                <el-card shadow="never" header="帮助" body-style="padding: 22px 22px 0 22px;">

                    <el-row>


                        <el-col :sm="12">
                            <el-form-item label="帮助页面开关:" prop="help_switch">
                                <el-radio-group v-model="form.help_switch">
                                    <el-radio label='1' value='1'>开启</el-radio>
                                    <el-radio label='2' value='2'>关闭</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>
                <el-card shadow="never" header="二次剪辑" body-style="padding: 22px 22px 0 22px;">

                    <el-row>
                        <el-col :sm="12">
                            <el-form-item label="二次剪辑:" prop="help_switch">
                                <el-radio-group v-model="form.clip_swtich">
                                    <el-radio label='1' value='1'>开启</el-radio>
                                    <el-radio label='2' value='2'>关闭</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>

                <el-card shadow="never" header="更新说明" body-style="padding: 22px 22px 0 22px;">

                    <el-row>


                        <el-col :sm="12">
                            <el-form-item label="更新说明开关:" prop="upload_describe_swich">
                                <el-radio-group v-model="form.upload_describe_swich">
                                    <el-radio label='1' value='1'>开启</el-radio>
                                    <el-radio label='2' value='2'>关闭</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="更新说明:" prop="upload_describe">
                                <tinymce-editor v-model="form.upload_describe" :init="editorConfig"/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>



                <el-card shadow="never" header="爆款跟拍文案要求" body-style="padding: 22px 22px 0 22px;">

                    <el-row>


                        <el-col :sm="12">
                          
                            <el-form-item label="爆款跟拍文案要求:" prop="copywriting_require">
                                <tinymce-editor v-model="form.copywriting_require" :init="editorConfig"/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>


 <el-card shadow="never" header="ip账号采集链接要求" body-style="padding: 22px 22px 0 22px;">

                    <el-row>


                        <el-col :sm="12">
                       
                            <el-form-item label="ip账号采集链接要求:" prop="link_require">
                                <tinymce-editor v-model="form.link_require" :init="editorConfig"/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>

                
                


                <uploadPictures
                        :isChoice="isChoice"
                        :visible.sync="modalPic"
                        @getPic="getPic"
                        :gridBtn="gridBtn"
                        :gridPic="gridPic"
                        :title="modalTitle"
                ></uploadPictures>

                <!-- 底部工具栏 -->
                <div class="ele-bottom-tool">
                    <div v-if="validMsg" class="ele-text-danger">
                        <i class="el-icon-circle-close"></i>
                        <span>{{ validMsg }}</span>
                    </div>
                    <div class="ele-bottom-tool-actions">
                        <el-button type="primary" :loading="loading" @click="submit">
                            提交
                        </el-button>
                    </div>
                </div>
            </el-form>
        </div>
    </div>

</template>

<script>
    const DEFAULT_FORM = {
        logo_title: '',
        site_image: [],
        site_name: '',
        site_ico: [],
        site_appid: '',
        site_copyright: '',
        site_beian: '',
        site_jingbeian: '',
        site_login_background: [],
        site_login_image: [],
        kf_date: '',
        kf_img: '',
        kf_mailbox: '',
        kf_phone: '',
        kf_wxno: '',
        kf_address: '',
        liuguang_ordersn:'',
        liuguang_appsecret:'',
          copywriting_require:'',
            link_require:'',

    };
    import EleImageUpload from 'ele-admin/es/ele-image-upload';
    import request from '@/utils/request';
    import uploadPictures from "@/components/uploadPictures";
    import {save, query} from '@/api/system/config';
    import TinymceEditor from '@/components/TinymceEditor';

    export default {
        name: 'SysBase',
        components: {EleImageUpload, uploadPictures, TinymceEditor},
        data() {
            return {
                modalTitle: '',
                modalPic: false,
                isChoice: "单选",
                gridBtn: {
                    xl: 4,
                    lg: 8,
                    md: 8,
                    sm: 8,
                    xs: 8,
                },
                gridPic: {
                    xl: 6,
                    lg: 8,
                    md: 12,
                    sm: 12,
                    xs: 12,
                },
                // 表单提交状态
                loading: false,
                // 表单数据
                form: {
                    ...DEFAULT_FORM
                },
                // 表单验证规则
                rules: {},
                // 表单验证信息
                validMsg: '',
                editorConfig: {
                    height: 525,
                    relative_urls: false,
                    convert_urls: false,
                    images_upload_handler: (blobInfo, success, error) => {
                        const file = blobInfo.blob();
                        // 使用 axios 上传，实际开发这段建议写在 api 中再调用 api
                        const formData = new FormData();
                        formData.append('file', file, file.name);
                        request({
                            url: '/common/uploadFile',
                            method: 'post',
                            data: formData,
                            onUploadProgress: (e) => {  // 文件上传进度回调

                            }
                        }).then((res) => {
                            if (res.data.code === 0) {
                                success(res.data.data.url);
                            } else {
                                error(res.data.message);
                            }
                        }).catch((e) => {
                            error('exception');
                        });
                    }
                },

            };
        },
        mounted() {
            query({group: 'site'}).then((msg) => {
                if (msg != null) {
                    this.form = msg;
                }
            })
                .catch((e) => {
                    this.$message.error(e.message);
                });
        },
        methods: {
            // 选择图片
            modalPicTap(tit, picTit, openTitle) {
                this.modalTitle = openTitle;
                this.isChoice = tit === "dan" ? "单选" : "多选";
                this.picTit = picTit;
                this.modalPic = true;
            },
            // 选中图片
            getPic(pc) {
                switch (this.picTit) {
                    case "site_image":
                        this.form.site_image = pc.satt_dir;
                        break;
                    case "site_login_background":
                        this.form.site_login_background = pc.satt_dir;
                        break;
                    case "merchant_img":
                        this.form.merchant_img = pc.satt_dir;
                        break;
                    case "site_ico":
                        this.form.site_ico = pc.satt_dir;
                        break;
                    case "kf_img":
                        this.form.kf_img = pc.satt_dir;
                        break;
                    case "site_login_image":
                        this.form.site_login_image = pc.satt_dir;
                        break;
                }
                this.modalPic = false;
            },
            //删除图片
            handleRemove(field) {
                this.form[field] = '';
            },
            /* 表单提交 */
            submit() {
                this.$refs['form'].validate((valid, obj) => {
                    if (valid) {
                        this.validMsg = '';
                        this.loading = true;
                        const data = {
                            ...this.form
                        };
                        save(data).then((msg) => {
                            this.loading = false;
                            this.$message.success(msg);
                        }).catch((e) => {
                            this.loading = false;
                            this.$message.error(e.message);
                        });
                    } else {
                        this.validMsg = ` 共有校验 ${Object.keys(obj).length} 项不通过`;
                        return false;
                    }
                });
            },
            onUpload(item) {
                console.log('item:', item);
                item.status = 'uploading';
                const formData = new FormData();
                formData.append('file', item.file);
                request({
                    url: '/common/uploadFile',
                    method: 'post',
                    data: formData,
                    onUploadProgress: (e) => {  // 文件上传进度回调
                        if (e.lengthComputable) {
                            item.progress = e.loaded / e.total * 100;
                        }
                    }
                }).then((res) => {
                    if (res.data.code === 0) {
                        item.status = 'done';
                        item.url = res.data.data.url;
                        // 如果你上传的不是图片格式, 建议将 url 字段作为缩略图, 再添加其它字段作为最后提交数据
                        //item.url = res.data.data.thumbnail;  // 也可以不赋值 url 字段, 默认会显示为一个文件图标
                        item.fileUrl = res.data.data.url;
                    }
                }).catch((e) => {
                    item.status = 'exception';
                });
            }
        }
    };
</script>
