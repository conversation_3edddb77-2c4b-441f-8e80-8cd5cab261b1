<template>
  <ele-table-select
    ref="select"
    :clearable="true"
    size="small"
    :value="value"
    placeholder="请选择"
    value-key="id"
    label-key="name"
    prefix-icon="el-icon-search"
    :table-config="tableConfig"
    @select="updateValue"
  >
    <template v-slot:toolbar>
      <div style="max-width: 200px">
        <el-input
          clearable
          size="small"
          v-model="where.keywords"
          placeholder="输入关键字搜索"
          prefix-icon="el-icon-search"
          @change="search"
        />
      </div>
    </template>

    <template v-slot:day="{ row }">
      <div>天数：{{row.day}}</div>
      <div>视频合成秒数：{{row.second_infinite == 0 ? row.second : '无限'}}</div>
      <div>声音高保真次数：{{row.voice_twin_count}}</div>


      <div>声音高保真合成字数：{{row.high_fidelity_words_number}}</div>
      <div>ai文案仿写|提取次数：{{row.ai_copywriting_times}}</div>
      <div>专业版声音克隆次数：{{row.xunfei_sound_clone_words_number}}</div>
      <div>专业版声音合成字数：{{row.xunfei_fidelity_words_number}}</div>
       <div>ai标题：{{row.ai_title_times}}</div>
    </template>
  
  </ele-table-select>
  
</template>

<script>
  import { list } from '@/api/member/list';
  export default {
    name: 'Pages',
    props: {
      // 修改回显的数据
      value: undefined
    },
    data() {
      return {
        // 搜索表单
        where: {
          keywords: ''
        },
        tableConfig: {
          datasource: ({ page, limit, where, order }) => {
            return list({ ...where, ...order, page, limit });
          },
          columns: [
            {
              columnKey: 'index',
              type: 'index',
              width: 45,
              align: 'center'
            },
            {
              prop: 'name',
              label: '名称',
              showOverflowTooltip: true,
              minWidth: 110,
            },
            {
              prop: 'money',
              label: '价格',
              align: 'center',
              showOverflowTooltip: true,
              minWidth: 90
            },
            {
              prop: 'day',
              label: '天数',
              showOverflowTooltip: true,
              minWidth:150,
              slot: 'day'
            },
          ],
              pageSize:3,
          pageSizes:[3,10, 20, 30, 40, 50, 100],
          rowClickChecked: true,
          rowClickCheckedIntelligent: false,
          toolkit: ['reload', 'columns'],
          size: 'small',
          toolStyle: { padding: '0 10px' }
          
        }
      };
    },
    methods: {
      updateValue(data) {
        this.$emit('done', data);
      },
      // 搜索
      search(where) {
        this.$refs.select.reload({
          where: where,
          page: 1
        });
      }
    }
  };
</script>
