<template>
  <div class="ele-body ele-body-card">
    <el-row :gutter="15">
      <el-col :md="12" :sm="12">
        <el-card shadow="never" class="monitor-count-card" header="合伙人统计">
          <div class="ele-cell">
            <div class="ele-cell-content">
              <el-tag size="large" class="ele-tag-round">
                <i class="el-icon-_database-solid"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up :end-val="indexData.partnerDayCount" />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                合伙人今日数量
              </div>
            </div>
            <div class="ele-cell-content">
              <el-tag size="large" type="success" class="ele-tag-round">
                <i class="el-icon-s-marketing"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up :end-val="indexData.partnerCount" />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                合伙人累计数量
              </div>
            </div>

            <div class="ele-cell-content">
              <el-tag size="large" type="warning" class="ele-tag-round">
                <i class="el-icon-s-order"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up :end-val="indexData.cardDayCount" />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                卡密今日数量
              </div>
            </div>
            <div class="ele-cell-content">
              <el-tag size="large" type="danger" class="ele-tag-round">
                <i class="el-icon-_money-solid"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up :end-val="indexData.cardCount" />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                卡密累计数量
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :md="12" :sm="12">
        <el-card shadow="never" class="monitor-count-card" header="点数统计">
          <div class="ele-cell">
            <div class="ele-cell-content">
              <el-tag size="large" class="ele-tag-round">
                <i class="el-icon-_database-solid"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up
                  :end-val="indexData.inPointMoneyDay * 1"
                  :options="countUpOptions"
                />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                今日收入点数
              </div>
            </div>
            <div class="ele-cell-content">
              <el-tag size="large" type="success" class="ele-tag-round">
                <i class="el-icon-s-marketing"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up
                  :end-val="indexData.inPointMoney * 1"
                  :options="countUpOptions"
                />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                累计收入点数
              </div>
            </div>

            <div class="ele-cell-content">
              <el-tag size="large" type="warning" class="ele-tag-round">
                <i class="el-icon-s-order"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up
                  :end-val="indexData.outPointMoneyDay * 1"
                  :options="countUpOptions"
                />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                今日支出点数
              </div>
            </div>
            <div class="ele-cell-content">
              <el-tag size="large" type="danger" class="ele-tag-round">
                <i class="el-icon-_money-solid"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up
                  :end-val="indexData.outPointMoney * 1"
                  :options="countUpOptions"
                />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                累计支出点数
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="15">
      <el-col :md="8" :sm="12">
        <el-card shadow="never" class="monitor-count-card" header="会员统计">
          <div class="ele-cell">
            <div class="ele-cell-content">
              <el-tag size="large" class="ele-tag-round">
                <i class="el-icon-s-help"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up :end-val="indexData.memberCount" />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                会员数量
              </div>
            </div>
            <div class="ele-cell-content">
              <el-tag size="large" type="success" class="ele-tag-round">
                <i class="el-icon-s-order"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up :end-val="indexData.memberCountDay" />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                会员今日数量
              </div>
            </div>
            <div class="ele-cell-content">
              <el-tag size="large" type="warning" class="ele-tag-round">
                <i class="el-icon-_time-solid"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up
                  :end-val="indexData.memberMoneyDay * 1"
                  :options="countUpOptions"
                />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                会员今日金额
              </div>
            </div>
            <div class="ele-cell-content">
              <el-tag size="large" type="danger" class="ele-tag-round">
                <i class="el-icon-_money-solid"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up
                  :end-val="indexData.memberMoney * 1"
                  :options="countUpOptions"
                />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                会员累计金额
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :md="8" :sm="12">
        <el-card shadow="never" class="monitor-count-card" header="佣金统计">
          <div class="ele-cell">
            <div class="ele-cell-content">
              <el-tag size="large" type="warning" class="ele-tag-round">
                <i class="el-icon-s-order"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up
                  :end-val="indexData.brokerageDay * 1"
                  :options="countUpOptions"
                />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                今日总佣金
              </div>
            </div>
            <div class="ele-cell-content">
              <el-tag size="large" type="danger" class="ele-tag-round">
                <i class="el-icon-_database-solid"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up
                  :end-val="indexData.brokerageTotal * 1"
                  :options="countUpOptions"
                />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                累计总佣金
              </div>
            </div>
            <div class="ele-cell-content">
              <el-tag size="large" type="warning" class="ele-tag-round">
                <i class="el-icon-s-order"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up
                  :end-val="indexData.ShareholderDay * 1"
                  :options="countUpOptions"
                />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                今日股东分红
              </div>
            </div>
            <div class="ele-cell-content">
              <el-tag size="large" type="warning" class="ele-tag-round">
                <i class="el-icon-s-order"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up
                  :end-val="indexData.ShareholderTotal * 1"
                  :options="countUpOptions"
                />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                累计股东分红
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :md="8" :sm="12">
        <el-card shadow="never" class="monitor-count-card" header="用户统计">
          <div class="ele-cell">
            <div class="ele-cell-content">
              <el-tag size="large" class="ele-tag-round">
                <i class="el-icon-user-solid"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up :end-val="indexData.userCount" />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                用户数量
              </div>
            </div>
            <div class="ele-cell-content">
              <el-tag size="large" type="danger" class="ele-tag-round">
                <i class="el-icon-_integral-solid"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up
                  :end-val="indexData.userMoney * 1"
                  :options="countUpOptions"
                />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                用户累计点数
              </div>
            </div>
            <div class="ele-cell-content">
              <el-tag size="large" type="warning" class="ele-tag-round">
                <i class="el-icon-circle-plus"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up :end-val="indexData.userCountDay" />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                用户今日新增
              </div>
            </div>
            <div class="ele-cell-content">
              <el-tag size="large" type="success" class="ele-tag-round">
                <i class="el-icon-s-promotion"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up :end-val="indexData.userCountWeek" />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                用户本周新增
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <statis-chart></statis-chart>
  </div>
</template>

<script>
import statisChart from './components/statis-chart';
import VueCountUp from 'vue-countup-v2';
import { getIndexData } from '@/api/statis';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart, BarChart, PieChart } from 'echarts/charts';
import {
  GridComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components';
import VChart from 'vue-echarts';
import { echartsMixin } from '@/utils/echarts-mixin';
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  PieChart,
  GridComponent,
  TooltipComponent,
  LegendComponent
]);
export default {
  name: 'StatisticsIndex',
  components: { statisChart, VueCountUp, VChart },
  mixins: [echartsMixin(['orderChart'])],

  data() {
    return {
      orderChartOption: {},
      countUpOptions: {
        useEasing: true,
        useGrouping: true,
        decimalPlaces: 2,
        separator: ',',
        decimal: '.',
        prefix: '',
        suffix: ''
      },
      indexData: {
        userCount: 0,
        userCountDay: 0,
        userCountWeek: 0,
        userMoney: 0,
        inPointMoneyDay: 0,
        inPointMoney: 0,
        outPointMoneyDay: 0,
        outPointMoney: 0,
        memberCount: 0,
        memberMoney: 0,
        memberMoneyDay: 0,
        memberCountDay: 0,
        partnerCount: 0,
        cardCount: 0,
        partnerDayCount: 0,
        cardDayCount: 0,
        brokerageDay: 0,
        brokerageTotal: 0,
        ShareholderDay: 0,
        ShareholderTotal: 0
      }
    };
  },
  created() {
    this.getShowData();
  },
  methods: {
    getShowData() {
      getIndexData()
        .then((data) => {
          if (data != null) {
            this.indexData = data;
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    }
  }
};
</script>

<style scoped>
.monitor-count-card ::v-deep .el-card__body {
  padding-top: 18px;
  text-align: center;
  position: relative;
}

.monitor-count-card ::v-deep .el-tag {
  border-color: transparent;
  font-size: 15px;
}

.monitor-count-card .monitor-count-card-num {
  font-weight: 500;
  font-size: 32px;
  margin-top: 12px;
}

.monitor-count-card .monitor-count-card-text {
  font-size: 12px;
  margin: 10px 0;
}

.monitor-count-card .monitor-count-card-trend {
  font-weight: 600;
  padding: 6px 0;
}

.monitor-count-card .monitor-count-card-trend > i {
  font-size: 12px;
  font-weight: 600;
  margin-right: 5px;
}

.monitor-count-card .monitor-count-card-tips {
  position: absolute;
  top: 15px;
  right: 15px;
  cursor: pointer;
}
</style>
