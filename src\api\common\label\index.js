import axios from '@/utils/request';

/**
 * 查询所有标签
 * @param params 查询条件
 */
export async function getLabel() {
  const res = await axios.get('/common/getLabel', {
    
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 添加标签
 * @param data 标签信息
 */
 export async function add(data) {
  const res = await axios.post('/common/addLabel', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}





