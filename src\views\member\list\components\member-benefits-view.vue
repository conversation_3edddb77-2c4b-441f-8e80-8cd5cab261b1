<!-- 权益描述查看弹窗 -->
<template>
  <el-dialog
    title="查看权益描述"
    :visible.sync="dialogVisible"
    width="900px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    :append-to-body="true"
    :modal-append-to-body="true"
    custom-class="benefits-view-dialog"
  >
    <div class="benefits-viewer">
      <!-- 会员信息 -->
      <div v-if="data" class="member-info">
        <h3>{{ data.name }} - 权益描述</h3>
        <p class="member-details">
          价格：￥{{ data.money }} | 天数：{{ data.day }}天 | 
          状态：{{ data.is_display == 1 ? '显示' : '隐藏' }}
        </p>
      </div>

      <!-- 权益列表 -->
      <div v-if="benefitsList.length > 0" class="benefits-list">
        <div 
          v-for="(item, index) in benefitsList" 
          :key="index"
          class="benefit-item"
        >
          <div class="benefit-header">
            <span class="benefit-index">权益 {{ index + 1 }}</span>
            <div class="benefit-actions">
              <el-button 
                type="primary" 
                size="mini" 
                icon="el-icon-edit"
                @click="editBenefit(index)"
              >
                编辑
              </el-button>
              <el-button 
                type="danger" 
                size="mini" 
                icon="el-icon-delete"
                @click="deleteBenefit(index)"
              >
                删除
              </el-button>
            </div>
          </div>
          
          <div class="benefit-content">
            <div class="benefit-image" v-if="item.img_url">
              <img :src="item.img_url" alt="权益图片" class="preview-img" />
            </div>
            <div class="benefit-text">
              <h4 class="benefit-title">{{ item.title || '未设置标题' }}</h4>
              <p class="benefit-desc">{{ item.desc || '未设置描述' }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="empty-state">
        <el-empty description="暂无权益描述">
          <el-button type="primary" @click="addBenefit">添加权益</el-button>
        </el-empty>
      </div>
      
      <!-- 添加按钮 -->
      <div v-if="benefitsList.length > 0" class="add-section">
        <el-button 
          type="primary" 
          icon="el-icon-plus"
          @click="addBenefit"
        >
          添加权益
        </el-button>
      </div>
    </div>
    
    <!-- 弹窗底部按钮 -->
    <template v-slot:footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="saveBenefits">保存修改</el-button>
    </template>
    
    <!-- 权益编辑弹窗 -->
    <member-benefits-edit
      :visible.sync="showBenefitsEdit"
      :value="editingBenefits"
      @confirm="onBenefitsConfirm"
    />
  </el-dialog>
</template>

<script>
import MemberBenefitsEdit from './member-benefits-edit';
import { update } from '@/api/member/list';

export default {
  name: 'MemberBenefitsView',
  components: {
    MemberBenefitsEdit
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 会员数据
    data: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      benefitsList: [],
      originalBenefitsList: [], // 保存原始数据，用于取消时恢复
      showBenefitsEdit: false,
      editingBenefits: []
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.initBenefitsList();
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    }
  },
  methods: {
    // 初始化权益列表
    initBenefitsList() {
      if (!this.data || !this.data.member_info) {
        this.benefitsList = [];
        this.originalBenefitsList = [];
        return;
      }

      try {
        const memberInfo = this.data.member_info;
        if (typeof memberInfo === 'string') {
          this.benefitsList = memberInfo ? JSON.parse(memberInfo) : [];
        } else if (Array.isArray(memberInfo)) {
          this.benefitsList = [...memberInfo];
        } else {
          this.benefitsList = [];
        }
        
        // 保存原始数据
        this.originalBenefitsList = JSON.parse(JSON.stringify(this.benefitsList));
        
        // 确保每个权益项都有必要的字段
        this.benefitsList = this.benefitsList.map(item => ({
          title: item.title || '',
          img_url: item.img_url || '',
          desc: item.desc || ''
        }));
        
      } catch (e) {
        console.error('解析权益描述数据失败:', e);
        this.benefitsList = [];
        this.originalBenefitsList = [];
      }
    },
    
    // 添加权益
    addBenefit() {
      this.editingBenefits = JSON.stringify(this.benefitsList);
      this.showBenefitsEdit = true;
    },
    
    // 编辑权益
    editBenefit(index) {
      this.editingBenefits = JSON.stringify(this.benefitsList);
      this.showBenefitsEdit = true;
    },
    
    // 删除权益
    deleteBenefit(index) {
      this.$confirm('确定要删除这个权益描述吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.benefitsList.splice(index, 1);
      }).catch(() => {});
    },
    
    // 权益编辑确认回调
    onBenefitsConfirm(benefits) {
      this.benefitsList = benefits;
    },
    
    // 保存权益修改
    saveBenefits() {
      if (!this.data) return;
      
      const loading = this.$loading({ lock: true });
      const memberInfo = JSON.stringify(this.benefitsList);
      
      update({
        id: this.data.id,
        member_info: memberInfo,
        // 保持其他字段不变
        name: this.data.name,
        price: this.data.price,
        money: this.data.money,
        day: this.data.day,
        second_infinite: this.data.second_infinite,
        second: this.data.second,
        voice_twin_count: this.data.voice_twin_count,
        high_fidelity_words_number: this.data.high_fidelity_words_number,
        ai_copywriting_times: this.data.ai_copywriting_times,
        ai_title_times: this.data.ai_title_times,
        xunfei_sound_clone_words_number: this.data.xunfei_sound_clone_words_number,
        xunfei_fidelity_words_number: this.data.xunfei_fidelity_words_number,
        sort: this.data.sort,
        is_display: this.data.is_display,
        is_recommend: this.data.is_recommend
      }).then((msg) => {
        loading.close();
        this.$message.success(msg);
        this.$emit('done');
        this.handleClose();
      }).catch((e) => {
        loading.close();
        this.$message.error(e.message);
      });
    },
    
    // 关闭弹窗
    handleClose() {
      // 恢复原始数据
      this.benefitsList = JSON.parse(JSON.stringify(this.originalBenefitsList));
      this.dialogVisible = false;
    }
  }
};
</script>

<style scoped>
.benefits-viewer {
  max-height: 600px;
  overflow-y: auto;
}

.member-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.member-info h3 {
  margin: 0 0 10px 0;
  color: #409eff;
  font-size: 18px;
}

.member-details {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.benefits-list {
  margin-bottom: 20px;
}

.benefit-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: #fafafa;
}

.benefit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.benefit-index {
  font-weight: bold;
  color: #409eff;
  font-size: 16px;
}

.benefit-actions {
  display: flex;
  gap: 8px;
}

.benefit-content {
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.benefit-image {
  flex-shrink: 0;
}

.preview-img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.benefit-text {
  flex: 1;
}

.benefit-title {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 16px;
  font-weight: bold;
}

.benefit-desc {
  margin: 0;
  color: #606266;
  line-height: 1.6;
  word-break: break-word;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.add-section {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #e4e7ed;
}

/* 确保查看弹窗层级正确 */
::v-deep .benefits-view-dialog {
  z-index: 2000 !important;
}

::v-deep .benefits-view-dialog .el-dialog__wrapper {
  z-index: 2000 !important;
}
</style>
