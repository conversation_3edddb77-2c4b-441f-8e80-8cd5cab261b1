<template>
  <ele-table-select
    ref="select"
    clearable
    size="small"
    :disabled="disabled"
    :v-model="value"
    placeholder="请选择"
    value-key="url"
    label-key="name"
    prefix-icon="el-icon-search"
    :table-config="tableConfig"
    @input="updateValue"
  >
    <template v-slot:toolbar>
      <div style="max-width: 200px">
        <el-input
          clearable
          size="small"
          v-model="where.keywords"
          placeholder="输入关键字搜索"
          prefix-icon="el-icon-search"
          @change="search"
        />
      </div>
    </template>
  </ele-table-select>
</template>

<script>
  import { getPages } from '@/api/layout';
  export default {
    name: 'Pages',
    props: {
      // 修改回显的数据
      value: '',
      disabled:Boolean
    },
    data() {
      return {
        // 搜索表单
        where: {
          keywords: ''
        },
        tableConfig: {
          datasource: ({ page, limit, where, order }) => {
            return getPages({ ...where, ...order, page, limit });
          },
          columns: [
            {
                columnKey: 'index',
                type: 'index',
                width: 45,
                align: 'center'
            },
            {
              prop: 'name',
              label: '名称',
              showOverflowTooltip: true,
              minWidth: 110,
              //slot: 'nickname'
            },
            {
              prop: 'url',
              label: '路径',
              showOverflowTooltip: true,
              minWidth: 110
            }
          ],
          rowClickChecked: true,
          rowClickCheckedIntelligent: false,
          toolkit: ['reload', 'columns'],
          size: 'small',
          toolStyle: { padding: '0 10px' }
        }
      };
    },
    methods: {
      updateValue(value) {
        this.$emit('input', value);
      },
      // 搜索
      search(where) {
        this.$refs.select.reload({
          where: where,
          page: 1
        });
      }
    }
  };
</script>
