<template>
    <el-card :bordered="false" dis-hover class="ivu-mt">
      <el-row :gutter="15">
        <el-col :md="24" :sm="12">
          <div class="row-between-wrapper mb20" style="justify-content: space-between;-webkit-box-pack: justify;display: flex;">
            <div class="header-title">点数记录汇总</div>
            <div class="acea-row" style="float: right;">
                <el-date-picker
                style="width: 400px!important;margin-right: 10px;"
                  v-model="timeVal"
                  type="daterange"
                  :picker-options="options"
                  unlink-panels
                  range-separator="~"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  class="ele-fluid"
                />
                <el-button type="primary" class="mr20" @click="onSeach">查询</el-button>
            </div>
          </div>
          <div class="acea-row mb20 flex-wrp">
            <div
              class="infoBox acea-row mb30 flex-aling-center"
              v-for="(item, index) in list"
              :key="index"
            >
              <div
                class="iconCrl mr15"
                :class="{
                  one: index % 4 == 0,
                  two: index % 4 == 1,
                  three: index % 4 == 2,
                  four: index % 4 == 3,
                }"
              >
                <i class="iconfont" :class="item.icon"></i>
              </div>
              <div class="info">
                <span class="sp1" v-text="item.name"></span>
                <span class="sp2" v-text="item.money"></span>
                <span class="content-time spBlock"
                  >环比增长：<i
                    class="content-is"
                    :class="Number(item.rate) >= 0 ? 'up' : 'down'"
                    >{{ item.rate }}%</i
                  ><el-icon
                    :color="Number(item.rate) >= 0 ? '#F5222D' : '#39C15B'"
                    :type="
                      Number(item.rate) >= 0 ? 'md-arrow-dropup' : 'md-arrow-dropdown'
                    "
                /></span>
              </div>
            </div>
          </div>
          <echarts-new
            :option-data="optionData"
            :styles="style"
            height="100%"
            width="100%"
            v-if="optionData"
          ></echarts-new>
          <Spin size="large" fix v-if="spinShow"></Spin>
        </el-col>
      </el-row>
      
    </el-card>
    
  </template>
  
  <script>
  import { getIndexChartData} from '@/api/statis';
  import echartsNew from "@/components/echartsNew/index";
  import { Message, Spin, Notice ,Poptip } from 'iview'
  export default {
    name: "StatisGoods",
    components: {
      echartsNew,
      Spin,
      Poptip
    },
    data() {
      return {
        grid: {
          xl: 8,
          lg: 8,
          md: 8,
          sm: 24,
          xs: 24,
        },
        options: this.$timeOptions,
        name: "近30天",
        timeVal: [],
        dataTime: "",
        list: {},
        optionData: {},
        style: { height: "400px" },
        getExcel: "",
        spinShow: false,
      };
    },
    created() {
      const end = new Date();
      const start = new Date();
      start.setTime(
        start.setTime(
          new Date(
            new Date().getFullYear(),
            new Date().getMonth(),
            new Date().getDate() - 29
          )
        )
      );
      this.timeVal = [start, end];
  
      this.dataTime = this.$util.toDateString(start) + "~" +  this.$util.toDateString(end);
  
    },
    mounted() {
      this.getStatistics();
    },
    methods: {
      onSeach() {
        this.getStatistics();
      },
      // 具体日期
      onchangeTime(e) {
        
        this.timeVal = e;
        this.dataTime = this.timeVal.join("~");
        this.name = this.dataTime;
      },
      // 统计
      getStatistics() {

        this.spinShow = true;
        getIndexChartData({ conTime: this.timeVal })
          .then(async (res) => {
            const cardLists = res;
            const incons = [
              "el-icon-_user-group-solid",
              "el-icon-_money-solid",
              "el-icon-_integral-solid",
             
              "el-icon-_red-packet-solid"
            ];
            for (var i = 0; i < cardLists.series.length; i++) {
              this.$set(cardLists.series[i], "icon", incons[i]);
            }
            this.list = cardLists.series;
            this.getExcel = cardLists.export;
            this.get(cardLists);
            this.spinShow = false;
          })
          .catch((res) => {
            this.$message.error(res.message);
            this.spinShow = false;
          });
      },
      get(extract) {
        let dataList = extract.series;
        let legend = dataList.map((item) => {
          return item.name;
        });
        
        let col = [
          "#5B8FF9",
          "#5AD8A6",
          "#5D7092",
          "#F5222D",
          "#FFAB2B",
          "#B37FEB",
        ];
        let seriesData = [];
        dataList.map((item, index) => {
          let series = [];
          Object.keys(item.value).forEach((key) => {
            series.push(Number(item.value[key]));
          });
  
          if(item.type == 1){
            seriesData.push({
              name: item.name,
              type: "line",
              data: series,
              itemStyle: {
                normal: {
                  color: col[index],
                },
              },
              smooth: true,
              symbol: 'none',
              areaStyle: {
                opacity: 0.5
              },
            });
          }else if(item.type == 2){
            seriesData.push({
              name: item.name,
              type: "bar",
              data: series,
              itemStyle: {
                normal: {
                  color: col[index],
                },
              },
              smooth: true,
              symbol: 'none',
              areaStyle: {
                opacity: 0.5
              },
            });
          }
  
          
        });
        this.optionData = {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross",
              label: {
                backgroundColor: "#6a7985",
              },
            },
          },
          legend: {
            x: "center",
            data: legend,
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          toolbox: {
            feature: {
              saveAsImage: {},
            },
          },
          xAxis: {
            type: "category",
            boundaryGap: true,
            axisLabel: {
              interval: 0,
              rotate: 40,
              textStyle: {
                color: "#000000",
              },
            },
            data: extract.day,
          },
          yAxis: {
            type: "value",
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              textStyle: {
                color: "#7F8B9C",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#F5F7F9",
              },
            },
          },
          series: seriesData,
        };
      },
      excel() {
        window.location.href = this.getExcel;
      },
    },
  };
  </script>
  
  <style scoped lang="less">
  .one {
    background: #1890ff;
  }
  .two {
    background: #00c050;
  }
  .three {
    background: #ffab2b;
  }
  .four {
    background: #b37feb;
  }
  .up,
  .el-icon-caret-top {
    color: #f5222d;
    font-size: 12px;
    opacity: 1 !important;
  }
  
  .down,
  .el-icon-caret-bottom {
    color: #39c15b;
    font-size: 12px;
  }
  .curP {
    cursor: pointer;
  }
  .header {
    &-title {
      font-size: 20px;
    }
    &-time {
      font-size: 12px;
      opacity: 0.45;
    }
  }
  
  
  .iconfont {
    font-size: 16px;
  }
  
  .iconCrl {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    text-align: center;
    line-height: 32px;
    opacity: 0.7;
    color: white;
  }
  
  .lan {
    background: #1890ff;
  }
  
  .iconshangpinliulanliang {
    color: #fff;
  }
  
  .infoBox {
    width: 20%;
    @media screen and (max-width: 1300px) {
      width: 25%;
    }
    @media screen and (max-width: 1200px) {
      width: 33%;
    }
    @media screen and (max-width: 900px) {
      width: 50%;
    }
  }
  .flex-aling-center{
    display: flex;
    align-items: center;
  }
  .flex-wrp{
    display: flex;
    flex-wrap: wrap;
  }
  .flex-end{
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }
  .acea-row{
    -webkit-box-lines: multiple;
    -moz-box-lines: multiple;
    -o-box-lines: multiple;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  
  }
  .mr15{
    margin-right: 15px!important;
  }
  
  .mb20{
    margin-bottom: 20px!important;
  }
  .mb30{
    margin-bottom: 30px!important;
  }
  
  .info {
    .sp1 {
      color: #666;
      font-size: 14px;
      display: block;
    }
    .sp2 {
      font-weight: 400;
      font-size: 30px;
      display: block;
    }
    .sp3 {
      font-size: 12px;
      font-weight: 400;
      display: block;
    }
    .acea-row.row-between-wrapper {
      -webkit-box-pack: justify;
      -o-box-pack: justify;
      -ms-flex-pack: justify;
      justify-content: space-between;
    }
  
  .row-between-wrapper {
    -webkit-box-pack: justify;
    -o-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }
  
  
  }
  </style>