<!-- 统计卡片 -->
<template>
  <el-row :gutter="15">
    <el-col v-bind="styleResponsive ? { lg: 6, md: 12 } : { span: 6 }">
      <el-card class="analysis-chart-card" shadow="never">
        <template v-slot:header>
          <div class="ele-cell">
            <div class="ele-cell-content">总销售额</div>
            <el-tooltip content="会员实付金额 + 合伙人申请金额 + 卡密实付金额" placement="top">
              <i
                class="el-icon-_question ele-text-placeholder"
                style="cursor: pointer"
              >
              </i>
            </el-tooltip>
          </div>
        </template>
        <div class="analysis-chart-card-num ele-text-heading">¥ {{ saleData.sale_money }}</div>
        <div class="analysis-chart-card-content" style="padding-top: 18px">
          <span class="ele-action">
            <span>周同比{{ saleData.sale_week_rate }}%</span>
            <i :class="Number(saleData.sale_week_rate) >= 0 ? 'el-icon-caret-top ele-text-danger' : 'el-icon-caret-bottom ele-text-success'"></i>
          </span>
          <span class="ele-action">
            <span>日同比{{ saleData.sale_day_rate }}%</span>
            <i :class="Number(saleData.sale_day_rate) >= 0 ? 'el-icon-caret-top ele-text-danger' : 'el-icon-caret-bottom ele-text-success'"></i>
          </span>
        </div>
        <el-divider />
        <div class="total">
          <el-row>
            <el-col :span="12">
              日销售额
            </el-col>
            <el-col :span="12" class="ivu-text-right">
              ¥ {{ saleData.day_sale_money }}
            </el-col>  
          </el-row>
        </div>
      </el-card>
    </el-col>
    <el-col v-bind="styleResponsive ? { lg: 6, md: 12 } : { span: 6 }">
      <el-card class="analysis-chart-card" shadow="never">
        <template v-slot:header>
          <div class="ele-cell">
            <div class="ele-cell-content">合伙人数量</div>
            <el-tag size="mini" type="primary" >今日</el-tag>
          </div>
        </template>
        <div class="analysis-chart-card-num ele-text-heading">{{ saleData.day_partner_count}}</div>
        <div class="analysis-chart-card-content" style="padding-top: 18px">
          <span class="ele-action">
            <span>昨日 {{ saleData.lastday_partner_count }} </span>
          </span>
          <span class="ele-action">
            <span>日环比{{ saleData.partner_count_rate }}%</span>
            <i :class="Number(saleData.partner_count_rate) >= 0 ? 'el-icon-caret-top ele-text-danger' : 'el-icon-caret-bottom ele-text-success'"></i>
          </span>
        </div>
        <el-divider />
        <div class="total">
          <el-row>
            <el-col :span="12">
              本月合伙人数量
            </el-col>
            <el-col :span="12" class="ivu-text-right">
              {{saleData.month_partner_count}}人
            </el-col>  
          </el-row>
        </div>
      </el-card>
    </el-col>
    <el-col v-bind="styleResponsive ? { lg: 6, md: 12 } : { span: 6 }">
      <el-card class="analysis-chart-card" shadow="never">
        <template v-slot:header>
          <div class="ele-cell">
            <div class="ele-cell-content">充值笔数</div>
            <el-tag size="mini" type="primary" >今日</el-tag>
          </div>
        </template>
        <div class="analysis-chart-card-num ele-text-heading">{{ saleData.day_recharge_count}}</div>
        <div class="analysis-chart-card-content" style="padding-top: 18px">
          <span class="ele-action">
            <span>昨日 {{ saleData.lastday_recharge_count }} </span>
          </span>
          <span class="ele-action">
            <span>日环比{{ saleData.recharge_count_rate }}%</span>
            <i :class="Number(saleData.recharge_count_rate) >= 0 ? 'el-icon-caret-top ele-text-danger' : 'el-icon-caret-bottom ele-text-success'"></i>
          </span>
        </div>
        <el-divider />
        <div class="total">
          <el-row>
            <el-col :span="12">
              本月充值笔数
            </el-col>
            <el-col :span="12" class="ivu-text-right">
              {{saleData.month_recharge_count}}笔
            </el-col>  
          </el-row>
        </div>
      </el-card>
    </el-col>
    <el-col :lg="6" :md="12">
      <el-card class="analysis-chart-card" shadow="never">
        <template v-slot:header>
          <div class="ele-cell">
            <div class="ele-cell-content">新增用户</div>
            <el-tag size="mini" type="primary" >今日</el-tag>
          </div>
        </template>
        <div class="analysis-chart-card-num ele-text-heading">{{ saleData.day_u_count}}</div>
        <div class="analysis-chart-card-content" style="padding-top: 18px">
          <span class="ele-action">
            <span>昨日 {{ saleData.lastday_u_count }} </span>
          </span>
          <span class="ele-action">
            <span>日环比{{ saleData.user_count_rate }}%</span>
            <i :class="Number(saleData.user_count_rate) >= 0 ? 'el-icon-caret-top ele-text-danger' : 'el-icon-caret-bottom ele-text-success'"></i>
          </span>
        </div>
        <el-divider />
        <div class="total">
          <el-row>
            <el-col :span="12">
              本月新增用户
            </el-col>
            <el-col :span="12" class="ivu-text-right">
              {{saleData.month_u_count}}人
            </el-col>  
          </el-row>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { LineChart, BarChart } from 'echarts/charts';
  import { GridComponent, TooltipComponent } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { getAdminSaleData } from '@/api/dashboard/analysis';
  import { echartsMixin } from '@/utils/echarts-mixin';

  
 

  use([CanvasRenderer, LineChart, BarChart, GridComponent, TooltipComponent]);

  export default {
    name: 'StatisticsCard',
    components: { VChart },
    mixins: [echartsMixin(['visitChart', 'payNumChart'])],
    data() {
      return {
        // 访问量折线图配置
        visitChartOption: {},
        // 支付笔数柱状图配置
        payNumChartOption: {},
        saleData:{
          sale_money:0,
          sale_week_rate:0,
          sale_day_rate:0,
          day_sale_money:0,

          u_count:0,
          day_u_count:0,
          lastday_u_count:0,
          user_count_rate:0,
          month_u_count:0,

          partner_count:0,
          day_partner_count:0,
          lastday_partner_count:0,
          partner_count_rate:0,
          month_partner_count:0,


          recharge_count:0,
          day_recharge_count:0,
          lastday_recharge_count:0,
          recharge_count_rate:0,
          month_recharge_count:0,
          
        },
        payCount:0,
        videoData:{
          count:0,
          d_count:0
        },
        mealData:{
          sum_price:0,
          cur_count:0,
          sum_count:0
        }

      };
    },
    computed: {
      // 是否开启响应式布局
      styleResponsive() {
        return this.$store.state.theme.styleResponsive;
      }
    },
    created() {
      this.getPayNumData();
    },
    methods: {
      /* 获取数据 */
      getPayNumData() {
        
        getAdminSaleData()
          .then((data) => {
             this.saleData = data;

             this.visitChartOption = {
              color: '#975fe5',
              tooltip: {
                trigger: 'axis',
                formatter:
                  '<i class="ele-chart-dot" style="background: #975fe5;"></i>{b0}: {c0}'
              },
              grid: {
                top: 10,
                bottom: 0,
                left: 0,
                right: 0
              },
              xAxis: [
                {
                  show: false,
                  type: 'category',
                  boundaryGap: false,
                  data: data.date
                }
              ],
              yAxis: [
                {
                  show: false,
                  type: 'value',
                  splitLine: {
                    show: false
                  }
                }
              ],
              series: [
                {
                  type: 'line',
                  smooth: true,
                  symbol: 'none',
                  areaStyle: {
                    opacity: 0.5
                  },
                  data: data.client_value
                }
              ]
            };

            this.payNumChartOption = {
              tooltip: {
                trigger: 'axis',
                formatter:
                  '<i class="ele-chart-dot" style="background: #5b8ff9;"></i>{b0}: {c0}'
              },
              grid: {
                top: 10,
                bottom: 0,
                left: 0,
                right: 0
              },
              xAxis: [
                {
                  show: false,
                  type: 'category',
                  data: data.month
                }
              ],
              yAxis: [
                {
                  show: false,
                  type: 'value',
                  splitLine: {
                    show: false
                  }
                }
              ],
              series: [
                {
                  type: 'bar',
                  data:data.brokerage_value
                }
              ]
            };
        })
        .catch((e) => {
          this.$message.error(e.message);
        });

      }
    }
  };
</script>

<style lang="less" scoped>

  /deep/ .el-divider--horizontal {
    margin: 0 0 12px 0;
  }

  .total {
      font-size: 14px;
      font-weight: 400;
      color: #999;
      line-height: 22px;
  }
  .ivu-text-right {
    text-align: right;
  }
  .analysis-chart-card-num {
    font-size: 30px;
  }

  .analysis-chart-card-content {
    height: 40px;
    box-sizing: border-box;
    margin-bottom: 12px;
  }

  .analysis-chart-card-text {
    padding-top: 12px;
  }
</style>
