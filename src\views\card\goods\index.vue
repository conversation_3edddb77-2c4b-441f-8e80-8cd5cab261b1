<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <goods-search @search="reload"/>
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            新增
          </el-button> 
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            class="ele-btn-icon"
            @click="removeBatch"
          >
            删除
          </el-button>
          
        </template>

        <template v-slot:name="{ row }">
          <div class="ele-cell">
            <el-avatar :src="row.pic_url" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.name }}</div>
            </div>
          </div>
        </template>
         <template v-slot:card_type="{ row }">
             <el-tag v-if="row.card_type==1" type="warning" effect="plain">合伙人购买</el-tag>
          <el-tag v-if="row.card_type==2" type="success" effect="plain">用户使用</el-tag>
        </template>


        <template v-slot:day="{ row }">
          <div>天数：{{row.day}}</div>
          <div>视频合成秒数：{{row.second_infinite == 0 ? row.second : '无限'}}</div>
          <div>ai文案次数：{{row.ai_copywriting_times}}</div>
          <div>声音高保真次数：{{row.voice_twin_count}}</div>
          <div>声音高保真合成字数：{{row.high_fidelity_words_number}}</div>
          
          <div>声音专业版次数：{{row.xunfei_sound_clone_words_number}}</div>
          <div>声音专业版字数：{{row.xunfei_sound_generate_words_number}}</div>
          <div>ai标题：{{row.ai_title_times}}</div>
        </template>

       

        <!-- 状态列 -->
        <template v-slot:is_display="{ row }">
          <el-switch
            :active-value="1"
            :inactive-value="2"
            v-model="row.is_display"
            @change="editStatus(row)"
          />
        </template>


        <template v-slot:sort>
          <i class="sort-handle el-icon-rank ele-text-secondary" style="cursor: move;"></i>
        </template>

        
        <!-- 操作列 -->
        <template v-slot:action="{ row }">
            <el-tag v-if="row.card_type==1" ><el-link
            type="primary"
            :underline="false"
            icon="el-icon-edit"
            @click="openEdit(row)"
          >
            修改
          </el-link></el-tag>
       
          
          <el-popconfirm
            class="ele-action"
            title="确定要删除此数据吗？"
            @confirm="remove(row)"
          >
            <template v-slot:reference>
              <el-link type="danger" :underline="false" icon="el-icon-delete">
                删除
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
    <!-- 编辑弹窗 -->
    <goods-edit :visible.sync="showEdit" :data="current"  @done="reload"/>
  </div>
</template>

<script>
  import GoodsSearch from './components/goods-search';
  import GoodsEdit from './components/goods-edit';
  import { list, remove, modify,sortChange  } from '@/api/card/goods';
  import Sortable from 'sortablejs';

  export default {
    name: 'Goods',
    components: {
      GoodsSearch,
      GoodsEdit
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },
          {
            prop: 'sort',
            label: '排序',
            sortable: 'custom',
            showOverflowTooltip: true,
            minWidth: 60,
            slot: 'sort'
          },
          {
            prop: 'name',
            label: '商品',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'name'
          },
            {
            prop: 'card_type',
            label: '卡密类型',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110,
              slot: 'card_type'
            
          },
          {
            prop: 'money',
            label: '价格',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'day',
            label: '会员天数',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 180,
            slot: 'day'
          },
          {
            prop: 'point',
            label: '赠送点数',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 150,
          },
          {
            prop: 'quantity',
            label: '卡密数量',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 90
          },
          {
            prop: 'inventory',
            label: '库存',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 90
          },
          {
            prop: 'sold',
            label: '已售',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 90
          },
          {
            prop: 'is_display',
            label: '展示状态',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'is_display'
          },
          {
            prop: 'create_time',
            label: '创建时间',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 100,
            formatter: (row, column, cellValue) => {
              return this.$util.toDateString(cellValue);
            }
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 250,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            showOverflowTooltip: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示分配回收员
        showCollector: false,
        //弹框名称
        id:0,
      };
    },
    methods: {
      rowDrop() {
        const tbody = document.querySelector('.el-table__body-wrapper tbody')
        const _this = this
        Sortable.create(tbody, {
          forceFallback: true,
          dragClass: 'drag',
          delay:100,
          animation: 1000,
          onEnd({ newIndex, oldIndex }) {
            console.log(newIndex);
            console.log(oldIndex);
            //更换表格数据 后续拖动坐标才会正确
            let data = _this.$refs.table.getData();
             
            let nid = data[newIndex].id;
            let oid = data[oldIndex].id;

            console.log(nid);
            console.log(oid);
            
            const currRow = data.splice(oldIndex, 1)[0];
            data.splice(newIndex, 0, currRow);

            sortChange(data).then((msg) => {
              
            }).catch((e) => {
              _this.$refs.table.reload();
            });
            //去数据库更换排序
            console.log(_this.$refs.table.getData());
          }
        })
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return list({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id).then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
      },
    
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的数据吗?', '提示', {
          type: 'warning'
        }).then(() => {
          const loading = this.$loading({ lock: true });
          remove(this.selection.map((d) => d.id))
            .then((msg) => {
              loading.close();
              this.$message.success(msg);
              this.reload();
            })
            .catch((e) => {
              loading.close();
              this.$message.error(e.message);
            });
        }).catch(() => {});
      },
      
      /* 更改状态 */
      editStatus(row) {
        const loading = this.$loading({ lock: true });
        modify({id:row.id,field:'is_display',value: row.is_display})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
          })
          .catch((e) => {
            loading.close();
            row.status = !row.status ? 1 : 0;
            this.$message.error(e.message);
          });
      }
    },mounted() {
      this.rowDrop()
      //this.columnDrop()
    }
  };
</script>

<style>
 .drag {
    background: #000 !important;
    background-image: linear-gradient(#333, #999) !important;
  }
  .ele-cell-content {
   
   overflow: hidden;
   display: -webkit-box;
   -webkit-line-clamp: 2;
   -webkit-box-orient: vertical;
 }
</style>
