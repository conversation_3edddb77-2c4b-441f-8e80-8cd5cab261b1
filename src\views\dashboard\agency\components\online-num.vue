<template>
  <el-card shadow="never" header="代理新增商户排行榜">
    <div style="padding: 10px 5px 0 0;height: 435px;">
          <div
            v-for="(item, index) in userCountDataRank"
            :key="index"
            class="monitor-user-count-item ele-cell"
            style="margin-top: 10px;"
          >
            <div class="el-tooltip" style="width: 50px;">{{ item.name }}</div>
            <div class="ele-cell-content">
              <el-progress
                :stroke-width="10"
                :show-text="false"
                :percentage="item.percent"
              />
            </div>
            <div>{{ item.value }}</div>
          </div>
        </div>
  </el-card>
</template>

<script>


import {  getOemShRank}  from '@/api/common/set';
  import VueCountUp from 'vue-countup-v2';

  export default {
    name: 'OnlineNum',
    components: { VueCountUp },
    data() {
      return {
        // 在线总人数倒计时
        updateTime: 10,
        // 当前时间
        currentTime: '20:58:22',
        // 在线人数
        onlineNum: 228,
        // 在线人数更新定时器
        onlineNumTimer: null,
        userCountDataRank:[]
      };
    },
    computed: {
      // 在线人数倒计时文字
      updateTimeText() {
        return this.updateTime + ' 秒后更新';
      }
    },
    created() {
      this.startUpdateOnlineNum();
    },
    methods: {

      

      /* 在线人数更新倒计时 */
      startUpdateOnlineNum() {
        getOemShRank()
          .then((data) => {
            if(data && data.length>0){

                const temp = data.sort((a, b) => b.value - a.value);
                const min = temp[temp.length - 1].value || 0;
                const max = temp[0].value || 1;
            this.userCountDataRank = temp.map((d) => {
                  return {
                    name: d.nickname,
                    value: d.value,
                    percent: (d.value / max) * 100
                  };
                });
            }
          })
          .catch((e) => {
            this.$message.error(e.message);
          });
     
      }
    }
  };
</script>

<style scoped>
  .monitor-online-num-card {
    text-align: center;
    padding: 5px 0;
  }

  .monitor-online-num-text {
    margin-bottom: 5px;
  }

  .monitor-online-num-title {
    font-size: 48px;
    margin-bottom: 10px;
  }

  @media screen and (max-width: 1200px) {
    .monitor-online-num-card {
      padding: 42px 0;
    }
  }
</style>
