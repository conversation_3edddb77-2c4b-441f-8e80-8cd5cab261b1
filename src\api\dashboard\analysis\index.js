import axios from '@/utils/request';




/**
 * 获取管理员工作台数据
 */
 export async function getAdminSaleData() {
  const res = await axios.get('/system.user/getAdminSaleData');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 获取代理工作台数据
 */
 export async function getAdminChartData(params) {
  const res = await axios.get('/system.user/getAdminChartData',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 获取代理工作台数据
 */
 export async function getAgencySaleData() {
  const res = await axios.get('/system.user/getAgencySaleData');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取代理工作台数据
 */
 export async function getAgencyChartData(params) {
  const res = await axios.get('/system.user/getAgencyChartData',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 获取代理工作台数据
 */
 export async function getPartnerSaleData() {
  const res = await axios.get('/system.user/getPartnerSaleData');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取代理工作台数据
 */
 export async function getPartnerChartData(params) {
  const res = await axios.get('/system.user/getPartnerChartData',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

