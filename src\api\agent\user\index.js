import axios from '@/utils/request';


/**
 * 查询代理系统用户列表
 * @param data 配置数据
 */
/**
 * 分页查询代理系统用户列表
 * @param data 配置数据
 */
export async function userList(params) {
    const res = await axios.post('/figure.agent/index', params);
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 删除
 * @param id id
 */
export async function remove(id) {
    const res = await axios.get('/figure.agent/delete?id=' + id);
    if (res.data.code === 0) {
      return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 属性修改
 * @param params nid oid 两个排序的id
 */
export async function modify(data) {
    const res = await axios.post('/figure.agent/modify', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 重置密码
 * @param params nid oid 两个排序的id
 */
export async function resetPassword(id) {
    const res = await axios.post('/figure.agent/resetPassword?id=' + id);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}




