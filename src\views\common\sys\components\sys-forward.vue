<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">转发配置</div>
      <div class="ele-page-desc">
        用于视频转发等场景。
      </div>
    </div>
    <div class="ele-body"  style="padding-bottom: 71px;height:500px;">
      <el-card shadow="never">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="130px"
          @keyup.enter.native="submit"
          @submit.native.prevent
        >
          <el-row :gutter="20"  style="margin-bottom: 20px;">
            <el-col :sm="12">
              <el-alert type="info" :closable="false" class="ele-alert-border">
                <div class="ele-cell">
                  <div class="ele-cell-content">
                    <div class="ele-cell-title"> <h5 style="margin: 5px 0 15px 0">D音应用平台配置 Webhooks</h5> </div>
                    <div class="ele-cell-desc"> <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/hooksNotify'}}  <el-link type="success" @click="copy_url(host +'/mobile/timer/hooksNotify')">复制</el-link></h6></div>
                  </div>
                </div>
              </el-alert>
            </el-col>
            <!-- <el-col :sm="12">
              <el-alert type="info" :closable="false" class="ele-alert-border">
                <div class="ele-cell">
                  <div class="ele-cell-content">
                    <div class="ele-cell-title"> <h5 style="margin: 5px 0 15px 0">D音应用平台配置 扫码授权重定向URL</h5> </div>
                    <div class="ele-cell-desc"> <h6 style="margin-bottom: 10px">{{host +'/mobile/index/dy_index'}}  <el-link type="success" @click="copy_url(host +'/mobile/index/dy_index')">复制</el-link></h6></div>
                  </div>
                </div>
              </el-alert>
            </el-col> -->
          </el-row>
          <el-row :gutter="15">
            <el-col :sm="12">
              <el-card shadow="never" header="D音配置" body-style="padding: 22px 22px 0 22px;">
                <el-form-item label="D音Key:"  prop="d_key">
                  <el-input v-model="form.d_key" placeholder="请输入D音Key" clearable></el-input>
                </el-form-item>
                <el-form-item label="D音Secret:"  prop="d_secret">
                  <el-input v-model="form.d_secret" placeholder="请输入D音Secret" type="password" show-password clearable></el-input>
                </el-form-item>
                <!-- <el-form-item label="D音数据权限:" prop="d_video_data">
                  <el-radio-group v-model="form.d_video_data">
                    <el-radio label='1' value='1'>有</el-radio>
                    <el-radio label='2' value='2'>没有</el-radio>
                  </el-radio-group>
                </el-form-item> -->
              </el-card>
            </el-col>
            <el-col :sm="12">
              <el-card shadow="never" header="K手配置" body-style="padding: 22px 22px 0 22px;">
                <el-form-item label="K手APPID:"  prop="k_appid">
                  <el-input v-model="form.k_appid" placeholder="请输入K手APPID" clearable></el-input>
                </el-form-item>
                <el-form-item label="K手Secret:"  prop="k_secret">
                  <el-input v-model="form.k_secret" placeholder="请输入K手Secret" type="password" show-password clearable></el-input>
                </el-form-item>
                <el-form-item label="k手小程序挂载权限:" prop="k_xiaochengxu">
                  <el-radio-group v-model="form.k_xiaochengxu">
                    <el-radio label='1' value='1'>有</el-radio>
                    <el-radio label='2' value='2'>没有</el-radio>
                  </el-radio-group>
                </el-form-item>
                
              </el-card>
            </el-col>
           
          </el-row>
          
          <el-row :gutter="15">
            <el-form-item>
              <el-button type="primary" :loading="loading" @click="submit">
                提交
              </el-button>
            </el-form-item>
          </el-row>

          
        </el-form>
      </el-card>
    </div>
  </div>
  
</template>

<script>

  const DEFAULT_FORM = {
    d_key: '',
    d_secret:'',
    d_video_data:'2',
    k_appid:'',
    k_secret:'',
   
  };
  import { save ,query } from '@/api/system/config';
  export default {
    name: 'SysForward',
    components: {  },
    data() {
      return {
        host:window.location.protocol + "//" +location.host,
        // 表单提交状态
        loading: false,
        // 表单数据
        form: {
          ...DEFAULT_FORM
        },
        // 表单验证规则
        rules: {
          d_key: [
            {
              required: true,
              message: '请输入D音Key',
              trigger: 'blur'
            }
          ],
          d_secret: [
            {
              required: true,
              message: '请输入D音Secret',
              trigger: 'blur'
            }
          ],
          k_appid: [
            {
              required: true,
              message: '请输入K手APPID',
              trigger: 'blur'
            }
          ],
          k_secret: [
            {
              required: true,
              message: '请输入K手Secret',
              trigger: 'blur'
            }
          ]
        },
        // 表单验证信息
        validMsg: ''
      };
    },
    mounted() {
      query({group:'forward'}).then((msg) => {
        if(msg != null){
          this.form = msg;
        }
      })
      .catch((e) => {
        this.$message.error(e.message);
      });
     },
    methods: {
      /* 表单提交 */
      submit() {
        this.$refs['form'].validate((valid, obj) => {
          if (valid) {
            this.validMsg = '';
            this.loading = true;
            const data = {
            ...this.form
            };
            save(data).then((msg) => {
              this.loading = false;
              this.$message.success(msg);
            }).catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });
          } else {
            this.validMsg = ` 共有校验 ${Object.keys(obj).length} 项不通过`;
            return false;
          }
        });
      },
      copy_url(content){
       // window.clipboardData.setData('text', context);

        if (window.clipboardData) {
            /*
            window.clipboardData有三个方法:
          （1）clearData(sDataFormat) 删除剪贴板中指定格式的数据。sDataFormat:"text","url"
          （2）getData(sDataFormat) 从剪贴板获取指定格式的数据。 sDataFormat:"text","url"
          （3）setData(sDataFormat, sData) 给剪贴板赋予指定格式的数据。返回 true 表示操作成功。
            */
          window.clipboardData.setData('text', content);
        } else {
          (function (content) {
            //oncopy 事件在用户拷贝元素上的内容时触发。
            document.oncopy = function (e) {
              e.clipboardData.setData('text', content);
              e.preventDefault(); //取消事件的默认动作
              document.oncopy = null;
            }
          })(content);
          //execCommand方法是执行一个对当前文档/当前选择/给出范围的命令。
          //'Copy':将当前选中区复制到剪贴板。 
          document.execCommand('Copy');
        }
        this.$message.success('复制成功！');
      },
    }
  };
</script>
