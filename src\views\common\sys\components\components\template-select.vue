<template>
  <el-select
    clearable
    :value="value"
    class="ele-block"
    :placeholder="placeholder"
    @input="updateValue"
  >
    <el-option
      v-for="item in data"
      :key="item.priTmplId"
      :value="item.priTmplId"
      :label="item.title+'（'+item.priTmplId+'）'"
    />
  </el-select>
</template>

<script>
  export default {
    name: 'TemplateSelect',
    props: {
      value:'',
      // 选中的数据(v-model)
      placeholder: {
        type: String,
        default: '请选择'
      },
      data:Array
    },
    methods: {
      updateValue(value) {
        this.$emit('input', value);
      }
    }
  };
</script>
