<template>
  <ele-table-select
    ref="select"
    :clearable="true"
    size="medium"
    :value="value"
    placeholder="请选择"
    value-key="id"
    label-key="name"
    prefix-icon="el-icon-search"
    :table-config="tableConfig"
    @select="updateValue"
    :popper-width="900"
  >
    <template v-slot:toolbar>
      <div style="max-width: 200px">
        <el-input
          clearable
          size="small"
          v-model="keywords"
          placeholder="输入关键字搜索"
          prefix-icon="el-icon-search"
          @blur="search"
        />
      </div>
    </template>

    <template v-slot:user="{ row }">
      <div class="ele-cell" v-if="row.figureUser">
        <el-avatar v-if="row.figureUser.avatar" :src="row.figureUser.avatar" shape="square" :size="40" />
        <div class="ele-cell-content">
          <div class="ele-cell-title">{{ row.figureUser.nickname }} </div>
          <div class="ele-cell-desc">{{ row.figureUser.telphone }} </div>
        </div>
      </div>
      <span v-else>无</span>
      
    </template>

    <!-- <template v-slot:a_type="{ row }">
      <el-tag v-if="row.a_type==1" type="success" effect="plain">形象克隆</el-tag>
      <el-tag v-if="row.a_type==2" type="success" effect="plain">照片克隆</el-tag>
    </template>


    <template v-slot:type="{ row }">
      <el-tag v-if="row.type==1" type="success" effect="plain">快速模式</el-tag>
      <el-tag v-if="row.type==2" type="primary" effect="plain">高级模式</el-tag>
    </template> -->

    <template v-slot:video_url="{ row }">
    
      <video v-if="row.video_url && !isImageFile(row.video_url)" width="150" height="120" controls :src="row.video_url"  > </video>
      <el-avatar v-else-if="row.video_url && isImageFile(row.video_url)" :src="row.video_url" shape="square" :size="80" />
      <el-avatar v-else-if="row.image_url && isImageFile(row.image_url)" :src="row.image_url" shape="square" :size="80" />
      
    </template>
  
  </ele-table-select>
  
</template>

<script>
  import { list } from '@/api/avatar';
  export default {
    name: 'AvatarSelect',
    props: {
      // 修改回显的数据
      value: undefined
    },
    data() {
      return {
        keywords:'',
        // 搜索表单
        where: {
          keywords: ''
        },
        tableConfig: {
          datasource: ({ page, limit, where, order }) => {
            where.current_status = 'completed';
            where.keywords = this.keywords;
            return list({ ...where, ...order, page, limit });
          },
          columns: [
            {
              prop: 'user',
              label: '用户',
              showOverflowTooltip: true,
              minWidth: 110,
              slot: 'user'
            },
            {
              prop: 'name',
              label: '名称',
              showOverflowTooltip: true,
              minWidth: 100,
            },
            {
              prop: 'a_type',
              label: '类型',
              align: 'center',
              showOverflowTooltip: true,
              minWidth: 90,
              slot: 'a_type'
            },
            {
              prop: 'type',
              label: '模式',
              align: 'center',
              showOverflowTooltip: true,
              minWidth: 90,
              slot: 'type'
            },

            {
              prop: 'video_url',
              label: '链接',
              align: 'center',
              showOverflowTooltip: true,
              minWidth: 220,
              slot: 'video_url'
            },
          ],
          pageSize:5,
          pageSizes:[5,10, 20, 30, 40, 50, 100],
          rowClickChecked: true,
          rowClickCheckedIntelligent: false,
          toolkit: ['reload', 'columns'],
          size: 'large',
          toolStyle: { padding: '0 10px' }
        }
      };
    },
    methods: {
      isImageFile(url){
        // 获取文件扩展名
        const extension = url.substring(url.lastIndexOf('.') + 1).toLowerCase();
        // 支持的图片扩展名列表
        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];
        // 判断当前文件扩展名是否为图片扩展名
        if (imageExtensions.includes(extension)) {
          return true;
        }
        return false;
      },
      reload(){
        this.$refs.select.reload();
      },
      updateValue(data) {
        this.$emit('done', data);
      },
      // 搜索
      search(where) {
        where.current_status = 'completed';
        where.keywords = this.keywords;
        this.$refs.select.reload({
          where: where,
          page: 1
        });
      }
    }
  };
</script>
<style lang="less" scoped>
 
 .drag {
    background: #000 !important;
    background-image: linear-gradient(#333, #999) !important;
  }

  .ele-cell-content {
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

</style>

