{"name": "ele-admin-template", "version": "1.9.3", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:preview": "vue-cli-service build --mode preview", "build:report": "vue-cli-service build --report", "lint": "vue-cli-service lint", "clean:lib": "rimraf node_modules"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/colors": "^6.0.0", "@babel/core": "^7.18.5", "@bytemd/plugin-gfm": "^1.15.0", "@bytemd/vue": "^1.15.0", "@jiaminghi/charts": "^0.2.18", "@jiaminghi/data-view": "^2.10.0", "@tinymce/tinymce-vue": "^3.2.8", "@vue/composition-api": "^1.6.2", "ali-oss": "^6.17.1", "axios": "^0.27.2", "bytemd": "^1.15.0", "core-js": "^3.23.1", "countup.js": "^2.2.0", "cropperjs": "^1.5.12", "echarts": "^5.3.3", "echarts-wordcloud": "^2.0.0", "ele-admin": "^1.10.1", "element-ui": "2.15.7", "github-markdown-css": "^5.1.0", "html2canvas": "^1.4.1", "iview": "^3.5.4", "jsbarcode": "^3.11.5", "node-polyfill-webpack-plugin": "^2.0.1", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "sortablejs": "^1.15.0", "stylus": "^0.59.0", "stylus-loader": "^3.0.2", "tinymce": "^5.10.5", "vue": "^2.6.14", "vue-clipboard2": "^0.3.3", "vue-countup-v2": "^4.0.0", "vue-echarts": "^6.1.0", "vue-i18n": "^8.27.1", "vue-router": "^3.5.4", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "xgplayer-vue": "^1.1.5", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "^5.0.6", "@vue/cli-plugin-eslint": "^5.0.6", "@vue/cli-plugin-router": "^5.0.6", "@vue/cli-plugin-vuex": "^5.0.6", "@vue/cli-service": "^5.0.6", "compression-webpack-plugin": "^9.2.0", "eslint": "^8.18.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^9.1.1", "less": "^4.1.3", "less-loader": "^7.3.0", "prettier": "^2.7.1", "sass": "^1.52.3", "sass-loader": "^13.0.0", "svg-sprite-loader": "^6.0.11", "vue-eslint-parser": "^9.0.2", "vue-template-compiler": "^2.6.14", "webpack": "^5.73.0"}}