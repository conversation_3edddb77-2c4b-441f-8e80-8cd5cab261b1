import axios from '@/utils/request';


/**
 * 查询代理佣金提现统计
 * @param params 查询条件
 */
 export async function resourcesGoods(params) {
  const res = await axios.get('/figure.resources/goodsList', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


export async function updateGoodsStatus(data) {
  const res = await axios.post('/figure.resources/updategoodsStatus', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}




export async function resourcesOrder(params) {
  const res = await axios.get('/figure.resources/orderList', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


export async function resourcesProfit(params) {
  const res = await axios.get('/figure.resources/profit', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除
 * @param id id
 */
export async function remove(id) {
  const res = await axios.get('/figure.resources/delete?id=' + id);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


export async function setting() {
  const res = await axios.get('/figure.resources/setting');
  if (res.data.code === 0) {
    return res.data.data    ;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function configSave(data) {
  console.log(data)
  const res = await axios.post('/figure.resources/configSave',data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
