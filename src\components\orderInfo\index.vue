<!-- 详情弹窗 -->
<template>
  <ele-modal
    title="详情"
    width="750px"
    :visible="visible"
    @update:visible="updateVisible"
    append-to-body 
  >
    <el-form size="mini" label-width="100px" class="ele-form-detail">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="订单编号:">
            <div class="ele-text-secondary">
              {{ data.order_number }}
            </div>
          </el-form-item>
          <el-form-item   label="下单用户:">
            <div class="ele-text-secondary">
              
               <div style="display:flex">
                <span>
                  <el-avatar size="small"  :src="data.avatar" ></el-avatar>
                </span >
                 <span style="margin-left:5px;">{{ data.nickname }}</span>
               </div>
              
            </div>
          </el-form-item>
          <el-form-item label="支付金额:" v-if="data.type==1">
            
            <div class="ele-text-secondary">
              ￥{{ data.real_payment }}
            </div>
          </el-form-item>
          <span v-if="data.type==2">
            <el-form-item label="报名费:" >
              <div class="ele-text-secondary">
                ￥{{ data.entry_price }}
              </div>
            </el-form-item>
            <el-form-item label="尾款:" v-if="data.yk_order_number!='' && data.yk_order_number!=null">
              <div class="ele-text-secondary">
                ￥{{ data.real_payment }}
              </div>
            </el-form-item>
          </span>
          <el-form-item label="使用优惠券:" v-if="data.discount>0">
            <div class="ele-text-secondary">
              ￥{{ data.discount }}
            </div>
          </el-form-item>
          <el-form-item label="使用积分:" v-if="data.integral>0">
            <div class="ele-text-secondary">
              {{ data.integral }}积分兑换￥{{ data.integral_price }}
            </div>
          </el-form-item>
          <el-form-item label="支付方式:">
            <div class="ele-text-secondary">
              <span v-if="data.pay_type==1">微信</span>
              <span v-if="data.pay_type==2">余额</span>
              <span v-if="data.pay_type==3">支付宝</span>
              <span v-if="data.pay_type==4">现金</span>
            </div>
          </el-form-item>
          <el-form-item label="下单时间:">
            <div class="ele-text-secondary">
              {{ data.create_time }}
            </div>
          </el-form-item>
          <el-form-item label="联系姓名:">
            <div class="ele-text-secondary">
              {{data.realname}}
       
            </div>
          </el-form-item>
          <el-form-item label="联系电话:">
            <div class="ele-text-secondary">
              {{data.mobile}}
       
            </div>
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          
          <el-form-item label="备注:">
            <div class="ele-text-secondary">
              {{data.remark}}
       
            </div>
          </el-form-item>
          <el-form-item label="订单状态:" v-if="data.type==1">
            <div class="ele-text-secondary">
              <el-tag :type="['danger', 'info','success','','success'][data.status]" size="mini">
              {{ ['取消', '待支付','已支付','','已完成'][data.status] }}
            </el-tag>
              {{ data.ip }}
            </div>
          </el-form-item>

          <el-form-item label="订单状态:" v-if="data.type==2">
            <div class="ele-text-secondary">
              
              <el-tag type="danger" size="mini" v-if="data.status==0">
                取消
              </el-tag>
              <el-tag type="info" size="mini" v-if="data.status==1">
                待支付
              </el-tag>
              <el-tag type="success" size="mini" v-if="data.status==4 && data.yk_status_c==4">
                已完成
              </el-tag>
              <el-tag type="success" size="mini" v-else-if="data.status==4">
                已报名
              </el-tag>
          
            </div>
          </el-form-item>

          


          <el-form-item label="完成时间:" v-if="(data.status==4)">
            <div class="ele-text-secondary">
              {{ data.end_time }}
            </div>
          </el-form-item>


          
           <el-form-item label="是否需要开票:">
            <div class="ele-text-secondary">
              <el-tag :type="['danger', 'info','success','','success'][data.need_fp]" size="mini">
              {{ ['', '不需要','需要','','已完成'][data.need_fp] }}
            </el-tag>
            </div>
          </el-form-item>

          

        
         
        </el-col>
      </el-row>
      <div style="margin: 12px 0">
         
        <el-divider />
      </div>
      <div v-if="this.data.type==1">
          <el-table
            :data="data.goods"
            style="width: 100%">
            <el-table-column
              label="产品"
              width="200">
              <template slot-scope="scope">

                <div style="display:flex">
                    <span>
                    
                      <el-avatar size="small" shape="square"  :src="scope.row.foods_img"></el-avatar>
                    </span >
                    <span style="margin-left:10px;align-items: center;">{{ scope.row.foods_name }}</span>
                </div>

              </template>
            </el-table-column>
            <el-table-column  width="180" label="服务">
              <template slot-scope="scope">
                {{scope.row.service_desc}} ￥{{scope.row.service_price}} 
              </template>
            </el-table-column>
            <el-table-column
              label="单价"
              width="180">
              <template slot-scope="scope">
                {{scope.row.unit_price}} 元
              </template>
            </el-table-column>
            <el-table-column label="数量">
              <template slot-scope="scope">
                X {{scope.row.num}} 
              </template>
            </el-table-column>
          </el-table>
          <el-form-item >

              <div class="ele-text-secondary" style="text-align: right;margin-right: 90px;margin-top: 2px;">
                  共计 {{sumCount}} 件
                </div>
                <div class="ele-text-secondary" style="text-align: right;margin-right: 90px;margin-top: 2px;" v-if="data.discount>0">
                  优惠券优惠 <span style="color: #ff5200;">-￥{{data.discount}}</span>
                </div>
                <div class="ele-text-secondary" style="text-align: right;margin-right: 90px;margin-top: 2px;" v-if="data.integral>0">
                  使用积分 <span style="color: #ff5200;">-￥{{data.integral_price}}</span>
                </div>
                <div class="ele-text-secondary" style="text-align: right;margin-right: 90px;margin-top: 2px;" >
                  实付款 ￥{{data.real_payment}}
                </div>
                
              </el-form-item>
      </div>
      <el-row :gutter="15" v-if="(this.data.type==2 || this.data.type==3)">
        <el-col :sm="12">
          <el-form-item   label="购买产品:">
            <div class="ele-text-secondary">
              {{ data.goods[0].foods_name }}
            </div>
          </el-form-item>
          <el-form-item label="产品原价:">
            <div class="ele-text-secondary">
              ￥{{ data.goods[0].original_price }}
            </div>
          </el-form-item>
          <el-form-item label="团购阶段:">
            <div class="ele-text-secondary">
              ￥{{ data.goods[0].discount }} 可购买
            </div>
          </el-form-item>

          <el-form-item label="购买时阶段:" v-if="data.group_status == 2">
            <div class="ele-text-secondary">
              报名{{data.end_number}}人
            </div>
          </el-form-item>
          
          <el-form-item label="活动时间:">
            <div class="ele-text-secondary">
              {{ data.goods[0].start_time }}  ~  {{data.goods[0].end_time }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="报名人数:">
            <div class="ele-text-secondary">
              {{ data.goods[0].people_count }}个  
            </div>
          </el-form-item>
          <el-form-item label="团购价格:">
            <div class="ele-text-secondary">
              <div v-for="(item,index) in data.goods[0].ladder_arr">
                满 {{item.p_number}}人,￥{{item.p_price}}元购买
              </div>
            </div>
          </el-form-item>
          
        
         
        </el-col>
      </el-row>
    </el-form>
    <template v-slot:footer>
      <el-button @click="updateVisible(false)">关闭</el-button>
    </template>
  </ele-modal>
</template>

<script>
import TextEllipsis from './text-ellipsis.vue';
import {
    getSingle
  } from '@/api/order/list';
  export default {
    name: 'OrderInfo',
    components: { TextEllipsis },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 数据
      data: {
        type: Object,
        required: true
      }
    },
     data() {
      return {
        // 表格列配置
        sumPrice:0,
        sumCount:0,
        timelineData:[1,2,3,4],
        columns: [
         
          {
            prop: 'foods_name',
            label: '菜品',
            slot: 'foods_name',
            sortable: 'custom',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'unit_price',
            label: '单价',
            sortable: 'custom',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'num',
            label: '数量',
            sortable: 'custom',
            showOverflowTooltip: true,
            minWidth: 110
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示导入弹窗
        showAuth: false,
        showChoose:false
      };
    },
    watch:{
      visible:function(){
        if(this.visible){
          debugger;
          this.getSingleMethod();

          
        }else{
          this.sumCount = 0;
          this.sumPrice = 0;
        }
      }

    },
     mounted() {
      
     },
    methods: {
      /* 更新visible */
       openUrl(share_url){
        window.open(share_url,'_blank');
      },
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      updateSum(){
        let foods =  this.data.goods;
          let sumCount = 0;
          let sumPrice = 0;
        debugger;
          foods.forEach(element => {
                sumCount+=element.num;
                let p = element.num * element.unit_price;
                sumPrice+=p;
          });
          this.sumCount = sumCount;
          this.sumPrice = sumPrice+(this.data.zou_price*1);
      },
      /* 详情 */
      getSingleMethod() {
        debugger;
        this.loading = true;
        let _this = this;
        getSingle(this.data.order_id)
            .then((msg) => {
              this.loading = false;
              this.data = msg;
              _this.updateSum();
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });
      }
    
    }
  };
</script>
