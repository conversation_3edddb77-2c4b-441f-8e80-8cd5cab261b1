<template>
    <ele-modal
    width="750px"
    :visible="visible"
    :append-to-body="true"
    :close-on-click-modal="true"
    custom-class="ele-dialog-form "
    title="更新日志"
    @update:visible="updateVisible"
  >
    <div class="modal-cls">
      <div style="margin-bottom:20px;font-size: 18px;margin-left: 25px;font-weight: bold;">
        当前版本 <span class="ele-text-danger">{{ version }}</span>
        <el-button style="float: right;margin-right: 15px;" v-if="versionFlag" size="small" type="primary" :loading="loading" @click="updateVersion()">在线更新</el-button>
      </div>
        
      <div class="time-line" >
        <el-timeline   v-if="data" >
            <el-timeline-item  
            v-for="(item, index) in data"
            :key="index"
            :type="version == item.version_number ? 'warning' : 'primary'"
            size="large"
            >
                <div class="time">
                <div>
                    <span class="year">
                    {{ item.date.substring(5, 11) }}
                    </span>
                </div>
                <div class="day">{{ item.date.substring(0, 4) }}年</div>
                </div>
                <div class="ml10">
                <div class="list-title">
                    版本信息：{{ item.version_number }}
                </div>

                <div class="box">
                    <div class="list-company">{{ item.title }}</div>
                    <div v-html="item.remarks" class="list-desc"></div>
                </div>
             
                </div>
            </el-timeline-item>
        </el-timeline>
        <span v-else>暂无更新日志</span>
    </div>
  </div>
    
</ele-modal>
</template>


<script>
  import { getVersion,versionList,updateVersion } from '@/api/index';

  export default {
    name: 'Version',
    props: {
      // 弹窗是否打开
      visible: Boolean,
    },
    computed: {
      // 当前显示语言
      language() {
        return this.$i18n.locale;
      }
    },
    data(){
        return {
            version:'',
            versionFlag:0,
            data:[],
            loading:false
        }
    },
    created() {
      
      this.getVersion();
      this.updateVersionList();
    },
    methods: {
      getVersion(){
        getVersion().then((res) => {
          this.version = res.version;
          this.versionFlag = res.flag;
        }).catch((e) => {
          this.$message.error(e.message);
        });
      },
      updateVersionList(){
        versionList().then((res) => {
          this.data = res.list;
        }).catch((e) => {
          this.$message.error(e.message);
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      updateVersion() {
        this.loading = true;
        updateVersion().then((msg) => {
          this.loading = false;
          this.$message.success(msg);
          this.getVersion();
          this.updateVersionList();
          this.$emit('done');
        }).catch((e) => {
          this.loading = false;
          this.$message.error(e.message);
        });
      },
    }
  };
</script>
<style lang="scss" scoped>
  .modal-cls{
    height:450px;overflow:auto;
  }
  .time-line {
    margin-left: 110px;
    margin-right: 10px;
  }
  .list-title {
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #181b1e;
  }
  .box{
    box-shadow: -1px 3px 10px #eee;
    border-radius: 10px;
  }
  .list-company {
    font-size: 18px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 700;
    color: #2991ff;
    margin-top: 15px;
    background:rgb(***********);
    padding: 16px 10px;
    border-radius: 10px 10px 0px 0px;
  }
  .list-desc {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #596878;
    padding: 10px;
  }
  //左侧时间
  .time {
    color: #409eff;
    position: absolute;
    left: -85px;
    top: 1px;
    .year {
      font-size: 18px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #20354a;
    }
    .day {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #596878;
      text-align: center;
      margin-top: 10px;
    }
  }
</style>