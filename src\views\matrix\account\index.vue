<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <log-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            class="ele-btn-icon"
            @click="removeBatch"
          >
            删除
          </el-button>
        </template>

        <template v-slot:user="{ row }">
          <div class="ele-cell" v-if="row.figureUser">
            <el-avatar
              v-if="row.figureUser.avatar"
              :src="row.figureUser.avatar"
              shape="square"
              :size="40"
            />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.figureUser.nickname }} </div>
              <div class="ele-cell-desc">{{ row.figureUser.telphone }} </div>
            </div>
          </div>
          <span v-else>无</span>
        </template>

          <template v-slot:avatar="{ row }">
          <div class="ele-cell">
            <el-avatar
              v-if="row.avatar"
              :src="row.avatar"
              shape="square"
              :size="40"
            /></div>
      
        </template>

        <template v-slot:status="{ row }">
          <div v-if="row.status == 1">
            <ele-dot text="已授权" type="success" :ripple="true" />
            <div>{{row.expires_in }}</div>
          </div>
      <div v-if="row.status == 2">
            <ele-dot text="已失效" type="danger" :ripple="true" />
            <div>{{ row.expires_in }}</div>
          </div>
        </template>

 <template v-slot:type="{ row }">
          <div v-if="row.type == 1">
            <ele-dot text="抖音" type="success" :ripple="true" />
          
          </div>
      <div v-if="row.type == 2">
            <ele-dot text="快手" type="danger" :ripple="true" />
    
          </div>
            <div v-if="row.type == 3">
            <ele-dot text="视频号" type="primary" :ripple="true" />
    
          </div>
            <div v-if="row.type == 4">
            <ele-dot text="小红薯" type="warning" :ripple="true" />
    
          </div>
        </template>



      </ele-pro-table>
    </el-card>

  
  </div>
</template>

<script>

import LogSearch from './components/log-search';
import { account} from '@/api/matrix';

export default {
  name: 'Video',
  components: {
    LogSearch
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          columnKey: 'selection',
          type: 'selection',
          width: 45,
          align: 'center',
          fixed: 'left'
        },
        {
          prop: 'user',
          label: '用户',
          showOverflowTooltip: true,
          minWidth: 110,
          slot: 'user'
        },
        {
          prop: 'account_name',
          label: '昵称',
          showOverflowTooltip: true,
          minWidth: 100,
      
        },
          {
          prop: 'avatar',
          label: '头像',
          showOverflowTooltip: true,
          minWidth: 100,
            slot: 'avatar'
      
        },

           {
          prop: 'type',
          label: '账号归属平台',
          showOverflowTooltip: true,
          minWidth: 100,
          slot: 'type'
      
        },
          {
          prop: 'status',
          label: '授权状态/预计失效时间',
          showOverflowTooltip: true,
          minWidth: 100,
          slot: 'status'
      
        },

        {
          prop: 'totle_task',
          label: '总任务数',
          showOverflowTooltip: true,
          minWidth: 100,
       
      
        },
          {
          prop: 'published_task',
          label: '已发布任务数量',
          showOverflowTooltip: true,
          minWidth: 100,
       
      
        },
        
      
    
      ],
      // 表格选中数据
      selection: [],
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showEdit: false,
      // 是否显示分配回收员
      showRepost: false,
      //弹框名称
      //弹框名称
      id: 0,
      videoName: '',
      dyPic: [],
      ksPic: []
    };
  },
  methods: {
    /* 表格数据源 */
    datasource({ page, limit, where, order }) {
      return account({ ...where, ...order, page, limit });
    },
    /* 刷新表格 */
    reload(where) {
      this.$refs.table.reload({ page: 1, where: where });
    },
    /* 打开编辑弹窗 */
    openEdit(row) {
      this.current = row;
      this.showEdit = true;
    },
    /* 打开编辑弹窗 */
    openRepost(row) {
      this.id = row.id;
      this.current = row;
      this.showRepost = true;
      this.videoName = row.name;
      this.dyPic = [row.dy_qrcode];
      this.ksPic = [row.ks_qrcode];
    },
    /* 删除 */
    remove(row) {
      const loading = this.$loading({ lock: true });
      remove(row.id)
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
    },

    refund(row) {
      const loading = this.$loading({ lock: true });
      refund(row.id)
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
    },

    /* 批量删除 */
    removeBatch() {
      if (!this.selection.length) {
        this.$message.error('请至少选择一条数据');
        return;
      }
      this.$confirm('确定要删除选中的数据吗?', '提示', {
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading({ lock: true });
          remove(this.selection.map((d) => d.id))
            .then((msg) => {
              loading.close();
              this.$message.success(msg);
              this.reload();
            })
            .catch((e) => {
              loading.close();
              this.$message.error(e.message);
            });
        })
        .catch(() => {});
    },

    toLocal(row) {
      const loading = this.$loading({ lock: true });
      toLocal({ id: row.id })
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          row.status = !row.status ? 1 : 0;
          this.$message.error(e.message);
        });
    },

    /* 更改状态 */
    editStatus(row) {
      const loading = this.$loading({ lock: true });
      modify({ id: row.id, field: 'is_display', value: row.is_display })
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
        })
        .catch((e) => {
          loading.close();
          row.status = !row.status ? 1 : 0;
          this.$message.error(e.message);
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.drag {
  background: #000 !important;
  background-image: linear-gradient(#333, #999) !important;
}

.ele-cell-content {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
