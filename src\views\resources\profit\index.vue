<template>
  <div class="ele-body">
    <el-card shadow="never">
      <statistics-card />
      <!-- 搜索表单 -->
      <resources-search @search="reload" />
      <!-- 数据表格 -->
     <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
       
        <template v-slot:user="{row}">
            <el-avatar v-if="row.fu_avatar" :src="row.fu_avatar" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.fu_nickname }} </div>
          </div>
        </template>

         <template v-slot:type="{ row }">
          <el-tag v-if="row.type==1" >形象</el-tag>
          <el-tag v-if= "row.type==2" >形象</el-tag>
            <el-tag v-if= "row.type==3" >声音</el-tag>
        </template>

          <template v-slot:video_url="{ row }">
             <el v-if="row.type==1" > <video  width="300" height="240" controls :src="row.fa_video_url"  > </video></el>
            <el v-if= "row.type==2" >  <video  width="300" height="240" controls :src="row.fca_url"  > </video></el>
            <el v-if= "row.type==3" >   <div v-for="item in row.fat_voice_urls" :key="item">
            <audio   :src="item"   controls="controls"></audio>
          </div></el>
        </template>

     


    
      
      </ele-pro-table>
    </el-card>

  </div>
</template>

<script>

  import ResourcesSearch from './components/resources-search';
  import { resourcesProfit } from '@/api/resources/index.js';

  export default {
    name: 'Resources',
    components: {
      ResourcesSearch,
     
    },
   data() {
      return {
        // 表格列配置
        columns: [
        
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            align: 'center',
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'fg_log_no',
            label: '订单号',
            showOverflowTooltip: true,
            minWidth: 110,
          
          },
          {
            prop: 'user',
            label: '收益用户',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'user',
          },
          {
            prop: 'type',
            label: '类型',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 100,
            slot: 'type',
          },
          {
            prop: 'video_url',
            label: '链接',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 280,
             slot: 'video_url',
            
          },
          {
            prop: 'order_price',
            label: '价格',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 80
          },
           
         
             {
            prop: 'user_profit',
            label: '收益',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 80
          },

        
          {
            prop: 'create_time',
            label: '时间',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 100,
           
          },
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示审核弹窗
        showCheck: false,
        // 是否显示充值弹窗
        showRecharge: false,
        //弹框名称
        id:0,
      };
    },
    methods: {
      beforeHandleCommand(index, row,command){

          return {
              'index': index,
              'row': row,
              'command':command
          }
    },
    
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return resourcesProfit({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },

      /* 打开编辑弹窗 */
      openCheck(row) {
        this.id = row.id;
        this.current = row;
        this.showCheck = true;
      },

      /* 打开编辑弹窗 */
      openRecharge(row) {
        this.id = row.id;
        this.current = row;
        this.showRecharge = true;
      },
     
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      },
      /* 更新二维码 */
    

     
      
      /* 更改状态 */
      editStatus(row,statusValue) {
        const loading = this.$loading({ lock: true });
        updateGoodsStatus({id:row.id,field:'status',value: statusValue})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
              this.reload();
          })
            .catch((e) => {
              loading.close();
              this.$message.error(e.message);
            });
         
      }
    }
  };
</script>
