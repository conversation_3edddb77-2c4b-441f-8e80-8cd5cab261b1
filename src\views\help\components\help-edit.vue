<!-- 用户编辑弹窗 -->
<template>
  <ele-modal
    width="740px"
    :visible="visible"
    :append-to-body="true"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? '修改教程' : '添加教程'"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
     

      <el-form-item
        label="视频地址"
        class="check-star set-class25"
        prop="introduce_url"
        :rules="
          !form.introduce_url
            ? {
                required: true,
                message: '请上传视频地址',
                trigger: ['blur', 'change']
              }
            : { required: false }
        "
      >
        <div class="flexBetween upload-class">
          <el-input
            clearable
            v-model.number="form.introduce_url"
            placeholder="请上传视频地址"
          />
          <div style="margin-left: 10px" @click="uploadVideo">
            <el-button  size="small">上传</el-button></div
          >
        </div>
      </el-form-item>

      <el-form-item label="标题:" prop="title">
        <el-input clearable v-model="form.title" placeholder="请输入标题" />
      </el-form-item>

      <el-form-item label="描述:" prop="desc">
        <el-input clearable v-model="form.desc" placeholder="请输入描述" />
      </el-form-item>

      <el-form-item label="排序号:" prop="sort">
        <el-input-number
          :min="0"
          v-model="form.sort"
          placeholder="请输入排序号"
          controls-position="right"
          class="ele-fluid ele-text-left"
        />
      </el-form-item>

      <file-upload :visible.sync="folderUpload" @done="onDone" />
    </el-form>
    <template v-slot:footer>
      <el-button @click="updateVisible(false)">取消 </el-button>
      <el-button type="primary" :loading="loading" @click="save"
        >保存
      </el-button>
    </template>
    <uploadPictures
      :isChoice="isChoice"
      :visible.sync="modalPic"
      @getPic="getPic"
      :gridBtn="gridBtn"
      :gridPic="gridPic"
      :title="modalTitle"
    ></uploadPictures>
  </ele-modal>
</template>

<script>
import PagesSearch from '@/views/common/pages/pages-search';
import uploadPictures from '@/components/uploadPictures';
import EleImageUpload from 'ele-admin/es/ele-image-upload';
import TinymceEditor from '@/components/TinymceEditor';
import { add, update } from '@/api/help';
import { getPages } from '@/api/layout';
import fileUpload from './file-upload.vue';
const DEFAULT_FORM = {
  id: 0,
  title: '',
  desc: '',

  introduce_url:'',
  sort: 0
};

export default {
  name: 'HelpEdit',
  components: {
    PagesSearch,
    EleImageUpload,
    uploadPictures,
    TinymceEditor,
    fileUpload
  },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object,
    b_type: 0
  },
  computed: {
    templateType() {
      return this.$store.state.user.template_type;
    }
  },
  data() {
    return {
      folderUpload: false,
      modalTitle: '选择图片',
      modalPic: false,
      isChoice: '单选',
      gridBtn: {
        xl: 4,
        lg: 8,
        md: 8,
        sm: 8,
        xs: 8
      },
      gridPic: {
        xl: 6,
        lg: 8,
        md: 12,
        sm: 12,
        xs: 12
      },
      // 表单数据
      form: { ...DEFAULT_FORM },
      // 表单验证规则
      rules: {
        title: [
          {
            required: true,
            message: '请输入轮播图标题',
            trigger: 'blur'
          }
        ]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      tableConfig: {
        datasource: ({ page, limit, where, order }) => {
          return getPages({ ...where, ...order, page, limit });
        },
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            align: 'center'
          },
          {
            prop: 'name',
            label: '名称',
            showOverflowTooltip: true,
            minWidth: 110
            //slot: 'nickname'
          },
          {
            prop: 'url',
            label: '路径',
            showOverflowTooltip: true,
            minWidth: 110
          }
        ],
        rowClickChecked: true,
        rowClickCheckedIntelligent: false,
        toolkit: ['reload', 'columns'],
        size: 'small',
        toolStyle: { padding: '0 10px' }
      }
    };
  },
  methods: {
    uploadVideo() {
      this.folderUpload = true;
    },
    onDone(file) {
  
      this.folderUpload=false

this.form.introduce_url = file;
      // this.form.matrixVideo = file;
      // console.log("this.form.matrixVideo=====", this.form.matrixVideo)
    },
    search(where) {
      // debugger
      this.$refs.select.reload({
        where: where,
        page: 1
      });
    },
    // 选择图片
    modalPicTap(tit, picTit, openTitle) {
      this.modalTitle = openTitle;
      this.isChoice = tit === 'dan' ? '单选' : '多选';
      this.picTit = picTit;
      this.modalPic = true;
    },
    // 选中图片
    getPic(pc) {
      switch (this.picTit) {
        case 'pic_url':
          this.form.pic_url = pc.satt_dir;
          break;
        case 'background':
          this.form.background = pc.satt_dir;
          break;
      }
      this.modalPic = false;
    },
    //删除图片
    handleRemove(field) {
      this.form[field] = '';
    },

    /* 保存编辑 */
    save() {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          return false;
        }

        this.loading = true;
        const data = {
          ...this.form
        };

        data.b_type = this.b_type;

        const saveOrUpdata = this.isUpdate ? update : add;

        saveOrUpdata(data)
          .then((msg) => {
            this.loading = false;
            this.$message.success(msg);
            this.updateVisible(false);
            this.$emit('done', { b_type: this.b_type });
          })
          .catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
          });
      });
    },
    /* 更新visible */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  },
  watch: {
    visible(visible) {
      if (visible) {
        if (this.data) {
          this.$util.assignObject(this.form, {
            ...this.data
          });
          this.isUpdate = true;
        } else {
          this.isUpdate = false;
        }
      } else {
        this.$refs['form'].clearValidate();
        this.form = { ...DEFAULT_FORM };
      }
    }
  }
};
</script>
<style lang="less">
 .flexBetween{
   display: flex;
   align-content: center;
   justify-content: center;
 }
</style>
