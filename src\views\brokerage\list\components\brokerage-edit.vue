<!-- 用户编辑弹窗 -->
<template>
  <ele-modal
    width="70%"
    :visible="visible"
    :append-to-body="true"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="'提现详情'"
    @update:visible="updateVisible"
  >
    <el-form v-if="row.is_status == 1" ref="form" :model="form" :rules="rules" label-width="120px">
      <div v-if="row.type == 2" style="margin-bottom: 20px; padding: 0 20px">
        <el-alert
          title="确认转账后, 资金将直接打入对方账户, 无法退回。"
          type="info"
          show-icon
          class="ele-alert-border"
        />
      </div>
      <el-row :gutter="15">
        <el-col :sm="12">
          <div class="user-info-card">
            <div class="user-info-avatar-group" >
              <img class="user-info-avatar" :src="row.figureUser.avatar" alt="" />
            </div>
            <h2 class="user-info-name">{{  row.figureUser.nickname }}</h2>
            <h2 class="user-info-name">{{  row.figureUser.telphone }}</h2>
          </div>
          <div class="user-info-list">
            <div class="user-info-item">
              <span>状态：
                <el-tag v-if="row.is_status==1 && row.cash_type==1"  class="status-text">待审核</el-tag>
                    <el-tag v-if="row.is_status==1 && row.cash_type==2   && row.wx_transfer_version==1"  class="status-text">待审核</el-tag>
                  <el-tag v-if="row.is_status==1 && row.cash_type==2 && row.wx_transfer_version ==2 "  class="status-text">等待用户</el-tag>
                <el-tag v-if="row.is_status==2" type="success" class="status-text">已打款</el-tag>
                <el-tag v-if="row.is_status==3" type="danger" class="status-text">已驳回</el-tag>
              </span>
            </div>
          </div>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="提现单号:" prop="log_no">
            <span>{{ row.log_no }}</span>
          </el-form-item>
          <el-form-item label="提现方式:" prop="type">
            <ele-dot v-if="row.cash_type==1" :ripple="true" text="支付宝"/>
            <ele-dot v-if="row.cash_type==2" type="success" :ripple="true" text="微信"/>
            <ele-dot v-if="row.cash_type==3" type="warning" :ripple="true" text="余额"/>
          </el-form-item>
          <el-form-item label="收款人昵称:" prop="nickname" v-if="row.nickname">
            <span>{{ row.nickname }}</span>
          </el-form-item>
          <el-form-item label="收款人账号:" prop="telphone" v-if="row.telphone" >
            <span>{{ row.telphone }}</span>
          </el-form-item>
          <el-form-item label="申请金额:" prop="commission_wait">
            <span>{{ row.commission_wait }}元</span>
          </el-form-item>
          
          <el-form-item label="提现手续费:" prop="service_charge">
            <span>{{ row.service_charge }}元</span>
          </el-form-item>

          <el-form-item label="转换比例:" prop="cash_charge" v-if="row.cash_type==3 && row.cash_charge">
            <span>{{ row.cash_charge }}点 = 1元</span>
          </el-form-item>

          <el-form-item label="实际金额:" prop="commission_actual">
            <span style="font-size: 24px">{{ row.commission_actual }}</span>
            <span> {{ row.cash_type== 3 ? '点' : '元' }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      
      <div style="margin: 20px 0 30px 0">
        <el-divider />
      </div>
      <el-alert type="info" :closable="false" class="ele-alert-border">
          <h6 style="margin: 5px 0 15px 0">说明</h6>
          <h6 style="margin-bottom: 10px">转账到支付宝</h6>
          <p style="margin-bottom: 15px">
            线下操作打款后，修改记录为已打款状态
          </p>
          <h6 style="margin-bottom: 10px">转账到微信</h6>
          <p style="margin-bottom: 15px">
            审核提现信息正确后，点击打款，线上打款给申请人的微信零钱账户
          </p>
        </el-alert>
    </el-form>

    <ele-result v-if="row.is_status == 2" title="打款成功">
      <el-form label-width="100px" size="mini" class="ele-form-detail">
        <el-row :gutter="15">
          <el-col :sm="12">
            <div class="user-info-card">
              <div class="user-info-avatar-group" >
                <img class="user-info-avatar" :src="row.figureUser.avatar" alt="" />
              </div>
              <h2 class="user-info-name">{{  row.figureUser.nickname }}</h2>
              <h2 class="user-info-name">{{  row.figureUser.telphone }}</h2>
            </div>
          </el-col>
          <el-col :sm="12">
            <el-form-item label="提现单号:" prop="log_no">
              <span>{{ row.log_no }}</span>
            </el-form-item>
            <el-form-item label="提现方式:" prop="type">
              <ele-dot v-if="row.cash_type==1" :ripple="true" text="支付宝"/>
              <ele-dot v-if="row.cash_type==2" type="success" :ripple="true" text="微信"/>
              <ele-dot v-if="row.cash_type==3" type="warning" :ripple="true" text="余额"/>
            </el-form-item>
            <el-form-item label="收款人昵称:" prop="nickname" v-if="row.nickname">
              <span>{{ row.nickname }}</span>
            </el-form-item>
            <el-form-item label="收款人账号:" prop="telphone" v-if="row.telphone">
              <span>{{ row.telphone }}</span>
            </el-form-item>
            <el-form-item label="申请金额:" prop="commission_wait">
              <span>{{ row.commission_wait }}元</span>
            </el-form-item>
            
            <el-form-item label="提现手续费:" prop="service_charge">
              <span>{{ row.service_charge }}元</span>
            </el-form-item>

            <el-form-item label="转换比例:" prop="cash_charge" v-if="row.cash_type==3 && row.cash_charge">
              <span>{{ row.cash_charge }}点 = 1元</span>
            </el-form-item>

            <el-form-item label="实际金额:" prop="commission_actual">
              <span style="font-size: 24px">{{ row.commission_actual }}</span>
              <span> {{ row.cash_type== 3 ? '点' : '元' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-result>
    <ele-result v-if="row.is_status == 3" type="fail" title="打款驳回">
      
      <el-form label-width="100px" size="mini" class="ele-form-detail">
        <div style="margin-bottom: 20px; padding: 0 20px">
          <el-alert
            :title="'驳回原因：' + row.refuse  "
            type="error"
            show-icon
            class="ele-alert-border"
          />
        </div>

        <el-row :gutter="15">
          <el-col :sm="12">
            <div class="user-info-card">
              <div class="user-info-avatar-group" >
                <img class="user-info-avatar" :src="row.figureUser.avatar" alt="" />
              </div>
              <h2 class="user-info-name">{{  row.figureUser.nickname }}</h2>
              <h2 class="user-info-name">{{  row.figureUser.telphone }}</h2>
            </div>
          </el-col>
          <el-col :sm="12">
            <el-form-item label="提现单号:" prop="log_no">
              <span>{{ row.log_no }}</span>
            </el-form-item>
            <el-form-item label="提现方式:" prop="type">
              <ele-dot v-if="row.cash_type==1" :ripple="true" text="支付宝"/>
              <ele-dot v-if="row.cash_type==2" type="success" :ripple="true" text="微信"/>
              <ele-dot v-if="row.cash_type==3" type="warning" :ripple="true" text="余额"/>
            </el-form-item>
            <el-form-item label="收款人昵称:" prop="nickname" v-if="row.nickname">
              <span>{{ row.nickname }}</span>
            </el-form-item>
            <el-form-item label="收款人账号:" prop="telphone" v-if="row.telphone">
              <span>{{ row.telphone }}</span>
            </el-form-item>
            <el-form-item label="申请金额:" prop="commission_wait">
              <span>{{ row.commission_wait }}元</span>
            </el-form-item>

            <el-form-item label="提现手续费:" prop="service_charge">
              <span>{{ row.service_charge }}元</span>
            </el-form-item>

            <el-form-item label="转换比例:" prop="cash_charge" v-if="row.cash_type==3 && row.cash_charge">
              <span>{{ row.cash_charge }}点 = 1元</span>
            </el-form-item>

            <el-form-item label="实际金额:" prop="commission_actual">
              <span style="font-size: 24px">{{ row.commission_actual }}</span>
              <span> {{ row.cash_type== 3 ? '点' : '元' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-result>
    <template v-if="row.is_status == 1 " v-slot:footer>
      <el-popconfirm
        class="ele-action"
        title="确定打款该提现吗？"
        @confirm="editSubmit"
      >
        <template v-slot:reference  v-if="row.wx_transfer_version != 2 ">
          <el-button type="primary" :loading="loading">审核打款</el-button>
        </template>
      </el-popconfirm>
      <el-button type="danger" @click="openReject()">审核驳回</el-button>
      <el-button @click="updateVisible(false)">取消 </el-button>
    </template>
    <ele-modal 
      width="50%"
      :close-on-click-modal="false"
      custom-class="ele-dialog-form"
      title="提现驳回"
      :append-to-body="true"
      :visible.sync="showReject">


      <el-form :model="form" label-width="120px">
        <el-form-item label="驳回原因:" prop="refuse" >
          <el-input v-model="form.refuse" autocomplete="off"  placeholder="驳回原因" clearable ></el-input>
        </el-form-item>
        
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showReject = false">取 消</el-button>
        <el-button type="primary" @click="rejectSubmit()">确 定</el-button>
      </span>
     
    </ele-modal> 
  </ele-modal>
</template>

<script>
  import RegionsSelect from '@/components/RegionsSelect/index.vue';
  import { emailReg, phoneReg } from 'ele-admin';
  import { editSubmit, rejectSubmit } from '@/api/brokerage';
  const DEFAULT_FORM = {
    id: 0,
    refuse:''
  };

  export default {
    name: 'CommissionEdit',
    components: { RegionsSelect },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        row:{},
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false,
        showReject:false
      };
    },
    methods: {
      openSubmit(){
        this.showSubmit = true;
      },
      openReject(){
        this.showReject = true;
      },
      editSubmit(){
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }

          const data = {
            ...this.form,
          };

          editSubmit(data).then((msg) => {
            this.$message.success(msg);
            this.updateVisible(false);
            this.$emit('done');
          }).catch((e) => {

            this.$message.error(e.message);
          });
        });
      },
      rejectSubmit(){
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }

          const data = {
            ...this.form,
          };
          rejectSubmit(data).then((msg) => {
            this.$message.success(msg);
            this.showReject = false;
            this.updateVisible(false);
            this.$emit('done');
          }).catch((e) => {
            this.$message.error(e.message);
            this.showReject = false;
          });
        });
      },
      
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      }
    },
    watch: {
      visible(visible) {
        this.row = this.data;
        if (visible) {
          if (this.data) {
            this.$util.assignObject(this.form, this.data);
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
<style lang="scss" scoped>
.status-text{
  font-size:20px;
  font-weight:bold;
}
.box-title{
  text-align: center;

}
.user-info-card {
    padding: 8px 0;
    text-align: center;

    .user-info-avatar-group {
      position: relative;
      cursor: pointer;
      margin: 0 auto;
      width: 110px;
      height: 110px;
      border-radius: 50%;
      overflow: hidden;

      & > i {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #fff;
        font-size: 30px;
        display: none;
        z-index: 2;
      }

      &:hover {
        & > i {
          display: block;
        }

        &:after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.3);
        }
      }
    }

    .user-info-avatar {
      width: 110px;
      height: 110px;
      border-radius: 50%;
      object-fit: cover;
    }

    .user-info-name {
      font-size: 24px;
      margin-top: 20px;
    }

    .user-info-desc {
      margin-top: 8px;
    }
  }
  /* 用户信息列表 */
  .user-info-list {
    margin-top: 30px;
    text-align: center;

    .user-info-item {
      margin-bottom: 16px;
      display: flex;
      align-items: baseline;

      & > i {
        margin-right: 10px;
        font-size: 16px;
      }

      & > span {
        flex: 1;
        display: block;
      }
    }
  }

</style>
