<!-- 顶栏右侧区域 -->
<template>
  <div class="ele-admin-header-tool">

     <!-- 更新日志 -->
     <div class="ele-admin-header-tool-item" 
    @click="toggleUpdateLog"
     
    >
    
    <el-badge :value="versionFlag" :hidden="!versionFlag">更新日志</el-badge>
    </div>
   
    <!-- 清除缓存 -->
    <div class="ele-admin-header-tool-item"
    @click="toggleClearCache"
    >
      <i class="el-icon-delete"></i>
    </div>
    <!-- 二维码 -->

   
    
    
    <!-- 全屏切换 -->
    <div 
      class="ele-admin-header-tool-item hidden-xs-only"
      @click="toggleFullscreen"
    >
      <i v-if="fullscreen" class="el-icon-_screen-restore"></i>
      <i v-else class="el-icon-_screen-full"></i>
    </div>
    <!-- 语言切换 -->
    <!-- <div class="ele-admin-header-tool-item">
      <i18n-icon />
    </div> -->

    <!-- 消息通知 删除消息通知
    <div class="ele-admin-header-tool-item">
      <header-notice />
    </div>
    -->
    
    <!-- 用户信息 -->
    <div class="ele-admin-header-tool-item">
      <el-dropdown @command="onUserDropClick">
        <div class="ele-admin-header-avatar">
          <el-avatar v-if="(loginUser.tenant_id==1)" src="/imgs/admin.png" />
          <el-avatar v-if="(loginUser.tenant_id==2)" src="/imgs/user.png" />
          <el-avatar v-if="(loginUser.tenant_id==3)" src="/imgs/proxy.png" />
          <el-avatar v-if="(loginUser.tenant_id==4)" src="/imgs/oem.png" />
          <span class="hidden-xs-only">{{ loginUser.nickname }}</span>
          <i class="el-icon-arrow-down"></i>
        </div>
        <template v-slot:dropdown>
          <el-dropdown-menu>
            <!-- 删除掉个人中心 <el-dropdown-item command="profile" icon="el-icon-user">
              {{ $t('layout.header.profile') }}
            </el-dropdown-item>-->
            <el-dropdown-item command="password" icon="el-icon-key">
              {{ $t('layout.header.password') }}
            </el-dropdown-item>
            <el-dropdown-item
              command="logout"
              icon="el-icon-switch-button"
              divided
            >
              {{ $t('layout.header.logout') }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <!-- 主题设置 -->
    <div class="ele-admin-header-tool-item" @click="openSetting">
      <i class="el-icon-_more"></i>
    </div>
    <!-- 修改密码弹窗 -->
    <password-modal :visible.sync="passwordVisible" />
    <!-- 主题设置抽屉 -->
    <setting-drawer :visible.sync="settingVisible" />
    <!-- 更新日志 -->
    <version :visible.sync="versionVisible" @done="reloadVersion" />
  </div>
</template>

<script>
  import HeaderNotice from './header-notice.vue';
  import PasswordModal from './password-modal.vue';
  import SettingDrawer from './setting-drawer.vue';
  import I18nIcon from './i18n-icon.vue';
  import Version from './version.vue';
  import { logout } from '@/utils/page-tab-util';
  import { clearCache } from '@/api/layout';
  import { getVersion } from '@/api/index';

  export default {
    name: 'HeaderTools',
    components: { HeaderNotice, PasswordModal, SettingDrawer, I18nIcon,Version },
    props: {
      // 是否是全屏
      fullscreen: Boolean
    },
    data() {
      return {
        // 是否显示修改密码弹窗
        passwordVisible: false,
        // 是否显示主题设置抽屉
        settingVisible: false,
        version:'1.0.0',
        versionFlag:0,
        versionVisible:false,
      };
    },
    computed: {
      // 当前用户信息
      loginUser() {
        return this.$store.state.user.info;
      }
    },
    created(){
       this.getVersion();
    },
    methods: {
      /* 用户信息下拉点击事件 */
      onUserDropClick(command) {
        if (command === 'password') {
          this.passwordVisible = true;
        } else if (command === 'profile') {
          if (this.$route.fullPath !== '/user/profile') {
            this.$router.push('/user/profile');
          }
        } else if (command === 'logout') {
          // 退出登录
          this.$confirm(
            this.$t('layout.logout.message'),
            this.$t('layout.logout.title'),
            { type: 'warning' }
          )
            .then(() => {
              logout();
            })
            .catch(() => {});
        }
      },
      /* 全屏切换 */
      toggleFullscreen() {
        this.$emit('fullscreen');
      },
      /* 打开设置抽屉 */
      openSetting() {
        this.settingVisible = true;
      },
      /* 清除缓存 */
      toggleClearCache() {
        clearCache().then((msg) => {
          this.loading = false;
          this.$message.success(msg);
        }).catch((e) => {
          this.loading = false;
          this.$message.error(e.message);
        });
      },
      getVersion(){
        getVersion().then((data) => {
          this.versionFlag = data.flag;
        }).catch((e) => {
        });
      },
      /* 更新日志 */
      toggleUpdateLog() {
        this.versionVisible = true;
      },
      reloadVersion(){
        this.getVersion();
      },
    }
  };
</script>
