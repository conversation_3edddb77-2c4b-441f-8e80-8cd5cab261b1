<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">首页设置</div>
    </div>
    <div class="ele-body" style="padding-bottom: 71px;background: var(--color-white);">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="160px"
        @keyup.enter.native="submit"
        @submit.native.prevent
      >
        <el-card shadow="never" body-style="padding: 22px 22px 0 22px;">

          <!-- <el-row>
            <el-col :lg="12" :md="12">
              <el-form-item label="模板类型:" prop="template_type" >
                <el-radio-group v-model="form.template_type">
                  <el-radio :label="1" value="1" >
                    模板1
                    <img class="card_icon" src="@/assets/template/1.png" alt="">
                  </el-radio>
                  <el-radio :label="2" value="2" >
                    模板2
                    <img class="card_icon" src="@/assets/template/2.png" alt="">
                  </el-radio>
                  <el-radio :label="3" value="3" >
                    模板3
                    <img class="card_icon" src="@/assets/template/3.png" alt="">
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>  -->
          
          <el-form-item label="模板类型:" prop="template_type" >
            <div class="flex-around ">
              <div v-for="(item, i) in templateList" class="flex-column" :key="i">
                <el-radio v-model="form.template_type" @input="selsectTemplate" :value="i+1" :label=i+1  style="margin-bottom: 10px">
                  {{ `模板${item.template}` }}
                </el-radio>
                <div>
                  <img class="avatar"
                      @click="templateImage(i)"
                      :src="require(`../../assets/template/${item.img}.png`)"
                      alt="" />
                </div>
                
              </div>

            </div>
          </el-form-item>

          

        
          <el-form-item>
            <el-button type="primary" :loading="loading" @click="submit">
              提交
            </el-button>
          </el-form-item>
        </el-card>
     
        
      </el-form>
    </div>
  </div>
  
</template>

<script>
  import { query ,save } from '@/api/indexSet';
  const DEFAULT_FORM = {
    template_type: 1
  };

  export default {
    name: 'CloneSet',
    components: {  },
    data() {
      return {
        currentTemplateIndex:'',
        templateList: [
          {
            img: '1',
            template: '1'
          },
          {
            img: '2',
            template: '2'
          },
          {
            img: '3',
            template: '3'
          },
           {
            img: '4',
            template: '4'
          },
           {
            img: '5',
            template: '5'
          },

        ],
        // 表单提交状态
        loading: false,
        // 表单数据
        form: {
          ...DEFAULT_FORM
        },
        // 表单验证规则
        rules: {
          template_type: [
            {
              required: true,
              message: '请选择类型',
              trigger: 'blur'
            }
          ]
        },
        // 表单验证信息
        validMsg: '',
      };
    },
    mounted() {
      query().then((msg) => {
        if(msg != null){
          this.form = msg;
        }
      })
      .catch((e) => {
        this.$message.error(e.message);
      });
    },
    methods: {

      selsectTemplate (value) {
        this.currentTemplateIndex = value
      },
      templateImage (i) {
        this.currentTemplateIndex = i + 1
        this.form.template_type = i + 1
      },
      
      /* 表单提交 */
      submit() {
        this.$refs['form'].validate((valid, obj) => {
          if (valid) {
            this.validMsg = '';
            this.loading = true;
            const data = {
            ...this.form
            };
            save(data).then((msg) => {
              this.loading = false;
              this.$message.success(msg);
            }).catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });
          } else {
            this.validMsg = ` 共有校验 ${Object.keys(obj).length} 项不通过`;
            return false;
          }
        });
      }
    }
  };
</script>
<style lang="less" scoped>
  .avatar {
    width: 253px;
    height: 500px;
    cursor: pointer;
  }
  .flex-around {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  /deep/ .ele-bottom-tool-actions {
    text-align: center;
  }

  /deep/.el-radio__input.is-checked + .el-radio__label {
    color: #112231;
  }
</style>
