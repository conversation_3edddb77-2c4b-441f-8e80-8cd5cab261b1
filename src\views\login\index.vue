<template>
  <div
    :class="[
      'login-wrapper',
      ['', 'login-form-right', 'login-form-left'][direction]
    ]"
    :style="indexData.site_login_background!='' ? { 'background-image': 'url(' + indexData.site_login_background + ')' } : { 'background-image': 'url(/imgs/bg-login.jpg)' } "
  >
  
    <div  class="login-form ele-bg-white" style="display: flex;">
      <div class="page-account-top" style="width: 510px;height:420px;">
          <div class="page-account-top-logo">
            <img
              :src="indexData.site_login_image != '' ?indexData.site_login_image :'/imgs/left-bg.jpg' "
              alt="logo"
              style="width: 100%; height: 100%;"
            />
          </div>
        </div>
      <div style="padding: 38px;">
        <el-form
          ref="form"
          size="large"
          :model="form"
          :rules="rules"
          class="login-form-item  ele-bg-white"
          @keyup.enter.native="submit"
        >
          <h4>{{ $t('login.title') }}</h4>
          <el-form-item prop="username">
            <el-input
              clearable
              v-model="form.username"
              prefix-icon="el-icon-user"
              :placeholder="$t('login.username')"
            />
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              show-password
              v-model="form.password"
              prefix-icon="el-icon-lock"
              :placeholder="$t('login.password')"
            />
          </el-form-item>
          <el-form-item prop="code">
            <div class="login-input-group">
              <el-input
                clearable
                v-model="form.code"
                prefix-icon="el-icon-_vercode"
                :placeholder="$t('login.code')"
              />
              <img
                alt=""
                v-if="captcha"
                :src="captcha"
                class="login-captcha"
                @click="changeCaptcha"
              />
            </div>
          </el-form-item>
          <div class="el-form-item">
            <el-checkbox v-model="form.remember">
              {{ $t('login.remember') }}
            </el-checkbox>
          <!--
    <el-link
              type="primary"
              :underline="false"
              class="ele-pull-right"
              @click="$router.push('/forget')"
            >
              忘记密码
            </el-link>
            -->
          
          </div>
          <div class="el-form-item">
            <el-button
              size="large"
              type="primary"
              class="login-btn"
              :loading="loading"
              @click="submit"
            >
              {{ loading ? $t('login.loading') : $t('login.login') }}
            </el-button>
          </div>
          <!--
    <div class="ele-text-center" style="margin-bottom: 10px">
            <i class="login-oauth-icon el-icon-_qq" style="background: #3492ed"></i>
            <i
              class="login-oauth-icon el-icon-_wechat"
              style="background: #4daf29"
            ></i>
            <i
              class="login-oauth-icon el-icon-_weibo"
              style="background: #cf1900"
            ></i>
          </div>

          -->
          
        </el-form>


      </div>


    </div>
   


    
    <!-- 多语言切换 -->
    <div style="position: absolute; right: 30px; top: 20px">
      <i18n-icon
        :icon-style="{ fontSize: '22px', color: '#fff', cursor: 'pointer' }"
      />
    </div>
   

    
    <div class="d-fixed flex">
  <div>
   <div class="d-title">在线客服</div>
   <div class="flex-align-center d-height">
    <img :src="indexData.kf_img" class="d-img" id="img-fangda" />
    <div class="flex-column">
     <div class="d-div">在线时间：{{indexData.kf_date}}</div>
     <div class="d-div d-m-top" v-if="indexData.site_beian">
      <a href="https://beian.miit.gov.cn/" target="_blank"> {{indexData.site_beian}}</a>
     </div>
     <div class="d-div d-m-top" v-if="indexData.site_jingbeian">
      <a href="https://beian.mps.gov.cn/#/query/webSearch" target="_blank"> {{indexData.site_jingbeian}}</a>
     </div>
     <div class="d-div d-m-top" v-if="indexData.site_copyright">
       {{indexData.site_copyright}}
     </div>
    </div>
   </div>
  </div>
  <div class="flex-column" style="margin-left: 126px;"  v-if="indexData.kf_phone || indexData.kf_mailbox || indexData.kf_address">
   <div class="d-title">联系我们</div>
   <div class="d-height">
    <div class="flex-align-center d-margin-btn" v-if="indexData.kf_phone">
     <el-icon class="el-icon-phone-outline"></el-icon>
     <div class="d-div2">{{indexData.kf_phone}}</div>
    </div>
    <div class="flex-align-center d-margin-btn" v-if="indexData.kf_mailbox">
     <el-icon class="el-icon-_network"></el-icon>
     <div class="d-div2">{{indexData.kf_mailbox}}.com</div>
    </div>
    <div class="flex-align-center d-margin-btn" v-if="indexData.kf_address">
     <el-icon class="el-icon-location-outline"></el-icon>
     <div class="d-div2">{{indexData.kf_address}}</div>
    </div>
   </div>
  </div>
 </div>
    
  
 <!-- 实际项目去掉这段



     <div
      class="hidden-xs-only"
      style="position: absolute; right: 30px; bottom: 20px; z-index: 9"
    >
      <el-radio-group v-model="direction" size="mini">
        <el-radio-button label="2">居左</el-radio-button>
        <el-radio-button label="0">居中</el-radio-button>
        <el-radio-button label="1">居右</el-radio-button>
      </el-radio-group>
    </div>
    -->
   
  </div>
</template>

<script>
import "../../assets/js/canvas-nest.min";
import "../../assets/js/jigsaw.js";
  import I18nIcon from '@/layout/components/i18n-icon.vue';
  import { getToken } from '@/utils/token-util';
  import { login, getCaptcha } from '@/api/login';
  import { getIndexData,getProxyByHost} from '@/api/layout';
  export default {
    // eslint-disable-next-line vue/multi-word-component-names
    name: 'Login',
    components: { I18nIcon },
    data() {
      return {
        // 登录框方向, 0居中, 1居右, 2居左
        direction: 0,
        // 加载状态
        loading: false,
        is_sh: false,
        title:"",
        // 表单数据
        form: {
          username: '',
          password: '',
          remember: true,
          code: ''
        },
        indexData:{
          logo_title:'',
          site_login_image:'',
          site_login_background:'',
          site_login_image:''
        },
        // 验证码base64数据
        captcha: '',
        // 验证码内容, 实际项目去掉
        //text: ''
      };
    },
    computed: {
      // 表单验证规则
      rules() {
        return {
          username: [
            {
              required: true,
              message: this.$t('login.username'),
              type: 'string',
              trigger: 'blur'
            }
          ],
          password: [
            {
              required: true,
              message: this.$t('login.password'),
              type: 'string',
              trigger: 'blur'
            }
          ]
        };
      }
    },
    mounted(){
      this.$nextTick(() => {
          document.getElementsByTagName("canvas")[0].className = "index_bg";
      });  
    },
    created() {
      let host = location.host;
      //let host = 'recover.admin168.net';
      //let host = 'xhscs.dns06.com';
      //let host = 'fpsaas.admin168.net';
      getProxyByHost({host}).then((msg) => {
        if(msg!="" ){
          this.indexData = msg;
        }else{
          getIndexData().then((msg) => {
            this.indexData = msg;
            if(msg.site_login_background == ""){
              this.indexData.site_login_background = '/imgs/bg.jpg';
            }

            if(msg.site_login_image == ""){
              this.indexData.site_login_image = '/imgs/left-bg.jpg';
            }
          }).catch((e) => {
            this.$message.error(e.message);
          });
        }
        let ico = this.indexData?.site_ico;

        if(ico!=null && ico!=null && ico!=''){
          let $favicon = document.querySelector('link[rel="icon"]');
          if ($favicon !== null) {
            $favicon.href = ico;
          } else {
            $favicon = document.createElement("link");
            $favicon.rel = "icon";
            $favicon.href = ico;
            document.head.appendChild($favicon);
          }
        }
      }).catch((e) => {
        this.$message.error(e.message);
      });

      if (getToken()) {
        this.goHome();
      } else {
        this.changeCaptcha();
      }
    },
    beforeCreate() {
      document.getElementsByTagName("canvas")[0].className = "index_bg";
    },
    beforeDestroy: function () {
      document.getElementsByTagName("canvas")[0].style.zIndex = -2;
    },
    methods: {
      
      /* 提交 */
      submit() {
        this.$refs.form.validate((valid) => {
          if (!valid) {
            return false;
          }
        
          if (this.form.code.toLowerCase() !== this.text.toLowerCase()) {
            this.$message.error('验证码错误');
            this.changeCaptcha();
            return;
          }
          let host = location.host;
          //let host = 'recover.admin168.net';
          //let host = 'xhscs.dns06.com';
          //let host = 'fpsaas.admin168.net';
          let data = {
            ...this.form,
            host:host
          }
          this.loading = true;
          login(data).then((msg) => {
              this.loading = false;
              this.$message.success(msg);
              this.goHome();
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
              this.changeCaptcha();
            });
        });
      },
      /* 跳转到首页 */
      goHome() {
        this.$router.push(this.$route?.query?.from ?? '/').catch(() => {});
      },
      /* 更换图形验证码 */
      changeCaptcha() {
   
        // 这里演示的验证码是后端返回base64格式的形式, 如果后端地址直接是图片请参考忘记密码页面
        getCaptcha().then((data) => {
            this.captcha = data.base64;
            // 实际项目后端一般会返回验证码的key而不是直接返回验证码的内容, 登录用key去验证, 可以根据自己后端接口修改
            this.text = data.text;
            // 自动回填验证码, 实际项目去掉这个
            //this.form.code = this.text;
            this.$refs?.form?.clearValidate();
        }).catch((e) => {
          this.$message.error(e.message);
        });
      }
    }
  };
</script>

<style scoped>


.index_bg {
    width: 100%;
    background: rgba(0,0,0,.6)!important;
    z-index: 0!important;
}

  /* 背景 */
  .login-wrapper {
    padding: 50px 20px;
    position: relative;
    box-sizing: border-box;
    background-image: url('@/assets/bg-login.jpg');
    background-repeat: no-repeat;
    background-size: cover;
    min-height: 100vh;
  }

  .login-wrapper:before {
    content: '';
    background-color: rgba(0, 0, 0, 0.2);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  /* 卡片 */
  .login-form {
    margin: 0 auto;
    width: 920px;
    height: 420px;
    max-width: 100%;
    position: relative;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
    box-sizing: border-box;
    border-radius: 4px;
    z-index: 2;
  }


  .login-form-item {
    width: 320px;
    max-width: 100%;
   
    border-radius: 4px;
    z-index: 2;
  }

  .login-form-right .login-form {
    margin: 0 15% 0 auto;
  }

  .login-form-left .login-form {
    margin: 0 auto 0 15%;
  }

  .login-form h4 {
    text-align: center;
    margin: 0 0 25px 0;
  }

  .login-form > .el-form-item {
    margin-bottom: 25px;
  }

  /* 验证码 */
  .login-input-group {
    display: flex;
    align-items: center;
  }

  .login-input-group ::v-deep .el-input {
    flex: 1;
  }

  .login-captcha {
    height: 38px;
    width: 102px;
    margin-left: 10px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    text-align: center;
    cursor: pointer;
  }

  .login-captcha:hover {
    opacity: 0.75;
  }

  .login-btn {
    display: block;
    width: 100%;
  }

  /* 第三方登录图标 */
  .login-oauth-icon {
    color: #fff;
    padding: 5px;
    margin: 0 10px;
    font-size: 18px;
    border-radius: 50%;
    cursor: pointer;
  }

  /* 底部版权 */
  .login-copyright {
    color: #eee;
    padding-top: 20px;
    text-align: center;
    position: relative;
    z-index: 1;
  }

  /* 响应式 */
  @media screen and (min-height: 550px) {
    .login-form {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateX(-50%);
      margin-top: -220px;
    }

    .login-form-right .login-form,
    .login-form-left .login-form {
      left: auto;
      right: 15%;
      transform: translateX(0);
      margin: -220px auto auto auto;
    }

    .login-form-left .login-form {
      right: auto;
      left: 15%;
    }

    .login-copyright {
      position: absolute;
      bottom: 20px;
      right: 0;
      left: 0;
    }
  }

  @media screen and (max-width: 768px) {
    .login-form-right .login-form,
    .login-form-left .login-form {
      left: 50%;
      right: auto;
      transform: translateX(-50%);
      margin-right: auto;
    }
  }


/* 底部 */

.title-div {
 color: #ffff;
 display: flex;
 font-size: 20px;
}

a:hover{
 color:#ffff;
}
a{
 color:#ffff;
}

.d-fixed {
 width: 92%; position : fixed;
 bottom: 20px;
 left: 20px;
 position: fixed;
 z-index: 9999;
}

.d-height {
 height: 100px;
 color:#ffff;
}

.d-title {
 margin-bottom: 19px;
 color: #fff;
 font-weight: bold;
 font-size: 20px;
}

.d-margin-btn {
 margin-bottom: 17px;
}

.d-title2 {
 color: #fff;
 font-weight: bold;
 font-size: 20px;
}

.d-img {
 border-radius: 10px;
 width: 100px;
 height: 100px;
 margin-right: 10px;
}

.flex-column {
 display: flex;
 flex-direction: column;
 justify-content: center;
}

.d-m-top {
 margin-top: 10px;
}

.d-div {
 color: #fff;
 font-size: 13px;
}

.flex-between {
 display: flex;
 justify-content: space-between;
 align-items: center;
}

.flex-around {
 padding: 0 20px; display : flex;
 justify-content: space-around;
 align-items: center;
 display: flex;
}

.flex {
 display: flex;
 justify-content: center;
 align-items: center;
}

.flex-align-center {
 display: flex;
 align-items: center;
}

.d-icon-img {
 width: 20px;
    height: 20px;
    margin-right: 10px;
    font-size: 19px;
}

.d-div2{
  margin-left: 10px;
}


</style>
