<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <log-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            class="ele-btn-icon"
            @click="removeBatch"
          >
            删除
          </el-button>
        </template>

      

 

        <template v-slot:anchor_type="{ row }">
          <el-tag v-if="row.anchor_type==0" type="primary" effect="plain">默认形象</el-tag>
          <el-tag v-if="row.anchor_type==1" type="success" effect="plain">自定义形象</el-tag>
        </template>


        <template v-slot:current_status="{ row }">
        
          <el-tag v-if="row.current_status== 'success'  || row.current_status== 'copy' " type="success">生成成功</el-tag>
          <el-tag v-else-if="row.current_status== 'fail'" type="danger">生成失败</el-tag>
          <div v-if="row.faild_message " style="margin-top: 5px;font-size: 2px;">
            失败原因：<span style="font-weight: bold;color: red;">{{row.faild_message}}</span>
          </div>
          <el-tag v-else-if=" row.current_status== '' || row.current_status== 'init' || row.current_status== 'start' || row.current_status== 'pending' || row.current_status== 'process'">生成中</el-tag>
        </template>

        <template v-slot:result="{ row }">

          <video width="320" height="240" controls :src="row.local_url"  v-if="row.local_url"> </video>
          <video width="320" height="240" controls :src="row.result"  v-else-if="row.result"> </video>
          <span v-else>--</span>
        </template>


        
        <!-- 操作列 -->
        <template v-slot:action="{ row }">
         
          <el-popconfirm
            class="ele-action"
            title="确定要删除此数据吗？"
            @confirm="remove(row)"
          >
            <template v-slot:reference>
              <el-link type="danger" :underline="false" icon="el-icon-delete">
                删除
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>


  </div>
</template>

<script>
  import LogSearch from './components/log-search';
  import { list, remove  } from '@/api/screen/log';

  export default {
    name: 'ScreenLog',
    components: {
      LogSearch,
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },
          
          {
            prop: 'anchor_type',
            label: '形象类型',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 90,
            slot: 'anchor_type'
          },
          {
            prop: 'current_status',
            label: '状态',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 90,
            slot: 'current_status'
          },
          {
            prop: 'result',
            label: '结果链接',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 200,
            slot: 'result'
          },
         
          {
            prop: 'create_time',
            label: '创建时间',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 90,
           
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 150,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            showOverflowTooltip: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示分配回收员
        showRepost: false,
        //弹框名称
        //弹框名称
        id:0,
        videoName:''
      };
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return list({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id).then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
      },
    
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的数据吗?', '提示', {
          type: 'warning'
        }).then(() => {
          const loading = this.$loading({ lock: true });
          remove(this.selection.map((d) => d.id))
            .then((msg) => {
              loading.close();
              this.$message.success(msg);
              this.reload();
            })
            .catch((e) => {
              loading.close();
              this.$message.error(e.message);
            });
        }).catch(() => {});
      },
      
     
    }
  };
</script>

<style lang="scss" scoped>

  .drag {
    background: #000 !important;
    background-image: linear-gradient(#333, #999) !important;
  }

  .ele-cell-content {
   
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

</style>
