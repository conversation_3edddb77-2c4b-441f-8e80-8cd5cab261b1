import axios from '@/utils/request';


/**
 * 保存系统配置信息
 * @param data 配置数据
 */
export async function save(data) {
  const res = await axios.post('/system.config/save', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 查询用户列表
 * @param params 查询条件
 */
 export async function query(params) {

  const res = await axios.get('/system.config/query',{
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}




/**
 * 查询商户系统基础设置
 * @param params 查询条件
 */
 export async function querySystem(params) {

  const res = await axios.get('/figure.system/querySystem',{
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 保存系统配置信息
 * @param data 配置数据
 */
 export async function saveSystem(data) {
  const res = await axios.post('/figure.system/saveSystem', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 查询消费记录
 * @param data 配置数据
 */
 export async function logList(params) {
  const res = await axios.post('/system.config/logList', params);
  if (res.data.code === 0) {
      return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



