<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <user-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            class="ele-btn-icon"
            @click="removeBatch"
          >
            删除
          </el-button>
         
        </template>

        <template v-slot:balanceNameHeader>
          <span >
            {{ balanceName }}
          </span>
        </template>

        <template v-slot:brokerageNameHeader>
          <span >
            {{ brokerageName }}
          </span>
        </template>

        <template v-slot:nickname="{ row }">
          <div class="ele-cell">
            <el-avatar v-if="row.avatar" :src="row.avatar" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.nickname }} </div>
              <div class="ele-cell-desc">{{ row.telphone }} </div>
            </div>
          </div>
        </template>

        <template v-slot:p_nickname="{ row }">
          <div class="ele-cell" v-if="row.figureUser">
            <el-avatar  v-if="row.figureUser.avatar" :src="row.figureUser.avatar" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.figureUser.nickname }} </div>
              <div class="ele-cell-desc">{{ row.figureUser.telphone }} </div>
            </div>
          </div>
          <span v-else>无</span>
        </template>

        <template v-slot:is_member="{ row }">
          <el-tag v-if="row.is_member==0" type="info" effect="plain">非会员</el-tag>
          <el-tag v-if="row.is_member==1" type="success" effect="plain">会员</el-tag>
          <div>视频合成秒数：{{row.second_infinite == 0 ? row.second : '无限'}}</div>
          <div>声音高保真克隆次数：{{row.voice_twin_count}}</div>
          <div>声音高保真合成字数：{{row.high_fidelity_words_number}}</div>
          <div>ai文案仿写|提取次数：{{row.ai_copywriting_times}}</div>
          <div>专业版声音克隆次数：{{row.xunfei_sound_clone_words_number}}</div>
          <div>专业版声音合成字数：{{row.xunfei_sound_generate_words_number}}</div>
          <div>ai标题：{{row.ai_title_times}}</div>


        </template>

        <template v-slot:maturity_time="{ row }">
          <span v-if="row.maturity_time"> {{ row.maturity_time }}</span>
          <span v-else> -- </span>
        </template>

        <!-- 状态列 -->
        <template v-slot:is_freeze="{ row }">
          <el-switch
            :active-value="1"
            :inactive-value="2"
            v-model="row.is_freeze"
            @change="editStatus(row)"
          />
        </template>
        

        <template v-slot:qrcode="{ row }">
              <el-image
            style="width: 70px; height: 70px"
            :src="row.qrcode"
            fit="fit">
            
            </el-image>
            <el-link
            type="primary"
            style="margin-left: 5px;"
            :underline="false"
            @click="reloadQrcode(row)"
          >
            重新生成
          </el-link>
        </template>

        
        <!-- 操作列 -->
        <template v-slot:action="{ row }">

          <el-link
            type="success"
            :underline="false"
            icon="el-icon-_rmb"
            @click="openBalance(row)"
          >
            记录
          </el-link>

          <el-link
            type="warning"
            :underline="false"
            icon="el-icon-user"
            @click="openTeam(row)"
          >
            团队
          </el-link>
          
          <el-link
            type="primary"
            :underline="false"
            icon="el-icon-edit"
            @click="openEdit(row)"
          >
            详情
          </el-link>

          <el-popconfirm
            class="ele-action"
            title="确定要删除此数据吗？"
            @confirm="remove(row)"
          >
            <template v-slot:reference>
              <el-link type="danger" :underline="false" icon="el-icon-delete">
                删除
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
    <!-- 编辑弹窗 -->
    <user-edit :visible.sync="showEdit" :data="current"  @done="reload"/>

    <ele-modal 
      width="80%"
      :close-on-click-modal="false"
      custom-class="ele-dialog-form"
      :title="userName + balanceName"
      :visible.sync="showBalance">
      <user-balance-log :data="current" :uid="id" @done="reload"/>
    </ele-modal> 

   

    <ele-modal 
      width="80%"
      :close-on-click-modal="false"
      custom-class="ele-dialog-form"
      :title=" userName + ' 团队'"
      :visible.sync="showTeam">
      <team :data="current" :pid="id"  @done="reload"/>
    </ele-modal>


  </div>
</template>

<script>
  import UserSearch from './components/user-search';
  import UserEdit from './components/user-edit';
  import UserBalanceLog from './components/user-balance-log';
  import Team from './components/team';
  import { userList, remove, modify,resetPassword } from '@/api/user/list';

  export default {
    name: 'User',
    components: {
      UserSearch,
      UserEdit,
      UserBalanceLog,
      Team,
    },
    computed: {
      balanceName() {
        return this.$store.state.user.balance_name;
      },
      brokerageName() {
        return this.$store.state.user.brokerage_name;
      },
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            align: 'center',
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'nickname',
            label: '昵称',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'nickname',
          },
          {
            prop: 'p_nickname',
            label: '推荐人',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'p_nickname',
          },
            {
            prop: 'name',
            label: '分销等级',
            showOverflowTooltip: true,
            minWidth: 110,
        
          },
          {
            prop: 'balance',
            label: '点数',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110,
          },
          
          {
            prop: 'brokerage',
            label: '佣金',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110,
          },

          {
            prop: 'is_member',
            label: '会员状态',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'is_member',
          },
          {
            prop: 'maturity_time',
            label: '到期时间',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'maturity_time',
          },
         
          {
            prop: 'create_time',
            label: '入驻时间',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 100
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 350,
            align: 'center',
            resizable: false,
            slot: 'action',
            fixed:'right',
            hideInSetting: true,
            showOverflowTooltip: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示积分弹窗
        showBalance: false,
        // 是否显示积分弹窗
        showBalance: false,
        // 是否显示团队弹窗
        showTeam: false,
        //弹框名称
        id:0,
        userName:''
      };
    },
    methods: {
    
      beforeHandleCommand(index, row,command){
        return {
          'index': index,
          'row': row,
          'command':command
         }
      },
      onDropClick(command) {
        switch (command.command) {
          case "a"://编辑
              this.openAddNum(command.row,1);
              break;
          case "b"://充值密码
              this.openAddNum(command.row,2);
              break;
          case "c"://删除
              this.openAddNum(command.row,3);
              break;
          case "d"://删除
              this.openAddNum(command.row,4);
              break;
        }
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return userList({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },

      /* 打开编辑弹窗 */
      openCheck(row) {
        this.id = row.id;
        this.current = row;
        this.showCheck = true;
      },

      /* 打开编辑弹窗 */
      openBalance(row) {
        this.id = row.id;
        this.current = row;
        this.showBalance = true;
        this.userName = row.nickname;
      },
      /* 打开编辑弹窗 */
      openBalance(row) {
        this.id = row.id;
        this.current = row;
        this.showBalance = true;
        this.userName = row.nickname;
      },
      /* 打开编辑弹窗 */
      openTeam(row) {
        this.id = row.id;
        this.current = row;
        this.showTeam = true;
        this.userName = row.nickname;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      },
      /* 更新二维码 */
      reloadQrcode(row) {
        const loading = this.$loading({ lock: true });
        reloadQrcode({user_id:row.userId})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      },
      /* 重置合伙人密码 */
      resetPsw(row) {
        this.$confirm('确定要重置此用户的密码为"123456"吗?', '提示', {
          type: 'warning'
        })
          .then(() => {
            const loading = this.$loading({ lock: true });
            resetPassword(row.id)
              .then((msg) => {
                loading.close();
                this.$message.success(msg);
              })
              .catch((e) => {
                loading.close();
                this.$message.error(e.message);
              });
          })
          .catch(() => {});
      },
      
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的合伙人吗?', '提示', {
          type: 'warning'
        })
          .then(() => {
            const loading = this.$loading({ lock: true });
            remove(this.selection.map((d) => d.id))
              .then((msg) => {
                loading.close();
                this.$message.success(msg);
                this.reload();
              })
              .catch((e) => {
                loading.close();
                this.$message.error(e.message);
              });
          })
          .catch(() => {});
      },
     
      /* 更改状态 */
      editStatus(row) {
        const loading = this.$loading({ lock: true });
        modify({id:row.id,field:'is_freeze',value: row.is_freeze})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
          })
          .catch((e) => {
            loading.close();
            row.is_freeze = row.is_freeze == 2 ? 1 : 2;
            this.$message.error(e.message);
          });
      }
    }
  };
</script>

<style lang="scss" scoped>

  .ele-cell-content {
   
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

</style>
