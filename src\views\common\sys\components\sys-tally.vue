<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">扣点配置</div>
      <div class="ele-page-desc"> 用于扣点配置。</div>
    </div>
    <div class="ele-body">
      <el-card shadow="never">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="230px"
          style="margin: 10px auto"
        >
          <el-row>
            <el-col :sm="12">
              <el-form-item
                label="形象克隆线路一扣点:"
                prop="avatar_high_deduct"
              >
                <el-input v-model="form.avatar_high_deduct" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>

              <el-form-item
                label="形象克隆线路二扣点:"
                prop="avatar_high_deduct_two"
              >
                <el-input v-model="form.avatar_high_deduct_two" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>

              <el-form-item label="形象克隆线路三扣点:" prop="avatar_deduct">
                <el-input v-model="form.avatar_deduct" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>
              <el-form-item
                label="形象克隆线路四扣点:"
                prop="avatar_deduct_four"
              >
                <el-input v-model="form.avatar_deduct_four" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>
              <el-form-item
                label="声音克隆高保真扣点:"
                prop="voice_high_deduct"
              >
                <el-input v-model="form.voice_high_deduct" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>
              <el-form-item label="专业版声音克隆:" prop="xunfei_sound_train">
                <el-input v-model="form.xunfei_sound_train" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>
              <el-form-item label="声音克隆入门版版扣点:" prop="voice_deduct">
                <el-input v-model="form.voice_deduct" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>

              <el-form-item label="AI文案扣点:" prop="ai_create_deduct">
                <el-input v-model="form.ai_create_deduct" clearable>
                  <template slot="append">点<em>/</em>每条</template>
                </el-input>
              </el-form-item>
              <el-form-item label="AI标题扣点:" prop="ai_title_create_deduct">
                <el-input v-model="form.ai_title_create_deduct" clearable>
                  <template slot="append">点<em>/</em>每条</template>
                </el-input>
              </el-form-item>

              <el-form-item label="发布抖音扣点:" prop="douyin_video">
                <el-input v-model="form.douyin_video" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>

              <el-form-item label="IP账号采集:" prop="douyin_homepage">
                <el-input v-model="form.douyin_homepage" clearable>
                  <template slot="append">点<em>/</em>条</template>
                </el-input>
              </el-form-item>

              <!-- 新增表单项 -->
              <el-form-item label="AI立项:" prop="ai_project_deduct">
                <el-input v-model="form.ai_project_deduct" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>

              <el-form-item label="AI诊断分析:" prop="ai_diagnosis_deduct">
                <el-input v-model="form.ai_diagnosis_deduct" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>

              <el-form-item label="AI商业定位:" prop="ai_business_position_deduct">
                <el-input v-model="form.ai_business_position_deduct" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>

              <el-form-item label="AI账号包装:" prop="ai_account_package_deduct">
                <el-input v-model="form.ai_account_package_deduct" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>

              <el-form-item label="深度思考:" prop="deep_thinking_deduct">
                <el-input v-model="form.deep_thinking_deduct" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>

              <el-form-item label="爆款选题:" prop="hot_topic_deduct">
                <el-input v-model="form.hot_topic_deduct" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>

              <el-form-item label="文案编导:" prop="copywriting_director_deduct">
                <el-input v-model="form.copywriting_director_deduct" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>

              <el-form-item label="热点跟拍:" prop="hotspot_follow_deduct">
                <el-input v-model="form.hotspot_follow_deduct" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>

              <el-form-item label="小红书笔记:" prop="xiaohongshu_note_deduct">
                <el-input v-model="form.xiaohongshu_note_deduct" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>

              <el-form-item label="口播精剪:" prop="voiceover_edit_deduct">
                <el-input v-model="form.voiceover_edit_deduct" clearable>
                  <template slot="append">点<em>/</em>个</template>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :sm="12">
              <el-form-item label="视频线路一创作扣点:" prop="video_deduct">
                <el-input v-model="form.video_deduct" clearable>
                  <template slot="append">点<em>/</em>秒</template>
                </el-input>
              </el-form-item>
              <el-form-item
                label="视频线路二创作扣点:"
                prop="woni_video_deduct"
              >
                <el-input v-model="form.woni_video_deduct" clearable>
                  <template slot="append">点<em>/</em>秒</template>
                </el-input>
              </el-form-item>
              <el-form-item label="视频线路三创作扣点:" prop="composite_deduct">
                <el-input v-model="form.composite_deduct" clearable>
                  <template slot="append">点<em>/</em>分钟</template>
                </el-input>
              </el-form-item>

              <el-form-item
                label="视频线路四创作扣点:"
                prop="composite_deduct_four"
              >
                <el-input v-model="form.composite_deduct_four" clearable>
                  <template slot="append">点<em>/</em>分钟</template>
                </el-input>
              </el-form-item>

              <el-form-item
                label="声音克隆高保真合成扣点:"
                prop="high_fidelity_sound"
              >
                <el-input v-model="form.high_fidelity_sound" clearable>
                  <template slot="append">点<em>/</em>100字</template>
                </el-input>
              </el-form-item>
              <el-form-item
                label="专业版声音合成:"
                prop="xunfei_sound_generate"
              >
                <el-input v-model="form.xunfei_sound_generate" clearable>
                  <template slot="append">点<em>/</em>100字</template>
                </el-input>
              </el-form-item>

              <el-form-item label="剪辑扣点:" prop="clip_price">
                <el-input v-model="form.clip_price" clearable>
                  <template slot="append">每条</template>
                </el-input>
              </el-form-item>
              <el-form-item
                label="ai视频提取文案扣点:"
                prop="ai_video_extraction"
              >
                <el-input v-model="form.ai_video_extraction" clearable>
                  <template slot="append">点<em>/</em>条</template>
                </el-input>
              </el-form-item>
              <el-form-item label="发布视频号扣点:" prop="shipinghao_video">
                <el-input v-model="form.shipinghao_video" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>
              <el-form-item label="发布小红书扣点:" prop="xiaohongshu_video">
                <el-input v-model="form.xiaohongshu_video" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>

                 <el-form-item label="主页视频分析:" prop="douyin_homepage_video">
                <el-input v-model="form.douyin_homepage_video" clearable>
                  <template slot="append">点<em>/</em>条</template>
                </el-input>
              </el-form-item>

              <!-- 新增表单项 -->
              <el-form-item label="批量混剪:" prop="batch_mix_edit_deduct">
                <el-input v-model="form.batch_mix_edit_deduct" clearable>
                  <template slot="append">点<em>/</em>个</template>
                </el-input>
              </el-form-item>

              <el-form-item label="AI设计:" prop="ai_design_deduct">
                <el-input v-model="form.ai_design_deduct" clearable>
                  <template slot="append">点<em>/</em>张</template>
                </el-input>
              </el-form-item>

              <el-form-item label="图片精修:" prop="image_retouch_deduct">
                <el-input v-model="form.image_retouch_deduct" clearable>
                  <template slot="append">点<em>/</em>张</template>
                </el-input>
              </el-form-item>

              <el-form-item label="表情包:" prop="emoji_package_deduct">
                <el-input v-model="form.emoji_package_deduct" clearable>
                  <template slot="append">点<em>/</em>张</template>
                </el-input>
              </el-form-item>

              <el-form-item label="商品抠图:" prop="product_cutout_deduct">
                <el-input v-model="form.product_cutout_deduct" clearable>
                  <template slot="append">点<em>/</em>张</template>
                </el-input>
              </el-form-item>

              <el-form-item label="AI翻译官:" prop="ai_translator_deduct">
                <el-input v-model="form.ai_translator_deduct" clearable>
                  <template slot="append">点<em>/</em>次</template>
                </el-input>
              </el-form-item>

              <el-form-item label="图生视频720P:" prop="image_to_video_720p_deduct">
                <el-input v-model="form.image_to_video_720p_deduct" clearable>
                  <template slot="append">点<em>/</em>秒</template>
                </el-input>
              </el-form-item>

              <el-form-item label="图生视频1080P:" prop="image_to_video_1080p_deduct">
                <el-input v-model="form.image_to_video_1080p_deduct" clearable>
                  <template slot="append">点<em>/</em>秒</template>
                </el-input>
              </el-form-item>

              <el-form-item label="文生视频720P:" prop="text_to_video_720p_deduct">
                <el-input v-model="form.text_to_video_720p_deduct" clearable>
                  <template slot="append">点<em>/</em>秒</template>
                </el-input>
              </el-form-item>

              <el-form-item label="文生视频1080P:" prop="text_to_video_1080p_deduct">
                <el-input v-model="form.text_to_video_1080p_deduct" clearable>
                  <template slot="append">点<em>/</em>秒</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item>
            <el-button type="primary" :loading="loading" @click="submit">
              提交
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import { save, query } from '@/api/system/config';

export default {
  name: 'SetTally',
  data() {
    return {
      host: window.location.protocol + '//' + location.host,
      count: 0,
      // 提交状态
      loading: false,
      disabled: false,
      // 表单数据
      form: {
        voice_deduct: '',
        voice_high_deduct: '',
        avatar_deduct: '',
        avatar_high_deduct: '',
        face_deduct: '',
        video_deduct: '',
        ai_create_deduct: '',
        photo_deduct: '',
        photo_high_deduct: '',
        composite_deduct: '',
        ai_title_create_deduct: '',
        one_way_name: '',
        two_way_name: '',
        three_way_name: '',
        // 新增字段
        ai_project_deduct: '',
        ai_diagnosis_deduct: '',
        ai_business_position_deduct: '',
        ai_account_package_deduct: '',
        deep_thinking_deduct: '',
        hot_topic_deduct: '',
        copywriting_director_deduct: '',
        hotspot_follow_deduct: '',
        xiaohongshu_note_deduct: '',
        voiceover_edit_deduct: '',
        batch_mix_edit_deduct: '',
        ai_design_deduct: '',
        image_retouch_deduct: '',
        emoji_package_deduct: '',
        product_cutout_deduct: '',
        ai_translator_deduct: '',
        image_to_video_720p_deduct: '',
        image_to_video_1080p_deduct: '',
        text_to_video_720p_deduct: '',
        text_to_video_1080p_deduct: ''
      },
      // 表单验证规则
      rules: {
        voice_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        voice_high_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        avatar_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        avatar_high_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        face_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        video_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        ai_create_deduct: [
          {
            required: true,
            message: '请输入字数',
            trigger: 'blur'
          }
        ],
        photo_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        photo_high_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        composite_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        // 新增字段验证规则
        ai_project_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        ai_diagnosis_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        ai_business_position_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        ai_account_package_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        deep_thinking_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        hot_topic_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        copywriting_director_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        hotspot_follow_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        xiaohongshu_note_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        voiceover_edit_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        batch_mix_edit_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        ai_design_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        image_retouch_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        emoji_package_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        product_cutout_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        ai_translator_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        image_to_video_720p_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        image_to_video_1080p_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        text_to_video_720p_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ],
        text_to_video_1080p_deduct: [
          {
            required: true,
            message: '请输入点数',
            trigger: 'blur'
          }
        ]
      },
      // 表格列配置
      columns: [
        {
          prop: 'sign',
          label: '签名',
          showOverflowTooltip: true,
          minWidth: 110
        },
        {
          prop: 'auditResult',
          label: '状态',
          showOverflowTooltip: true,
          minWidth: 110,
          slot: 'auditResult'
        }
      ],
      // 表格选中数据
      selection: [],
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showEdit: false
    };
  },
  methods: {
    query() {
      query({ group: 'tally' })
        .then((data) => {
          if (data != null) {
            this.form = data;
            this.count = data?.count;
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },
    /* 提交 */
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true;
          save(this.form)
            .then((msg) => {
              this.loading = false;
              this.$message.success(msg);
              this.query();
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });
        } else {
          return false;
        }
      });
    },
    copy_url(content) {
      if (window.clipboardData) {
        window.clipboardData.setData('text', content);
      } else {
        (function (content) {
          //oncopy 事件在用户拷贝元素上的内容时触发。
          document.oncopy = function (e) {
            e.clipboardData.setData('text', content);
            e.preventDefault(); //取消事件的默认动作
            document.oncopy = null;
          };
        })(content);
        document.execCommand('Copy');
      }
      this.$message.success('复制成功！');
    }
  },
  mounted() {
    this.query();
  }
};
</script>
<style>
.my_input {
  width: 400px;
}

.pay_header {
  margin-bottom: 30px;
  font-size: 17px;
}
</style>
