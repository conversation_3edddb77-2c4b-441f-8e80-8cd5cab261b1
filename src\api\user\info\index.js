import axios from '@/utils/request';


/**
 * 代理首页数据
 * @param params 查询条件
 */
 export async function getAdminProfileData() {
 
  const res = await axios.get('/system.user/getAdminProfileData', {
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 代理首页数据
 * @param params 查询条件
 */
 export async function getAgencyProfileData() {
 
  const res = await axios.get('/system.user/getAgencyProfileData', {
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 代理首页数据
 * @param params 查询条件
 */
 export async function getAgencySaleData() {
 
  const res = await axios.get('/system.user/getAgencySaleData', {
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 合伙人首页数据
 * @param params 查询条件
 */
 export async function getPartnerProfileData() {
 
  const res = await axios.get('/system.user/getPartnerProfileData', {
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 合伙人首页数据
 * @param params 查询条件
 */
 export async function getPartnerSaleData() {
 
  const res = await axios.get('/system.user/getPartnerSaleData', {
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



