<template>
    <div class="ele-body">
        <el-card shadow="never">
            <!-- 搜索表单 -->
            <task-search @search="reload"/>
            <!-- 数据表格 -->
            <ele-pro-table
                    ref="table"
                    :columns="columns"
                    :datasource="datasource"
                    :selection.sync="selection"
            >

                <template v-slot:title="{ row }">
                    <div>
                        <div v-for="item in row.title">
                            <div>{{ item .title}}</div>
                        </div>
                    </div>
                </template>


              <!--  <template v-slot:video="{ row }">
                    <div>
                        <div v-for="item in row.video_info">
                            <div>
                                <video
                                        width="320"
                                        height="200"
                                        controls
                                        :src="item.result"
                                        v-if="item.result"
                                ></video>
                               </div>
                        </div>
                    </div>
                </template>-->

                <template v-slot:video="{ row }">
                    <el-link
                            type="success"
                            :underline="false"
                            icon="el-icon-more"
                            @click="openMore(row)"
                    >
                        查看视频
                    </el-link>
                </template>

                <template v-slot:material="{ row }">
                    <el-link
                            type="success"
                            :underline="false"
                            icon="el-icon-more"
                            @click="material(row)"
                    >
                        素材列表
                    </el-link>
                </template>

                <template v-slot:model="{ row }">
                    <el-link
                            type="success"
                            :underline="false"
                            icon="el-icon-more"
                            @click="model(row)"
                    >
                        模版列表
                    </el-link>
                </template>

                <template v-slot:primary_url="{ row }">
                    <div>
                        <div v-for="item in row.primary_url">
                            <div>
                                <audio
                                        controls="controls"
                                        v-if="item .primary_url"
                                        :src="item.primary_url"
                                />
                            </div>
                        </div>
                    </div>
                </template>
                <template v-slot:background_img="{ row }">
                    <!-- <div>{{row.captions_text_admission_special_img}}</div> -->
                    <img
                            style="width: 200px; height: 150px"
                            v-if="row.background_img"
                            :src="row.background_img"
                            alt=""
                    />
                    <!-- <el-avatar v-if="row.captions_text_admission_special_img" :src="row.deputy_title_text_admission_special_img" shape="square"  style="width: 110px; height: 50px" /> -->
                </template>

                <template v-slot:status="{ row }">
                    <ele-dot
                            v-if="row.status == 0"
                            type="warning"
                            effect="plain"
                            text="等待提交媒资"
                            :ripple="true"
                    />
                    <ele-dot
                            v-if="row.status == 1"
                            type="warning"
                            effect="plain"
                            text="剪辑中"
                            :ripple="true"
                    />
                    <ele-dot
                            v-if="row.status == 2"
                            type="warning"
                            effect="plain"
                            text="生成中"
                            :ripple="true"

                    />
                    <ele-dot
                            v-if="row.status == 3"
                            type="warning"
                            effect="plain"
                            text="生成中"
                            :ripple="true"
                    />
                    <ele-dot
                            v-if="row.status == 4"
                            type="success"
                            effect="plain"
                            text="成功"
                            :ripple="true"
                    />
                    <ele-dot
                            v-if="row.status == 5"
                            type="danger"
                            effect="plain"
                            text="合成失败"
                            :ripple="true"
                    />
                    <ele-dot
                            v-if="row.status == 6"
                            type="success"
                            effect="plain"
                            text="部分合成"
                            :ripple="true"
                    />
                </template>




            </ele-pro-table>
        </el-card>

        <ele-modal
                width="80%"
                :close-on-click-modal="false"
                custom-class="ele-dialog-form"
                title=" 视频数据"
                :visible.sync="showRepost"
        >
            <el-row>
                <el-col>
                    <More :data="current" :videoId="id" @done="reload"/>
                </el-col>
            </el-row>
        </ele-modal>

        <ele-modal
                width="80%"
                :close-on-click-modal="false"
                custom-class="ele-dialog-form"
                title=" 视频数据"
                :visible.sync="materialshow"
        >
            <el-row>
                <el-col>
                    <materialList :data="current" :videoId="id" @done="reload"/>
                </el-col>
            </el-row>
        </ele-modal>

        <ele-modal
                width="80%"
                :close-on-click-modal="false"
                custom-class="ele-dialog-form"
                title=" 模版列表"
                :visible.sync="modelshow"
        >
            <el-row>
                <el-col>
                    <modelList :data="current" :videoId="id" @done="reload"/>
                </el-col>
            </el-row>
        </ele-modal>

    </div>
</template>

<script>
    import TaskSearch from './components/task-search';
    import More from './components/more';
    import materialList from './components/material_list';
    import {task} from '@/api/clip/index.js';
    import modelList from './components/model_list';
    export default {
        name: 'Task',
        components: {
            TaskSearch,
            More,
            materialList,
            modelList

        },
        data() {
            return {
                // 表格列配置
                columns: [
                    {
                        columnKey: 'index',
                        type: 'index',
                        width: 45,
                        align: 'center',
                        showOverflowTooltip: true,
                        fixed: 'left'
                    },
                    {
                        prop: 'id',
                        label: '剪辑id',
                        showOverflowTooltip: true,
                        minWidth: 110,
                    },


                    {
                        prop: 'nickname',
                        label: '剪辑用户',
                        showOverflowTooltip: true,
                        minWidth: 110,

                    },

                    {
                        prop: 'title',
                        label: '标题',
                        showOverflowTooltip: true,
                        minWidth: 110,
                        slot: 'title',
                    },

                    {
                        prop: 'title_show_time',
                        label: '标题展示时间',
                        showOverflowTooltip: true,
                        minWidth: 110
                    },
                    {
                        prop: 'video',
                        label: '原视频链接',
                        showOverflowTooltip: true,
                        minWidth: 120,
                        slot: 'video',
                    },
                    {
                        prop: 'material',
                        label: '合成素材',
                        showOverflowTooltip: true,
                        minWidth: 120,
                        slot: 'material',
                    },

                    {
                        prop: 'primary_url',
                        label: '背景音乐',
                        showOverflowTooltip: true,
                        width: 220,
                        slot: 'primary_url',

                    },
                    {
                        prop: 'model',
                        label: '模版链接',
                        showOverflowTooltip: true,
                        minWidth: 220,
                        slot: 'model',
                    },

                    {
                        prop: 'status',
                        label: '状态',
                        align: 'center',
                        width: 150,
                        resizable: false,
                        slot: 'status',
                        showOverflowTooltip: true
                    },
                    {
                        prop: 'create_time',
                        label: '时间',
                        align: 'center',
                        showOverflowTooltip: true,
                        minWidth: 100
                    },
                    {
                        prop: 'error_msg',
                        label: '错误信息',
                        showOverflowTooltip: true,
                        minWidth: 180
                    },
                    {
                        columnKey: 'action',
                        label: '操作',
                        width: 150,
                        align: 'center',
                        resizable: false,
                        slot: 'action',
                        hideInSetting: true,
                        showOverflowTooltip: true
                    }
                ],
                // 表格选中数据
                selection: [],
                // 当前编辑数据
                current: null,
                currentMore: null,
                // 是否显示编辑弹窗
                showEdit: false,
                // 是否显示审核弹窗
                showCheck: false,
                // 是否显示充值弹窗
                showRecharge: false,
                showRepost: false,
                materialshow:false,
                modelshow:false,

                //弹框名称
                id: 0,
                idMore: null,
                showMore: false
            };
        },
        methods: {
            beforeHandleCommand(index, row, command) {
                return {
                    index: index,
                    row: row,
                    command: command
                };
            },

            /* 表格数据源 */
            datasource({page, limit, where, order}) {
                return task({...where, ...order, page, limit});
            },
            /* 刷新表格 */
            reload(where) {
                this.$refs.table.reload({page: 1, where: where});
            },

            openMore(row) {
                this.id = row.id;

                this.current = row;
                this.showRepost = true;
            },
            material(row) {
                this.id = row.id;

                this.current = row;
                this.materialshow = true;
            },
            model(row) {
                this.id = row.id;

                this.current = row;
                this.modelshow = true;
            },
            /* 删除 */
            remove(row) {
                const loading = this.$loading({lock: true});
                remove(row.id)
                    .then((msg) => {
                        loading.close();
                        this.$message.success(msg);
                        this.reload();
                    })
                    .catch((e) => {
                        loading.close();
                        this.$message.error(e.message);
                    });
            },
            /* 更新二维码 */

            /* 更改状态 */
            editStatus(row, statusValue) {
                const loading = this.$loading({lock: true});
                updateGoodsStatus({id: row.id, field: 'status', value: statusValue})
                    .then((msg) => {
                        loading.close();
                        this.$message.success(msg);
                        this.reload();
                    })
                    .catch((e) => {
                        loading.close();
                        this.$message.error(e.message);
                    });
            }
        }
    };
</script>
