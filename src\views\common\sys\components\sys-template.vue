<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">通知配置</div>
      <div class="ele-page-desc"> 用于订阅消息配置场景。 </div>
    </div>
    <div class="ele-body">
      <el-card shadow="never">
        <el-alert
          show-icon
          type="warning"
          :closable="false"
          title="注意："
          class="ele-alert-border"
          style="margin-bottom: 20px"
        >
          <div class="ele-text">
            <div>
              请将小程序服务类目选择为：<b class="ele-text-warning"
                >信息查询<em>休闲娱乐</em></b
              >，未选类目将会导致订阅消息不可用。
            </div>
            <div style="margin-top: 10px">
              您的小程序目前服务类目为：<b>{{ industrytext }}</b> <em></em
              ><em></em>
            </div>
          </div>
        </el-alert>
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="200px"
          style="max-width: 90%; margin: 10px auto"
        >
          <table class="ele-table ele-table-border" style="margin-bottom: 20px">
            <colgroup>
              <col width="150" />
              <col width="300" />
              <col />
            </colgroup>
            <thead>
              <tr>
                <th
                  >开通会员成功通知
                  <el-link
                    class="content"
                    type="primary"
                    @click="showImgViewer(0)"
                    >查看</el-link
                  >
                  <el-image-viewer
                    v-if="imgViewerVisible"
                    :on-close="closeImgViewer"
                    :url-list="srcList"
                  />
                </th>
                <th style="text-align: center">模板信息（类目：信息查询）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>模板ID</td>
                <td>
                  <el-form-item
                    label="开通会员成功通知模板ID:"
                    prop="open_member_template"
                  >
                    <template-select
                      placeholder="请选择"
                      v-model="form.open_member_template"
                      :data="templateList"
                      :value="form.open_member_template"
                    />
                  </el-form-item>
                </td>
              </tr>
            </tbody>
          </table>
          <table class="ele-table ele-table-border" style="margin-bottom: 20px">
            <colgroup>
              <col width="150" />
              <col width="300" />
              <col />
            </colgroup>
            <thead>
              <tr>
                <th
                  >充值成功通知
                  <el-link
                    class="content"
                    type="primary"
                    @click="showImgViewer(1)"
                    >查看</el-link
                  >
                  <el-image-viewer
                    v-if="imgViewerVisible"
                    :on-close="closeImgViewer"
                    :url-list="srcList"
                  />
                </th>
                <th style="text-align: center">模板信息（类目：休闲娱乐）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>模板ID</td>
                <td>
                  <el-form-item
                    label="充值成功通知模板ID:"
                    prop="recharge_success_template"
                  >
                    <template-select
                      placeholder="请选择"
                      v-model="form.recharge_success_template"
                      :data="templateList"
                      :value="form.recharge_success_template"
                    />
                  </el-form-item>
                </td>
              </tr>
            </tbody>
          </table>
          <table class="ele-table ele-table-border" style="margin-bottom: 20px">
            <colgroup>
              <col width="150" />
              <col width="300" />
              <col />
            </colgroup>
            <thead>
              <tr>
                <th
                  >消费成功通知
                  <el-link
                    class="content"
                    type="primary"
                    @click="showImgViewer(2)"
                    >查看</el-link
                  >
                  <el-image-viewer
                    v-if="imgViewerVisible"
                    :on-close="closeImgViewer"
                    :url-list="srcList"
                  />
                </th>
                <th style="text-align: center">模板信息（类目：信息查询）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>模板ID</td>
                <td>
                  <el-form-item
                    label="消费成功通知模板ID:"
                    prop="consume_success_template"
                  >
                    <template-select
                      placeholder="请选择"
                      v-model="form.consume_success_template"
                      :data="templateList"
                      :value="form.consume_success_template"
                    />
                  </el-form-item>
                </td>
              </tr>
            </tbody>
          </table>

          <table class="ele-table ele-table-border" style="margin-bottom: 20px">
            <colgroup>
              <col width="150" />
              <col width="300" />
              <col />
            </colgroup>
            <thead>
              <tr>
                <th
                  >申请结果通知
                  <el-link
                    class="content"
                    type="primary"
                    @click="showImgViewer(3)"
                    >查看</el-link
                  >
                  <el-image-viewer
                    v-if="imgViewerVisible"
                    :on-close="closeImgViewer"
                    :url-list="srcList"
                  />
                </th>
                <th style="text-align: center">模板信息（类目：休闲娱乐）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>模板ID</td>
                <td>
                  <el-form-item
                    label="申请结果通知模板ID:"
                    prop="apply_result_template"
                  >
                    <template-select
                      placeholder="请选择"
                      v-model="form.apply_result_template"
                      :data="templateList"
                      :value="form.apply_result_template"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>温馨提示</td>
                <td>
                  <el-form-item
                    label="申请结果温馨提示:"
                    prop="apply_result_remark"
                  >
                    <el-input
                      v-model="form.apply_result_remark"
                      placeholder="请输入申请结果温馨提示"
                      clearable
                    />
                    <div class="text-info">填充模板 thing5.DATA 的值</div>
                  </el-form-item>
                </td>
              </tr>
            </tbody>
          </table>

          <table class="ele-table ele-table-border" style="margin-bottom: 20px">
            <colgroup>
              <col width="150" />
              <col width="300" />
              <col />
            </colgroup>
            <thead>
              <tr>
                <th
                  >佣金到账通知
                  <el-link
                    class="content"
                    type="primary"
                    @click="showImgViewer(4)"
                    >查看</el-link
                  >
                  <el-image-viewer
                    v-if="imgViewerVisible"
                    :on-close="closeImgViewer"
                    :url-list="srcList"
                  />
                </th>
                <th style="text-align: center">模板信息（类目：休闲娱乐）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>模板ID</td>
                <td>
                  <el-form-item
                    label="佣金到账通知模板ID:"
                    prop="brokerage_arrival_template"
                  >
                    <template-select
                      placeholder="请选择"
                      v-model="form.brokerage_arrival_template"
                      :data="templateList"
                      :value="form.brokerage_arrival_template"
                    />
                  </el-form-item>
                </td>
              </tr>
            </tbody>
          </table>

          <table class="ele-table ele-table-border" style="margin-bottom: 20px">
            <colgroup>
              <col width="150" />
              <col width="300" />
              <col />
            </colgroup>
            <thead>
              <tr>
                <th
                  >申请提现通知
                  <el-link
                    class="content"
                    type="primary"
                    @click="showImgViewer(5)"
                    >查看</el-link
                  >
                  <el-image-viewer
                    v-if="imgViewerVisible"
                    :on-close="closeImgViewer"
                    :url-list="srcList"
                  />
                </th>
                <th style="text-align: center">模板信息（类目：休闲娱乐）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>模板ID</td>
                <td>
                  <el-form-item
                    label="申请提现通知模板ID:"
                    prop="withdraw_apply_template"
                  >
                    <template-select
                      placeholder="请选择"
                      v-model="form.withdraw_apply_template"
                      :data="templateList"
                      :value="form.withdraw_apply_template"
                    />
                  </el-form-item>
                </td>
              </tr>
            </tbody>
          </table>

          <table class="ele-table ele-table-border" style="margin-bottom: 20px">
            <colgroup>
              <col width="150" />
              <col width="300" />
              <col />
            </colgroup>
            <thead>
              <tr>
                <th
                  >提现结果通知
                  <el-link
                    class="content"
                    type="primary"
                    @click="showImgViewer(6)"
                    >查看</el-link
                  >
                  <el-image-viewer
                    v-if="imgViewerVisible"
                    :on-close="closeImgViewer"
                    :url-list="srcList"
                  />
                </th>
                <th style="text-align: center">模板信息（类目：休闲娱乐）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>模板ID</td>
                <td>
                  <el-form-item
                    label="提现结果通知模板ID:"
                    prop="withdraw_result_template"
                  >
                    <template-select
                      placeholder="请选择"
                      v-model="form.withdraw_result_template"
                      :data="templateList"
                      :value="form.withdraw_result_template"
                    />
                  </el-form-item>
                </td>
              </tr>
            </tbody>
          </table>

          <table class="ele-table ele-table-border" style="margin-bottom: 20px">
            <colgroup>
              <col width="150" />
              <col width="300" />
              <col />
            </colgroup>
            <thead>
              <tr>
                <th
                  >审核通过通知
                  <el-link
                    class="content"
                    type="primary"
                    @click="showImgViewer(7)"
                    >查看</el-link
                  >
                  <el-image-viewer
                    v-if="imgViewerVisible"
                    :on-close="closeImgViewer"
                    :url-list="srcList"
                  />
                </th>
                <th style="text-align: center">模板信息（类目：信息查询）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>模板ID</td>
                <td>
                  <el-form-item
                    label="审核通过通知模板ID:"
                    prop="check_success_template"
                  >
                    <template-select
                      placeholder="请选择"
                      v-model="form.check_success_template"
                      :data="templateList"
                      :value="form.check_success_template"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>备注</td>
                <td>
                  <el-form-item
                    label="审核通过通知备注:"
                    prop="check_success_remark"
                  >
                    <el-input
                      v-model="form.check_success_remark"
                      placeholder="请输入审核通过通知备注"
                      clearable
                    />
                    <div class="text-info">填充模板 thing5.DATA 的值</div>
                  </el-form-item>
                </td>
              </tr>
            </tbody>
          </table>

          <table class="ele-table ele-table-border" style="margin-bottom: 20px">
            <colgroup>
              <col width="150" />
              <col width="300" />
              <col />
            </colgroup>
            <thead>
              <tr>
                <th
                  >审核驳回通知
                  <el-link
                    class="content"
                    type="primary"
                    @click="showImgViewer(8)"
                    >查看</el-link
                  >
                  <el-image-viewer
                    v-if="imgViewerVisible"
                    :on-close="closeImgViewer"
                    :url-list="srcList"
                  />
                </th>
                <th style="text-align: center">模板信息（类目：信息查询）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>模板ID</td>
                <td>
                  <el-form-item
                    label="审核驳回通知模板ID:"
                    prop="check_fail_template"
                  >
                    <template-select
                      placeholder="请选择"
                      v-model="form.check_fail_template"
                      :data="templateList"
                      :value="form.check_fail_template"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>温馨提醒</td>
                <td>
                  <el-form-item
                    label="审核驳回通知温馨提醒:"
                    prop="check_fail_remark"
                  >
                    <el-input
                      v-model="form.check_fail_remark"
                      placeholder="请输入审核驳回通知温馨提醒"
                      clearable
                    />
                    <div class="text-info">填充模板 thing5.DATA 的值</div>
                  </el-form-item>
                </td>
              </tr>
            </tbody>
          </table>

          <table class="ele-table ele-table-border" style="margin-bottom: 20px">
            <colgroup>
              <col width="150" />
              <col width="300" />
              <col />
            </colgroup>
            <thead>
              <tr>
                <th
                  >视频生成成功提醒
                  <el-link
                    class="content"
                    type="primary"
                    @click="showImgViewer(9)"
                    >查看</el-link
                  >
                  <el-image-viewer
                    v-if="imgViewerVisible"
                    :on-close="closeImgViewer"
                    :url-list="srcList"
                  />
                </th>
                <th style="text-align: center">模板信息（类目：AI问答）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>模板ID</td>
                <td>
                  <el-form-item
                    label="视频生成成功提醒模板ID:"
                    prop="generate_success_template"
                  >
                    <template-select
                      placeholder="请选择"
                      v-model="form.generate_success_template"
                      :data="templateList"
                      :value="form.generate_success_template"
                    />
                  </el-form-item>
                </td>
              </tr>
            </tbody>
          </table>



   <table class="ele-table ele-table-border" style="margin-bottom: 20px">
            <colgroup>
              <col width="150" />
              <col width="300" />
              <col />
            </colgroup>
            <thead>
              <tr>
                <th
                  >视频生成失败提醒
                  <el-link
                    class="content"
                    type="primary"
                    @click="showImgViewer(10)"
                    >查看</el-link
                  >
                  <el-image-viewer
                    v-if="imgViewerVisible"
                    :on-close="closeImgViewer"
                    :url-list="srcList"
                  />
                </th>
                <th style="text-align: center">模板信息（类目：AI问答）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>模板ID</td>
                <td>
                  <el-form-item
                    label="视频生成失败提醒模板ID:"
                    prop="generate_fail_template"
                  >
                    <template-select
                      placeholder="请选择"
                      v-model="form.generate_fail_template"
                      :data="templateList"
                      :value="form.generate_fail_template"
                    />
                  </el-form-item>
                </td>
              </tr>
            </tbody>
          </table>

          
   <table class="ele-table ele-table-border" style="margin-bottom: 20px">
            <colgroup>
              <col width="150" />
              <col width="300" />
              <col />
            </colgroup>
            <thead>
              <tr>
                <th
                  >任务完成提醒
                  <el-link
                    class="content"
                    type="primary"
                    @click="showImgViewer(11)"
                    >查看</el-link
                  >
                  <el-image-viewer
                    v-if="imgViewerVisible"
                    :on-close="closeImgViewer"
                    :url-list="srcList"
                  />
                </th>
                <th style="text-align: center">模板信息（类目：服饰内衣）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>模板ID</td>
                <td>
                  <el-form-item
                    label="任务完成提醒模板ID:"
                    prop="generate_task_template"
                  >
                    <template-select
                      placeholder="请选择"
                      v-model="form.generate_task_template"
                      :data="templateList"
                      :value="form.generate_task_template"
                    />
                  </el-form-item>
                </td>
              </tr>
            </tbody>
          </table>
          <el-form-item>
            <el-button type="primary" :loading="loading" @click="submit">
              提交
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import EleImageUpload from 'ele-admin/es/ele-image-upload';
import TemplateSelect from './components/template-select';
import { query, save } from '@/api/system/config';
const DEFAULT_FORM = {
  open_member_template: '',
  recharge_success_template: '',
  consume_success_template: '',
  apply_result_template: '',
  apply_result_remark: '',
  brokerage_arrival_template: '',
  withdraw_apply_template: '',
  withdraw_result_template: '',
  check_success_template: '',
  check_success_remark: '',
  check_fail_template: '',
  check_fail_remark: '',
  generate_success_template: '',
  generate_fail_template: '',
  generate_task_template: ''
};
export default {
  name: 'SysTemplate',
  components: {
    TemplateSelect,
    EleImageUpload,
    'el-image-viewer': () =>
      import('element-ui/packages/image/src/image-viewer')
  },
  data() {
    return {
      imgViewerVisible: false,
      srcList: [
        '/imgs/template/open_member.png',
        '/imgs/template/recharge_success.png',
        '/imgs/template/consume_success.png',
        '/imgs/template/apply_result.png',
        '/imgs/template/brokerage_arrival.png',
        '/imgs/template/withdraw_apply.png',
        '/imgs/template/withdraw_result.png',
        '/imgs/template/check_success.png',
        '/imgs/template/check_fail.png',
        '/imgs/template/generate_success.png',
         '/imgs/template/generate_fail.png',
           '/imgs/template/task.png'

      ],

      value: '',
      placeholder: {
        type: String,
        default: '请选择'
      },
      // 提交状态
      loading: false,
      // 表单数据
      form: {
        ...DEFAULT_FORM
      },

      // 表单验证规则
      rules: {
        client_key: [
          {
            required: true,
            message: '请输入KEY',
            trigger: 'blur'
          }
        ],
        client_secret: [
          {
            required: true,
            message: '请输入秘钥',
            trigger: 'blur'
          }
        ]
      },
      templateList: [],
      industrytext: '',
      // 表单验证信息
      validMsg: ''
    };
  },
  methods: {
    showImgViewer(index) {
      this.imgViewerVisible = true;
      const m = (e) => {
        e.preventDefault();
      };
      document.body.style.overflow = 'hidden';
      document.addEventListener('touchmove', m, false); // 禁止页面滑动

      let tempImgList = [...this.srcList];
      let before = [];
      for (let i = 0; i <= index; i++) {
        before.push(tempImgList[i]);
      }

      let after = [];
      for (let i = index; i < tempImgList.length; i++) {
        after.push(tempImgList[i]);
      }

      this.srcList = after.concat(before);
    },
    closeImgViewer() {
      this.srcList = [
        '/imgs/template/open_member.png',
        '/imgs/template/recharge_success.png',
        '/imgs/template/consume_success.png',
        '/imgs/template/apply_result.png',
        '/imgs/template/brokerage_arrival.png',
        '/imgs/template/withdraw_apply.png',
        '/imgs/template/withdraw_result.png',
        '/imgs/template/check_success.png',
        '/imgs/template/check_fail.png',
        '/imgs/template/generate_success.png',
         '/imgs/template/generate_fail.png',
           '/imgs/template/task.png'
      ];
      this.imgViewerVisible = false;
      const m = (e) => {
        e.preventDefault();
      };
      document.body.style.overflow = 'auto';
      document.removeEventListener('touchmove', m, true);
    },

    /* 提交 */
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true;
          this.loading = true;
          save(this.form)
            .then((msg) => {
              this.loading = false;
              this.$message.success(msg);
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });
        } else {
          return false;
        }
      });
    }
  },
  mounted() {
    query({ group: 'template' })
      .then((data) => {
        if (data != null) {
          if (data.set != null) {
            this.form = data.set;
          }

          this.industrytext = data.industrytext;
          this.templateList = data.list;
        }
      })
      .catch((e) => {
        this.$message.error(e.message);
      });
  }
};
</script>
