<!-- 搜索表单 -->
<template>
  <div class="ele-table-search">
    <el-form
      :model="form"
      label-width="77px"
      class="ele-table-search-form"
      @keyup.enter.native="search"
      @submit.native.prevent
    >
      <el-row :gutter="15">
        <el-col :lg="6" :md="12">
          <el-form-item label="视频类型:">
            <el-select
              clearable
              v-model="form.type_id"
              placeholder="请选择视频类型"
              class="ele-fluid"
            >
              <el-option label="全部" value="" />
              <el-option
                v-for="item in categoryList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12">
          <div class="ele-table-search-actions">
            <el-button
              type="primary"
              icon="el-icon-search"
              class="ele-btn-icon"
              @click="search"
            >
              搜索
            </el-button>
            <el-button @click="reset">重置</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  import { getVideoTypeList } from '@/api/finishVideo';

  export default {
    name: 'VideoSearch',
    data() {
      return {
        // 搜索表单数据
        form: {
          type_id: ''
        },
        // 分类列表
        categoryList: []
      };
    },
    mounted() {
      this.loadCategoryList();
    },
    methods: {
      /* 加载分类列表 */
      async loadCategoryList() {
        try {
          this.categoryList = await getVideoTypeList();
        } catch (error) {
          this.$message.error(error.message);
        }
      },
      /* 搜索 */
      search() {
        this.$emit('search', this.form);
      },
      /* 重置 */
      reset() {
        this.form = {
          type_id: ''
        };
        this.$emit('search', this.form);
      },
      /* 刷新分类列表 */
      refresh() {
        this.loadCategoryList();
      }
    }
  };
</script>
