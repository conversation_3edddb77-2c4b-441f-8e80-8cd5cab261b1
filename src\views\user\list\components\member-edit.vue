<!-- 用户编辑弹窗 -->
<template>
  <ele-modal
    width="740px"
    :visible="visible"
    :append-to-body="true"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    title="修改会员"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" label-width="140px">
     
      <el-form-item  label="是否会员:" prop="is_member">
        <el-radio-group v-model="form.is_member">
          <el-radio :label="1" value="1" >是</el-radio>
          <el-radio :label="0" value="0" >否</el-radio>
        </el-radio-group>
      </el-form-item>

    
      <el-form-item  label="到期时间:" prop="maturity_time">
        <el-date-picker
          v-model="form.maturity_time"
          type="date"
          placeholder="选择日期">
        </el-date-picker>
      </el-form-item>

      <el-form-item  label="视频合成无限:" prop="second_infinite">
        <el-radio-group  v-model="form.second_infinite" size="small"  @change="updateSecond">
          <el-radio-button :label=0 :value=0>否</el-radio-button>
          <el-radio-button :label=1 :value=1>是</el-radio-button>
        </el-radio-group>
      </el-form-item>

      <el-form-item  label="视频合成秒数:" prop="second" v-if="form.second_infinite == 0">
        <el-input-number
          v-model="form.second"
          placeholder="请输入秒数"
        />
      </el-form-item> 

      <el-form-item  label="声音高保真次数:" prop="voice_twin_count">
        <el-input-number
          v-model="form.voice_twin_count"
          placeholder="请输入次数"
        />
      </el-form-item>

      
      <el-form-item  label="声音高保真合成字数:" prop="high_fidelity_words_number">
        <el-input-number
          v-model="form.high_fidelity_words_number"
          placeholder="请输入字数"
        />
      </el-form-item>

 <el-form-item  label="Ai文案生成次数:" prop="ai_copywriting_times">
        <el-input-number
          v-model="form.ai_copywriting_times"
          placeholder="请输入次数"
        />
      </el-form-item>

 <el-form-item  label="专业版声音克隆次数:" prop="xunfei_sound_clone_words_number">
        <el-input-number
          v-model="form.xunfei_sound_clone_words_number"
          placeholder="请输入次数"
        />
      </el-form-item>

          
      <el-form-item  label="专业版声音合成字数:" prop="xunfei_sound_generate_words_number">
        <el-input-number
          v-model="form.xunfei_sound_generate_words_number"
          placeholder="请输入字数"
        />
      </el-form-item>
        <el-form-item  label="Ai标题生成次数:" prop="ai_title_times">
            <el-input-number
                    v-model="form.ai_title_times"
                    placeholder="请输入次数"
            />
        </el-form-item>

       
    </el-form>
    <template v-slot:footer>
      <el-button @click="updateVisible(false)">取消 </el-button>
      <el-button type="primary" :loading="loading" @click="save"
        >保存
      </el-button>
    </template>
    
  </ele-modal>
</template>

<script>
 
  const DEFAULT_FORM = {
    id: 0,
    is_member: 0,
    maturity_time: '',
    second: 0,
    voice_twin_count: 0,
    second_infinite: 0,
      xunfei_sound_generate_words_number:0,
      high_fidelity_words_number:0,
      ai_copywriting_times:0,
      xunfei_sound_clone_words_number:0,
      ai_title_times:0,
  };
  import { update } from '@/api/user/list';
  export default {
    name: 'MemberEdit',
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
       
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false,
        
      };
    },
    methods: {
      updateSecond(){
        if(this.form.second_infinite == 1){
          this.form.second = 0;
        }
        
      },
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }

          this.loading = true;
          const data = {
            ...this.form,
          };

          update(data).then((msg) => {
            this.loading = false;
            this.$message.success(msg);
            this.updateVisible(false);
            this.$emit('done');
          }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
          });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      }
    },
    watch: {
      visible(visible) {
        if (visible) {
          if (this.data) {

            this.$util.assignObject(this.form, {
              ...this.data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
