<!-- 用户编辑弹窗 -->
<template>
  <ele-modal
    width="740px"
    :visible="visible"
    :append-to-body="true"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? '修改轮播图' : '添加轮播图'"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
    
     
      <el-form-item label="图片:" prop="pic_url">
        <span slot="label">
          图片
          
          <el-tooltip placement="top">
            <div slot="content" v-if="templateType == 1">
              模板1：建议上传750px * 950px尺寸,或者等比例图片
            </div>
            <div slot="content" v-if="templateType == 2">
              模板2：建议上传750px * 950px尺寸,或者等比例图片
            </div>
            <div slot="content" v-if="templateType == 3">
              模板3：建议上传710px * 340px尺寸,或者等比例图片
            </div>
             <div slot="content" v-if="templateType == 4">
              模板4：建议上传750px * 730尺寸,或者等比例图片
            </div>
             <div slot="content" v-if="templateType == 5">
              模板5：建议上传750px * 730尺寸,或者等比例图片
            </div>
            <i class="el-icon-question" />
          </el-tooltip>
        </span>  
        <!--<ele-image-upload v-model="img" :limit="1" :drag="true" :multiple="false"  @upload="onUpload" /> --> 
          <div class="ele-image-upload-list">
          <div class="ele-image-upload-item ele-image-upload-button" @click="modalPicTap('dan','pic_url','图片')">
            <div>
              <div tabindex="0" class="el-upload el-upload--text">
                <div class="el-upload-dragger">
                  <i class="el-icon-plus ele-image-upload-icon"></i>
                </div>
                  <div class="ele-image-upload-item" style="margin:0 0 0 0;" v-if="form.pic_url!=''">
                  <div class="el-image" >
                    <img :src="form.pic_url" width="100%" height="100%"  class="el-image__inner" style="object-fit: cover;">
                  </div>
                  <div class="ele-image-upload-close" @click="handleRemove('pic_url')"><i class="el-icon-close"></i></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-form-item>

      
      <el-form-item label="跳转类型:" prop="type">
        <el-radio-group v-model="form.type">
          <el-radio :label=1 value=1 >内部小程序链接</el-radio>
          <el-radio :label="2" value="2">外部小程序链接</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="form.type == 1 || form.type == 2 " label="跳转链接:" prop="url">
       
        <ele-table-select
          v-if="form.type == 1"
          ref="select"
          clearable
          v-model="form.url"
          placeholder="请选择"
          value-key="url"
          label-key="name"
          :table-config="tableConfig"
          :popper-width="580"
        >
          <template v-slot:toolbar>
            <pages-search   @search="search" />
          </template>
        </ele-table-select>

        <el-input
          v-if="form.type == 2"
          clearable
          v-model="form.url"
          placeholder="请输入外部小程序链接"
        />
        
      </el-form-item>

      <el-form-item v-if="form.type == 2 " label="小程序appid:" prop="appid">
        <el-input
          clearable
          v-model="form.appid"
          placeholder="请输入外部小程序appid"
        />
      </el-form-item>


      <el-form-item label="排序号:" prop="sort">
        <el-input-number
          :min="0"
          v-model="form.sort"
          placeholder="请输入排序号"
          controls-position="right"
          class="ele-fluid ele-text-left"
        />
      </el-form-item>

      <el-form-item label="展示状态:">
        <el-switch
          :active-value="1"
          :inactive-value="2"
          v-model="form.is_display"
        />
        <el-tooltip
          placement="top"
          content="选择不可见则前端页面不显示该数据"
        >
          <i
            class="el-icon-_question"
            style="vertical-align: middle; margin-left: 8px"
          ></i>
        </el-tooltip>
      </el-form-item>

       
    </el-form>
    <template v-slot:footer>
      <el-button @click="updateVisible(false)">取消 </el-button>
      <el-button type="primary" :loading="loading" @click="save"
        >保存
      </el-button>
    </template>
    <uploadPictures
      :isChoice="isChoice"
      :visible.sync="modalPic"
      @getPic="getPic"
      :gridBtn="gridBtn"
      :gridPic="gridPic"
      :title="modalTitle"
    ></uploadPictures>
  </ele-modal>
</template>

<script>
  import PagesSearch from "@/views/common/pages/pages-search";
  import uploadPictures from "@/components/uploadPictures";
  import EleImageUpload from 'ele-admin/es/ele-image-upload';
  import TinymceEditor from '@/components/TinymceEditor';
  import { add,update } from '@/api/banner';
  import { getPages } from '@/api/layout';
  const DEFAULT_FORM = {
    id: 0,
    mold:1,
    type: 1,
    b_type:1,
    pic_url :[],
    url :'',
    appid :'',
    is_display :1,
    sort :0
  };

  export default {
    name: 'BannerEdit',
    components: { PagesSearch,EleImageUpload,uploadPictures,TinymceEditor },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object,
      b_type:0
    },
    computed: {
      templateType() {
        return this.$store.state.user.template_type;
      }
    },
    data() {
      return {
        modalTitle:'选择图片',
        modalPic: false,
        isChoice: "单选",
        gridBtn: {
          xl: 4,
          lg: 8,
          md: 8,
          sm: 8,
          xs: 8,
        },
        gridPic: {
          xl: 6,
          lg: 8,
          md: 12,
          sm: 12,
          xs: 12,
        },
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          title: [
            {
              required: true,
              message: '请输入轮播图标题',
              trigger: 'blur'
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false,
        tableConfig: {
          datasource: ({ page, limit, where, order }) => {
            return getPages({ ...where, ...order, page, limit });
          },
          columns: [
            {
                columnKey: 'index',
                type: 'index',
                width: 45,
                align: 'center'
            },
            {
              prop: 'name',
              label: '名称',
              showOverflowTooltip: true,
              minWidth: 110,
              //slot: 'nickname'
            },
            {
              prop: 'url',
              label: '路径',
              showOverflowTooltip: true,
              minWidth: 110
            }
          ],
          rowClickChecked: true,
          rowClickCheckedIntelligent: false,
          toolkit: ['reload', 'columns'],
          size: 'small',
          toolStyle: { padding: '0 10px' }
        }
      };
    },
    methods: {
      search(where) {
        // debugger
        this.$refs.select.reload({
          where: where,
          page: 1
        });
      },
      // 选择图片
      modalPicTap(tit, picTit,openTitle) {
        this.modalTitle = openTitle;
        this.isChoice = tit === "dan" ? "单选" : "多选";
        this.picTit = picTit;
        this.modalPic = true;
      },
      // 选中图片
      getPic(pc) {
        switch (this.picTit) {
          case "pic_url":
            this.form.pic_url = pc.satt_dir;
            break;
          case "background":
            this.form.background = pc.satt_dir;
            break;
        }
        this.modalPic = false;
      },
      //删除图片
      handleRemove(field) {
        this.form[field] = '';
      },

      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }

          this.loading = true;
          const data = {
            ...this.form,
          };

          data.b_type = this.b_type;

          const saveOrUpdata = this.isUpdate ? update : add;

          saveOrUpdata(data).then((msg) => {
            this.loading = false;
            this.$message.success(msg);
            this.updateVisible(false);
            this.$emit('done',{ b_type: this.b_type });
          }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
          });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      }
    },
    watch: {
      visible(visible) {
        if (visible) {
          if (this.data) {

            this.$util.assignObject(this.form, {
              ...this.data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
