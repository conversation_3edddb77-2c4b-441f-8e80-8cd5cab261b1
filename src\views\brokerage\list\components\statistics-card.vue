<!-- 统计卡片 -->
<template>
  <el-row :gutter="15">
    <el-col :md="6" :sm="12">
      <el-card shadow="never" class="monitor-count-card">
        <el-tag size="large" class="ele-tag-round">
          <i class="el-icon-s-flag"></i>
        </el-tag>
        <div class="monitor-count-card-num ele-text-heading">
          <vue-count-up :end-val="wait * 1"  :options="countUpOptions" />
        </div>
        <div class="monitor-count-card-text ele-text-secondary">
          待提现金额
        </div>
      </el-card>
    </el-col>
    <el-col :md="6" :sm="12">
      <el-card shadow="never" class="monitor-count-card">
        <el-tag size="large" type="warning" class="ele-tag-round">
          <i class="el-icon-s-check"></i>
        </el-tag>
        <div class="monitor-count-card-num ele-text-heading">
          <vue-count-up :end-val="check * 1"  :options="countUpOptions" />
        </div>
        <div class="monitor-count-card-text ele-text-secondary">
          待审核金额
        </div>
      </el-card>
    </el-col>
    <el-col :md="6" :sm="12">
      <el-card shadow="never" class="monitor-count-card">
        <el-tag size="large" type="success" class="ele-tag-round">
          <i class="el-icon-success"></i>
        </el-tag>
        <div class="monitor-count-card-num ele-text-heading">
          <vue-count-up :end-val="already * 1"  :options="countUpOptions" />
        </div>
        <div class="monitor-count-card-text ele-text-secondary">
          已打款金额
        </div>
      </el-card>
    </el-col>
    <el-col :md="6" :sm="12">
      <el-card shadow="never" class="monitor-count-card">
        <el-tag size="large" type="danger" class="ele-tag-round">
          <i class="el-icon-error"></i>
        </el-tag>
        <div class="monitor-count-card-num ele-text-heading">
          <vue-count-up :end-val="refuse * 1"  :options="countUpOptions" />
        </div>
        <div class="monitor-count-card-text">已驳回金额</div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
  import VueCountUp from 'vue-countup-v2';
  import { brokerageStatis } from '@/api/brokerage';
  export default {
    name: 'StatisticsCard',
    components: { VueCountUp },
    data() {
      return {
        wait:0,
        check:0,
        already:0,
        refuse:0,
        countUpOptions: {
          useEasing: true,
          useGrouping: true,
          decimalPlaces:2,
          separator: ',',
          decimal: '.',
          prefix: '',
          suffix: ''
        },
      };
    },
    mounted() {
      brokerageStatis().then((data) => {
        if(data != null){
          this.wait = data.wait;  
          this.check = data.check;  
          this.already = data.already;  
          this.refuse = data.refuse;  
        }
        
      }).catch((e) => {
        this.$message.error(e.message);
      });
     },
    watch: {
      type(){
        brokerageStatis().then((data) => {
          if(data != null){
            this.data = data;  
          }
          
        }).catch((e) => {
          this.$message.error(e.message);
        });
      }
    },

  };
</script>

<style scoped>
  .monitor-count-card ::v-deep .el-card__body {
    padding-top: 18px;
    text-align: center;
    position: relative;
  }

  .monitor-count-card ::v-deep .el-tag {
    border-color: transparent;
    font-size: 15px;
  }

  .monitor-count-card .monitor-count-card-num {
    font-weight: 500;
    font-size: 32px;
    margin-top: 12px;
  }

  .monitor-count-card .monitor-count-card-text {
    font-size: 12px;
    margin: 10px 0;
  }

  .monitor-count-card .monitor-count-card-trend {
    font-weight: 600;
    padding: 6px 0;
  }

  .monitor-count-card .monitor-count-card-trend > i {
    font-size: 12px;
    font-weight: 600;
    margin-right: 5px;
  }

  .monitor-count-card .monitor-count-card-tips {
    position: absolute;
    top: 15px;
    right: 15px;
    cursor: pointer;
  }
</style>
