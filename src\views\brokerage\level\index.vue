<template>
  <div class="ele-body">
      <MemberSearch @search="reload" />
    <el-card shadow="never">
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            新增
          </el-button>
        </template>

        <!-- 状态列 -->
           <template v-slot:status="{ row }">
          <el-switch
            :active-value="2"
            :inactive-value="1"
            v-model="row.status"
            @change="editStatus(row)"
          />
        </template>
      
        <template v-slot:one_level_income="{ row }">
          {{ row.one_level_income }}%
        </template>
         <template v-slot:two_level_income="{ row }">
          {{ row.two_level_income }}%
        </template>
         <template v-slot:one_level_repurchase_income="{ row }">
          {{ row.one_level_repurchase_income }}%
        </template>
         <template v-slot:two_level_repurchase_income="{ row }">
          {{ row.two_level_repurchase_income }}%
        </template>
        
         <template v-slot:consumption_amount="{ row }">
               <ele-dot v-if="row.distribution_mode==2"  :text="'下级购买'+row.package_count+'个'+row.package_name"  />
                     <ele-dot v-if="row.distribution_mode==1"  :text="'消费满'+row.consumption_amount"  />
        
        </template>
        <template v-slot:is_shareholder_swich ="{ row }">
               <ele-dot v-if="row.is_shareholder_swich==1" text="是" />
                     <ele-dot v-if="row.is_shareholder_swich==2"  text="否"  />
        
        </template>

        <!-- 操作列 -->
        <template v-slot:action="{ row }">
          <el-link
            type="primary"
            :underline="false"
            icon="el-icon-edit"
            @click="openEdit(row)"
          >
            修改
          </el-link>
        </template>
      </ele-pro-table>
    </el-card>
    <!-- 编辑弹窗 -->
    <level-edit :visible.sync="showEdit" :data="current" @done="reload" />
  </div>
</template>

<script>
import LevelEdit from './components/level-edit';
 import MemberSearch from './components/user-search';
import { levelList, remove, modify, sortChange } from '@/api/brokerage';
import Sortable from 'sortablejs';

export default {
  name: 'Member',
  components: {
    LevelEdit,
       MemberSearch,

  },
  data() {
    return {
      // 表格列配置
      columns: [
      
        {
          columnKey: 'index',
          type: 'index',
          width: 45,
          align: 'center',
          showOverflowTooltip: true,
          fixed: 'left'
        },
        {
          prop: 'name',
          label: '名称',
          showOverflowTooltip: true,
          minWidth: 110
        },

        {
          prop: 'level',
          label: '等级',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110
        },
          {
          prop: 'status',
          label: '状态',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110,
           slot: 'status'
        },
        {
          prop: 'consumption_amount',
          label: '升级条件',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110,
          slot: 'consumption_amount'
        },

        {
          prop: 'desc',
          label: '描述',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110
        },

        {
          prop: 'one_level_income',
          label: '首购直推',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110,
          slot: 'one_level_income'
        },
        {
          prop: 'two_level_income',
          label: '首购间推',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110,
          slot: 'two_level_income'
        },
        {
          prop: 'one_level_repurchase_income',
          label: '复购直推',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110,
          slot: 'one_level_repurchase_income'
        },
        {
          prop: 'two_level_repurchase_income',
          label: '复购间推',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110,
          slot: 'two_level_repurchase_income'
        },

       {
          prop: 'is_shareholder_swich',
          label: '是否开启股东分红',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110,
          slot: 'is_shareholder_swich'
        },
        {
          prop: 'create_time',
          label: '创建时间',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 100
        },
        {
          columnKey: 'action',
          label: '操作',
          width: 350,
          align: 'center',
          resizable: false,
          slot: 'action',
          fixed: 'right',
          hideInSetting: true,
          showOverflowTooltip: true
        }
      ],
      // 表格选中数据
      selection: [],
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showEdit: false,
      // 是否显示积分弹窗
      showBalance: false,
      // 是否显示积分弹窗
      showBalance: false,
      // 是否显示团队弹窗
      showTeam: false,
      //弹框名称
      id: 0,
      userName: ''
    };
  },
  methods: {
    rowDrop() {
      const tbody = document.querySelector('.el-table__body-wrapper tbody');
      const _this = this;
      Sortable.create(tbody, {
        forceFallback: true,
        dragClass: 'drag',
        delay: 100,
        animation: 1000,
        onEnd({ newIndex, oldIndex }) {
          console.log(newIndex);
          console.log(oldIndex);
          //更换表格数据 后续拖动坐标才会正确
          let data = _this.$refs.table.getData();

          let nid = data[newIndex].id;
          let oid = data[oldIndex].id;
          const currRow = data.splice(oldIndex, 1)[0];
          data.splice(newIndex, 0, currRow);

          sortChange(data)
            .then((msg) => {})
            .catch((e) => {
              _this.$refs.table.reload();
            });
          //去数据库更换排序
        }
      });
    },
    /* 表格数据源 */
    datasource({ page, limit, where, order }) {
      return levelList({ ...where, ...order, page, limit });
    },
    /* 刷新表格 */
    reload(where) {
      this.$refs.table.reload({ page: 1, where: where });
    },
    /* 打开编辑弹窗 */
    openEdit(row) {

      this.current = row;
      this.showEdit = true;
    },
    /* 删除 */
    remove(row) {
      const loading = this.$loading({ lock: true });
      remove(row.id)
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
    },

    /* 一键默认 */
    doDefault() {
      this.$confirm(
        '请谨慎使用该功能，一旦使用则会变成系统默认内容。确定一键默认？',
        '提示',
        {
          type: 'warning'
        }
      )
        .then(() => {
          const loading = this.$loading({ lock: true });
          doDefault()
            .then((msg) => {
              loading.close();
              this.$message.success(msg);
              this.reload();
            })
            .catch((e) => {
              loading.close();
              this.$message.error(e.message);
            });
        })
        .catch(() => {});
    },

    /* 批量删除 */
    removeBatch() {
      if (!this.selection.length) {
        this.$message.error('请至少选择一条数据');
        return;
      }
      this.$confirm('确定要删除选中的数据吗?', '提示', {
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading({ lock: true });
          remove(this.selection.map((d) => d.id))
            .then((msg) => {
              loading.close();
              this.$message.success(msg);
              this.reload();
            })
            .catch((e) => {
              loading.close();
              this.$message.error(e.message);
            });
        })
        .catch(() => {});
    },

    /* 更改状态 */
    editStatus(row) {
      const loading = this.$loading({ lock: true });
      modify({ id: row.id, field: 'status', value: row.status })
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          row.status = row.status == 1 ? 2 : 1;
          this.$message.error(e.message);
        });
    },
    /* 更改状态 */
    editRecommend(row) {
      const loading = this.$loading({ lock: true });
      modify({ id: row.id, field: 'is_recommend', value: row.is_recommend })
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          row.is_recommend = row.is_recommend == 1 ? 2 : 1;
          this.$message.error(e.message);
        });
    }
  },
  mounted() {
    this.rowDrop();
  }
};
</script>

<style>
.drag {
  background: #000 !important;
  background-image: linear-gradient(#333, #999) !important;
}
</style>
