import axios from '@/utils/request';



/**
 * 添加分类
 * @param data 机构信息
 */
export async function addCate(data) {
  const res = await axios.post('/system.uploadFile/addCate', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 删除分类
 * @param params 查询条件
 */
 export async function delCate(params) {
 
  const res = await axios.get('/system.uploadFile/delCate', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除文件
 * @param
 */
 export async function delFile(data) {
  const res = await axios.post('/system.uploadFile/delFile', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}



