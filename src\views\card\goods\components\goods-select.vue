<template>
  <ele-table-select
    ref="select"
    :clearable="true"
    size="medium"
    :value="value"
    placeholder="请选择"
    value-key="id"
    label-key="name"
    prefix-icon="el-icon-search"
    :table-config="tableConfig"
    @select="updateValue"
    :popper-width="900"
    :popper-height="180"
  >
    <template v-slot:toolbar>
      <div style="max-width: 200px">
        <el-input
          clearable
          size="small"
          v-model="keywords"
          placeholder="输入关键字搜索"
          prefix-icon="el-icon-search"
          @blur="search"
        />
      </div>
    </template>

    <template v-slot:name="{ row }">
      <div class="ele-cell">
        <el-avatar :src="row.pic_url" shape="square" :size="40" />
        <div class="ele-cell-content">
          <div class="ele-cell-title">{{ row.name }}</div>
        </div>
      </div>
    </template>


    <template v-slot:day="{ row }">
      <div>天数：{{row.day}}</div>
      <div>视频合成秒数：{{row.second_infinite == 0 ? row.second : '无限'}}</div>
      <div>声音高保真次数：{{row.voice_twin_count}}</div>

      <div>声音高保真合成字数：{{row.high_fidelity_words_number}}</div>
      <div>ai文案仿写|提取次数：{{row.ai_copywriting_times}}</div>
      <div>专业版声音克隆次数：{{row.xunfei_sound_clone_words_number}}</div>
      <div>专业版声音合成字数：{{row.xunfei_sound_generate_words_number}}</div>
      <div>ai标题：{{row.ai_title_times}}</div>
    </template>
  
  </ele-table-select>
  
</template>

<script>
  import { list } from '@/api/card/goods';
  export default {
    name: 'Pages',
    props: {
      // 修改回显的数据
      value: undefined
    },
    data() {
      return {
        keywords:'',
        // 搜索表单
        where: {
          is_display:1,
          keywords: ''
        },
        tableConfig: {
          datasource: ({ page, limit, where, order }) => {
            where.is_display = 1;
            where.keywords = this.keywords;
            where.card_type = 1;
            return list({ ...where, ...order, page, limit });
          },
          columns: [
            {
              columnKey: 'index',
              type: 'index',
              width: 45,
              align: 'center'
            },
            {
              prop: 'name',
              label: '商品',
              showOverflowTooltip: true,
              minWidth: 140,
              slot: 'name'
            },
            {
              prop: 'money',
              label: '价格',
              align: 'center',
              showOverflowTooltip: true,
              minWidth: 90
            },
            {
              prop: 'day',
              label: '会员天数',
              align: 'center',
              showOverflowTooltip: true,
              minWidth: 150,
              slot: 'day'
            },
            {
              prop: 'point',
              label: '赠送点数',
              align: 'center',
              showOverflowTooltip: true,
              minWidth: 90,
            },
            {
              prop: 'quantity',
              label: '卡密数量',
              align: 'center',
              showOverflowTooltip: true,
              minWidth: 90
            },
            {
              prop: 'inventory',
              label: '库存',
              align: 'center',
              showOverflowTooltip: true,
              minWidth: 90
            },
            {
              prop: 'sold',
              label: '已售',
              align: 'center',
              showOverflowTooltip: true,
              minWidth: 90
            },
          ],
          pageSize:3,
          pageSizes:[3,10, 20, 30, 40, 50, 100],
          rowClickChecked: true,
          rowClickCheckedIntelligent: false,
          toolkit: ['reload', 'columns'],
          size: 'large',
          toolStyle: { padding: '0 10px' }
        }
      };
    },
    methods: {
      reload(){
        this.$refs.select.reload();
      },
      updateValue(data) {
        this.$emit('done', data);
      },
      // 搜索
      search(where) {
        where.is_display = 1;
        where.keywords = this.keywords;
        this.$refs.select.reload({
          where: where,
          page: 1
        });
      }
    }
  };
</script>
<style lang="less" scoped>
 
 .drag {
    background: #000 !important;
    background-image: linear-gradient(#333, #999) !important;
  }

  .ele-cell-content {
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

</style>

