<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">用户设置</div>
    </div>
    <div class="ele-body" style="padding-bottom: 71px;background: var(--color-white);">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="130px"
        @keyup.enter.native="submit"
        @submit.native.prevent
      >
        <el-card shadow="never" body-style="padding: 22px 22px 0 22px;">


          <el-row :gutter="15">
            <el-col>
              <el-form-item  label="新人赠送:" prop="new_open">
                <el-radio-group v-model="form.new_open">
                  <el-radio :label="1" value="1" >显示</el-radio>
                  <el-radio :label="2" value="2" >隐藏</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item  :label="'新人赠送'+balanceName+':'" prop="new_get" style="width:380px;">
                <el-input type="number" step="0.01" v-model="form.new_get" :placeholder="'请输入'+balanceName" clearable >
                  <template slot="append">{{ balanceName }}</template>
                </el-input>
              </el-form-item>
              <el-form-item label="新人赠送图片:" prop="new_pic">
                <span slot="label">
                  赠送图片
                  <el-tooltip placement="top">
                    <div slot="content">
                      建议上传700px * 650px尺寸,或者等比例图片
                    </div>
                    <i class="el-icon-question" />
                  </el-tooltip>
                </span>  
                <div class="ele-image-upload-list">
                  <div class="ele-image-upload-item ele-image-upload-button" @click="modalPicTap('dan','new_pic','新人赠送图片')">
                    <div>
                      <div tabindex="0" class="el-upload el-upload--text">
                        <div class="el-upload-dragger">
                          <i class="el-icon-plus ele-image-upload-icon"></i>
                        </div>
                          <div class="ele-image-upload-item" style="margin:0 0 0 0;" v-if="form.new_pic!=''">
                          <div class="el-image" >
                            <img :src="form.new_pic" width="100%" height="100%"  class="el-image__inner" style="object-fit: cover;">
                          </div>
                          <div class="ele-image-upload-close" @click="handleRemove('new_pic')"><i class="el-icon-close"></i></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
   
        
          
          <el-form-item>
            <el-button type="primary" :loading="loading" @click="submit">
              提交
            </el-button>
          </el-form-item>
        </el-card>
        
       <uploadPictures
          :isChoice="isChoice"
          :visible.sync="modalPic"
           @getPic="getPic"
          :gridBtn="gridBtn"
          :gridPic="gridPic"
          :title="modalTitle"
        ></uploadPictures>
        
      </el-form>
    </div>
  </div>
  
</template>

<script>
  import uploadPictures from "@/components/uploadPictures";
  import EleImageUpload from 'ele-admin/es/ele-image-upload';
  import { query ,save } from '@/api/user/set';
  const DEFAULT_FORM = {
    new_open:2,
    new_get: '',
    new_pic: '',
  };

  export default {
    name: 'FormAdvanced',
    components: { EleImageUpload,uploadPictures },
    computed: {
      balanceName() {
        return this.$store.state.user.balance_name;
      }
    },
    data() {
      return {
        modalTitle:'',
        modalPic: false,
        isChoice: "单选",
        gridBtn: {
          xl: 4,
          lg: 8,
          md: 8,
          sm: 8,
          xs: 8,
        },
        gridPic: {
          xl: 6,
          lg: 8,
          md: 12,
          sm: 12,
          xs: 12,
        },
        // 表单提交状态
        loading: false,
        // 表单数据
        form: {
          ...DEFAULT_FORM
        },
        // 表单验证规则
        rules: {
          name: [
            {
              required: true,
              message: '请输入仓库名',
              trigger: 'blur'
            }
          ]
        },
        // 表单验证信息
        validMsg: '',
      };
    },
    mounted() {
      query().then((msg) => {
        if(msg != null){
          this.form = msg;
        }
      })
      .catch((e) => {
        this.$message.error(e.message);
      });
    },
    methods: {
      // 选择图片
      modalPicTap(tit, picTit,openTitle) {
        this.modalTitle = openTitle;
        this.isChoice = tit === "dan" ? "单选" : "多选";
        this.picTit = picTit;
        this.modalPic = true;
      },
      // 选中图片
      getPic(pc) {
        switch (this.picTit) {
          case "new_pic":
            this.form.new_pic = pc.satt_dir;
            break;
        }
        this.modalPic = false;
      },
      //删除图片
      handleRemove(field) {
        this.form[field] = '';
      },
      /* 表单提交 */
      submit() {
        this.$refs['form'].validate((valid, obj) => {
          if (valid) {
            this.validMsg = '';
            this.loading = true;
            const data = {
            ...this.form
            };
            save(data).then((msg) => {
              this.loading = false;
              this.$message.success(msg);
            }).catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });
          } else {
            this.validMsg = ` 共有校验 ${Object.keys(obj).length} 项不通过`;
            return false;
          }
        });
      }
    }
  };
</script>
