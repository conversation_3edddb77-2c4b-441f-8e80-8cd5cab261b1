<!-- 用户编辑弹窗 -->
<template>
  <ele-modal
    width="740px"
    :visible="visible"
    :append-to-body="true"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? '修改合伙人' : '添加合伙人'"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="140px">
     
      <el-form-item  label="合伙人名称:" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入合伙人名称"
        />
      </el-form-item>

      <el-form-item  label="合伙人电话:" prop="telphone">
        <el-input
          v-model="form.telphone"
          placeholder="请输入合伙人电话"
        />
      </el-form-item>


      <el-form-item label="审核状态:" prop="is_status">
        <el-radio-group v-model="form.is_status">
          <el-radio :label="1" :value="1">待审核</el-radio>
          <el-radio :label="2" :value="2">审核通过</el-radio>
          <el-radio :label="3" :value="3">审核拒绝</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="form.is_status=='3'" label="拒绝原因:" prop="refuse">
        <el-input v-model="form.refuse" label="拒绝原因"></el-input>
      </el-form-item>

      <el-form-item label="冻结状态:">
        <el-switch
          :active-value="2"
          :inactive-value="1"
          v-model="form.is_freeze"
        />
        <el-tooltip
          placement="top"
          content="选择冻结则合伙人不可操作"
        >
          <i
            class="el-icon-_question"
            style="vertical-align: middle; margin-left: 8px"
          ></i>
        </el-tooltip>
      </el-form-item>

      <!-- <el-form-item label="推荐状态:">
        <el-switch
          :active-value="1"
          :inactive-value="2"
          v-model="form.is_recommend"
        />
        <el-tooltip
          placement="top"
          content="选择不推荐则前端页面不推荐该数据"
        >
          <i
            class="el-icon-_question"
            style="vertical-align: middle; margin-left: 8px"
          ></i>
        </el-tooltip>
      </el-form-item> -->

       
    </el-form>
    <template v-slot:footer>
      <el-button @click="updateVisible(false)">取消 </el-button>
      <el-button type="primary" :loading="loading" @click="save"
        >保存
      </el-button>
    </template>
    <uploadPictures
      :isChoice="isChoice"
      :visible.sync="modalPic"
      @getPic="getPic"
      :gridBtn="gridBtn"
      :gridPic="gridPic"
      :title="modalTitle"
    ></uploadPictures>
  </ele-modal>
</template>

<script>
  import PagesSearch from "@/views/common/pages/pages-search";
  import uploadPictures from "@/components/uploadPictures";
  import EleImageUpload from 'ele-admin/es/ele-image-upload';
  import TinymceEditor from '@/components/TinymceEditor';
  import { add,update } from '@/api/partner/list';
  import { getPages } from '@/api/layout';
  const DEFAULT_FORM = {
    id: 0,
    name: '',
    telphone: '',
    is_status :1,
    refuse :'',
    is_freeze:1,
  };

  export default {
    name: 'PartnerEdit',
    components: { PagesSearch,EleImageUpload,uploadPictures,TinymceEditor },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        modalTitle:'选择图片',
        modalPic: false,
        isChoice: "单选",
        gridBtn: {
          xl: 4,
          lg: 8,
          md: 8,
          sm: 8,
          xs: 8,
        },
        gridPic: {
          xl: 6,
          lg: 8,
          md: 12,
          sm: 12,
          xs: 12,
        },
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          name: [
            {
              required: true,
              message: '请输入合伙人名称',
              trigger: 'blur'
            }
          ],
          day: [
            {
              required: true,
              message: '请输入合伙人天数',
              trigger: 'blur'
            }
          ],
          money: [
            {
              required: true,
              message: '请输入合伙人价格',
              trigger: 'blur'
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false,
        tableConfig: {
          datasource: ({ page, limit, where, order }) => {
            return getPages({ ...where, ...order, page, limit });
          },
          columns: [
            {
                columnKey: 'index',
                type: 'index',
                width: 45,
                align: 'center'
            },
            {
              prop: 'name',
              label: '名称',
              showOverflowTooltip: true,
              minWidth: 110,
              //slot: 'nickname'
            },
            {
              prop: 'url',
              label: '路径',
              showOverflowTooltip: true,
              minWidth: 110
            }
          ],
          rowClickChecked: true,
          rowClickCheckedIntelligent: false,
          toolkit: ['reload', 'columns'],
          size: 'small',
          toolStyle: { padding: '0 10px' }
        }
      };
    },
    methods: {
      search(where) {
        // debugger
        this.$refs.select.reload({
          where: where,
          page: 1
        });
      },
      // 选择图片
      modalPicTap(tit, picTit,openTitle) {
        this.modalTitle = openTitle;
        this.isChoice = tit === "dan" ? "单选" : "多选";
        this.picTit = picTit;
        this.modalPic = true;
      },
      // 选中图片
      getPic(pc) {
        switch (this.picTit) {
          case "pic_url":
            this.form.pic_url = pc.satt_dir;
            break;
        }
        this.modalPic = false;
      },
      //删除图片
      handleRemove() {
        this.form.pic_url = '';
      },

      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }

          this.loading = true;
          const data = {
            ...this.form,
          };

          const saveOrUpdata = this.isUpdate ? update : add;

          saveOrUpdata(data).then((msg) => {
            this.loading = false;
            this.$message.success(msg);
            this.updateVisible(false);
            this.$emit('done');
          }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
          });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      }
    },
    watch: {
      visible(visible) {
        if (visible) {
          if (this.data) {

            this.$util.assignObject(this.form, {
              ...this.data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
