<template>
  <el-select
    clearable
    :value="value"
    class="ele-block"
    :placeholder="placeholder"
    @input="updateValue"
  >
    <el-option
      v-for="item in data"
      :key="item.id"
      :value="item.id"
      :label="item.name"
    />
  </el-select>
</template>

<script>
  import { user } from '@/api/user/list';

  export default {
    name: 'UserSelect',
    props: {
      value:'',
      // 选中的数据(v-model)
      placeholder: {
        type: String,
        default: '请选择用户'
      },
    },
    data() {
      return {
        data: [],
        
      };
    },
    created() {
      user().then((res) => {
        this.data = res.list;
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    methods: {
      updateValue(value) {
        this.$emit('input', value);
      }
    }
  };
</script>
