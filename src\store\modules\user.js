/**
 * 登录状态管理
 */
import { formatMenus, toTreeData, formatTreeData } from 'ele-admin';
import { USER_MENUS } from '@/config/setting';
import { getUserInfo } from '@/api/layout';
import { querySystem } from '@/api/system/config';


export default {
  namespaced: true,
  state: {
    // 当前登录用户信息
    info: null,
    // 当前登录用户的菜单
    menus: null,
    // 当前登录用户的权限
    authorities: [],
    // 当前登录用户的角色
    roles: [],
    // 回收币名称
    balance_name: [],
    // 模板类型
    template_type: [],
  },
  mutations: {
    // 设置登录用户的信息
    setUserInfo(state, info) {
      state.info = info;
    },
    // 设置登录用户的菜单
    setMenus(state, menus) {
      state.menus = menus;
    },
    // 设置登录用户的权限
    setAuthorities(state, authorities) {
      state.authorities = authorities;
    },
    // 设置登录用户的角色
    setRoles(state, roles) {
      state.roles = roles;
    },
    // 设置登录用户的信息
    setBalanceName(state, balance_name) {
      state.balance_name = balance_name;
    },
    // 设置登录用户的信息
    setBrokerageName(state, brokerage_name) {
      state.brokerage_name = brokerage_name;
    },
    // 设置登录用户的信息
    setTemplateType(state, template_type) {
      state.template_type = template_type;
    },
  },
  actions: {
    /**
     * 请求用户信息、权限、角色、菜单
     */
    async fetchUserInfo({ commit }) {

      const system = await querySystem().catch(() => void 0);

      // 模板类型
      let templateType = system?.template_type ?  system.template_type: 1;
      commit('setTemplateType', templateType);


      // 余额名称
      let name = system?.balance_name ?  system.balance_name:'点数';
      commit('setBalanceName', name);

      // 佣金名称
      let b_name = system?.brokerage_name ?  system.brokerage_name:'佣金';
      commit('setBrokerageName', b_name);


      const result = await getUserInfo().catch(() => void 0);
      if (!result) {
        return {};
      }
      // 用户信息
      commit('setUserInfo', result);
      // 用户权限
      const authorities =
        result.authorities
          ?.filter((d) => !!d.authority)
          ?.map((d) => d.authority) ?? [];
      commit('setAuthorities', authorities);
      // 用户角色
      const roles = result.roles?.map((d) => d.roleCode) ?? [];
      commit('setRoles', roles);
      // 用户菜单, 过滤掉按钮类型并转为 children 形式
      const { menus, homePath } = formatMenus(
        USER_MENUS ??
          toTreeData({
            data: result.authorities?.filter((d) => d.menuType === 0),
            idField: 'menuId',
            parentIdField: 'parentId'
          })
      );
      commit('setMenus', menus);
      return { menus, homePath };
    },
    /**
     * 更新用户信息
     */
    setInfo({ commit }, value) {
      commit('setUserInfo', value);
    },
    /**
     * 更新菜单的badge
     */
    setMenuBadge({ commit, state }, { path, value, color }) {
      const menus = formatTreeData(state.menus, (m) => {
        if (path === m.path) {
          return {
            ...m,
            meta: {
              ...m.meta,
              badge: value,
              badgeColor: color
            }
          };
        }
        return m;
      });
      commit('setMenus', menus);
    },
    /**
     * 更新余额名称
     */
     setBalanceName({ commit }, value) {
      commit('setBalanceName', value);
    },
    /**
     * 更新佣金名称
     */
    setBrokerageName({ commit }, value) {
      commit('setBrokerageName', value);
    },
    /**
     * 更新模板类型
     */
     setTemplateType({ commit }, value) {
      commit('setTemplateType', value);
    },
  }
};
