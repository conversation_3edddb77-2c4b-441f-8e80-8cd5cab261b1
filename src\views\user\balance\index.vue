<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <balance-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            class="ele-btn-icon"
            @click="removeBatch"
          >
            删除
          </el-button>
         
        </template>


        <!-- 订单编号 -->
        <template v-slot:log_no="{ row }">
          <span v-if="row.log_no"> {{ row.log_no }}</span>
          <span v-else> --- </span>
        </template>

        <template v-slot:user="{ row }">
          <div class="ele-cell">
            <el-avatar :src="row.figureUser.avatar" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.figureUser.nickname }} </div>
              <div class="ele-cell-desc">{{ row.figureUser.telphone }} </div>
            </div>
          </div>
        </template>
       

        <template v-slot:way="{ row }">
          <ele-dot v-if="row.way==0"  text="后台操作" :ripple="true" />
          <ele-dot v-else-if="row.way==1"  text="点数充值" :ripple="true" />
          <ele-dot v-else-if="row.way==2"  text="新人赠送" :ripple="true" />
          <ele-dot v-else-if="row.way==3"  text="声音克隆" :ripple="true" />
          <ele-dot v-else-if="row.way==4"  text="形象克隆" :ripple="true" />
          <ele-dot v-else-if="row.way==6"  text="视频高级创作" :ripple="true" />
          <ele-dot v-else-if="row.way==7"  text="购买会员" :ripple="true" />
          <ele-dot v-else-if="row.way==8"  text="AI文案" :ripple="true" />
          <ele-dot v-else-if="row.way==9"  text="照片克隆" :ripple="true" />
          <ele-dot v-else-if="row.way==10"  text="线路三视频创作" :ripple="true" />
          <ele-dot v-else-if="row.way==11"  text="二次剪辑" :ripple="true" />
          <ele-dot v-else-if="row.way==12"  text="AI视频提取文案" :ripple="true" />
          <ele-dot v-else-if="row.way==13"  text="视频高级创作2" :ripple="true" />
            <ele-dot v-else-if="row.way==14"  text="高保真合成音频" :ripple="true" />
             <ele-dot v-else-if="row.way==15"  text="专业版声音克隆" :ripple="true" />
              <ele-dot v-else-if="row.way==16"  text="专业版声音合成" :ripple="true" />
              <ele-dot v-else-if="row.way==17"  text="ai标题" :ripple="true" />
              <ele-dot v-else-if="row.way==18"  text="发布抖音视频扣点" :ripple="true" />
              <ele-dot v-else-if="row.way==19"  text="发布快手视频扣点" :ripple="true" />
              <ele-dot v-else-if="row.way==20"  text="发布视频号扣点" :ripple="true" />
                <ele-dot v-else-if="row.way==21"  text="发布小红薯扣点" :ripple="true" />
                 <ele-dot v-else-if="row.way==22"  text="获取抖音二维码" :ripple="true" />
                  <ele-dot v-else-if="row.way==23"  text="抖音授权" :ripple="true" />
                   <ele-dot v-else-if="row.way==24"  text="获取视频号二维码" :ripple="true" />
                    <ele-dot v-else-if="row.way==25"  text="视频号授权" :ripple="true" />
                     <ele-dot v-else-if="row.way==26"  text="获取小红薯二维码" :ripple="true" />
                      <ele-dot v-else-if="row.way==27"  text="小红薯授权" :ripple="true" />
                        <ele-dot v-else-if="row.way==28"  text="线路四合成" :ripple="true" />
                          <ele-dot v-else-if="row.way==29"  text="获取主页视频" :ripple="true" />
                            <ele-dot v-else-if="row.way==31"  text="主页视频分析" :ripple="true" />
                                <ele-dot v-else-if="row.way==32"  text="IP账号采集" :ripple="true" />
        </template>

        <template v-slot:money="{ row }">
          <el-tag  v-if="row.type==1 && row.b_type == 1"  type="success">+{{ row.money}}点</el-tag>
          <el-tag  v-if="row.type==1 && row.b_type == 3"  type="success">+{{ row.money}}秒</el-tag>
            <el-tag  v-if="row.type==1 && row.b_type == 4"  type="danger">+{{ row.money}}字</el-tag>
              <el-tag  v-if="row.type==1 && row.b_type == 2"  type="success">+{{ row.money}}次</el-tag>
          <el-tag  v-if="row.type==2 && row.b_type == 1"  type="danger">-{{ row.money}}点</el-tag> 
          <el-tag  v-if="row.type==2 && row.b_type == 2"  type="danger">-{{ row.money}}次</el-tag> 
          <el-tag  v-if="row.type==2 && row.b_type == 3"  type="danger">-{{ row.money}}秒</el-tag> 
          <el-tag  v-if="row.type==2 && row.b_type == 4"  type="danger">-{{ row.money}}字</el-tag>


            <el-tag  v-if="row.type==3 && row.b_type == 1"  type="success">+{{ row.money}}点</el-tag>
          <el-tag  v-if="row.type==3 && row.b_type == 3"  type="success">+{{ row.money}}秒</el-tag>
            <el-tag  v-if="row.type==3 && row.b_type == 4"  type="danger">+{{ row.money}}字数</el-tag> 
              <el-tag  v-if="row.type==3 && row.b_type == 2"  type="success">+{{ row.money}}次</el-tag>
        

                  
        </template>

        
        <!-- 操作列 -->
        <template v-slot:action="{ row }">
          <el-popconfirm
            class="ele-action"
            title="确定要删除此数据吗？"
            @confirm="remove(row)"
          >
            <template v-slot:reference>
              <el-link type="danger" :underline="false" icon="el-icon-delete">
                删除
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
  </div>
</template>

<script>
  import BalanceSearch from './components/balance-search';
  import { balanceLog, remove, modify,resetPassword } from '@/api/user/balance';

  export default {
    name: 'Partner',
    components: {
      
      BalanceSearch
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            align: 'center',
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'log_no',
            label: '记录单号',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'log_no'
          },
          {
            prop: 'user',
            label: '用户',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'user'
          },
          {
            prop: 'way',
            label: '方式',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'way',
          },
          {
            prop: 'desc',
            label: '描述',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110,
          },
          {
            prop: 'current',
            label: '操作前余额',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'money',
            label: '操作金额',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'money',
          },
          {
            prop: 'card',
            label: '卡密',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'create_time',
            label: '操作时间',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 100,
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 150,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            showOverflowTooltip: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示审核弹窗
        showCheck: false,
        // 是否显示充值弹窗
        showBalance: false,
        // 是否显示价格弹窗
        showPrice: false,
        //弹框名称
        id:0,
      };
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return balanceLog({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },

      /* 打开编辑弹窗 */
      openCheck(row) {
        this.id = row.id;
        this.current = row;
        this.showCheck = true;
      },

      /* 打开编辑弹窗 */
      openBalance(row) {
        this.id = row.id;
        this.current = row;
        this.showBalance = true;
      },
      /* 打开价格弹窗 */
      openPrice(row) {
        this.current = row;
        this.showPrice = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      },
      /* 更新二维码 */
      reloadQrcode(row) {
        const loading = this.$loading({ lock: true });
        reloadQrcode({user_id:row.userId})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      },

      
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的合伙人吗?', '提示', {
          type: 'warning'
        })
          .then(() => {
            const loading = this.$loading({ lock: true });
            remove(this.selection.map((d) => d.id))
              .then((msg) => {
                loading.close();
                this.$message.success(msg);
                this.reload();
              })
              .catch((e) => {
                loading.close();
                this.$message.error(e.message);
              });
          })
          .catch(() => {});
      },
      /* 重置合伙人密码 */
      resetPsw(row) {
        this.$confirm('确定要重置此合伙人的密码为"123456"吗?', '提示', {
          type: 'warning'
        })
          .then(() => {
            const loading = this.$loading({ lock: true });
            resetPassword(row.id)
              .then((msg) => {
                loading.close();
                this.$message.success(msg);
              })
              .catch((e) => {
                loading.close();
                this.$message.error(e.message);
              });
          })
          .catch(() => {});
      },
      /* 更改状态 */
      editStatus(row) {
        const loading = this.$loading({ lock: true });
        modify({id:row.id,field:'is_freeze',value: row.is_freeze})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
          })
          .catch((e) => {
            loading.close();
            row.status = !row.status ? 1 : 0;
            this.$message.error(e.message);
          });
      }
    }
  };
</script>
<style lang="scss" scoped>

  .ele-cell-content {
   
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

</style>
