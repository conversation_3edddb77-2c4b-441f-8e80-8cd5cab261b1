import axios from '@/utils/request';


/**
 * 查询统计首页数据
 * @param params 查询条件
 */
 export async function getIndexData() {
  const res = await axios.get('/figure.statis/getIndexData');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询统计首页回收分类图表
 * @param params 查询条件
 */
 export async function getIndexChartData(params) {
  const res = await axios.get('/figure.statis/getIndexChartData',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}




/**
 * 查询统计合伙人统计图表
 * @param params 查询条件
 */
 export async function getPartnerChartData(params) {
  const res = await axios.get('/figure.statis/getPartnerChartData',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询统计合伙人地图图表
 * @param params 查询条件
 */
 export async function getPartnerMapData(params) {
  const res = await axios.get('/figure.statis/getPartnerMapData',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 查询统计回收员统计图表
 * @param params 查询条件
 */
 export async function getAccountChartData(params) {
  const res = await axios.get('/figure.statis/getAccountChartData',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询统计回收员地图图表
 * @param params 查询条件
 */
 export async function getAccountMapData(params) {
  const res = await axios.get('/figure.statis/getAccountMapData',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 查询统计回收员在线人数
 * @param params 查询条件
 */
 export async function getAccountOnlineData(params) {
  const res = await axios.get('/figure.statis/getAccountOnlineData',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 查询统计回收站余额图表
 * @param params 查询条件
 */
 export async function getAccountBalanceData() {
  const res = await axios.get('/figure.statis/getAccountBalanceData');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 查询统计回收站统计图表
 * @param params 查询条件
 */
 export async function getStationChartData(params) {
  const res = await axios.get('/figure.statis/getStationChartData',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询统计回收站地图图表
 * @param params 查询条件
 */
 export async function getStationMapData(params) {
  const res = await axios.get('/figure.statis/getStationMapData',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询统计回收站订单排行
 * @param params 查询条件
 */
export async function getStationRankingData(params) {
  const res = await axios.get('/figure.statis/getStationRankingData',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 查询统计打包站统计图表
 * @param params 查询条件
 */
 export async function getPackChartData(params) {
  const res = await axios.get('/figure.statis/getPackChartData',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询统计打包站地图图表
 * @param params 查询条件
 */
 export async function getPackMapData(params) {
  const res = await axios.get('/figure.statis/getPackMapData',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询统计打包站订单排行
 * @param params 查询条件
 */
export async function getPackRankingData(params) {
  const res = await axios.get('/figure.statis/getPackRankingData',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}












