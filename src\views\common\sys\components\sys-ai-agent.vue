<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">AI智能体管理</div>
      <div class="ele-page-desc">用于管理AI智能体信息，包括空间配置和智能体设置。</div>
    </div>
    <div class="ele-body" style="padding-bottom: 71px">
      <!-- 搜索表单 -->
      <agent-search @search="reload"/>
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            新增智能体
          </el-button>
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            class="ele-btn-icon"
            @click="removeBatch"
          >
            删除
          </el-button>
        </template>
        <!-- 图片列 -->
        <template v-slot:pic_url="{ row }">
          <el-avatar
            v-if="row.pic_url"
            :src="row.pic_url"
            shape="square"
            :size="40"
          />
          <span v-else>-</span>
        </template>
        <!-- 类型列 -->
        <template v-slot:type="{ row }">
          <el-tag
            :type="getTypeTagType(row.type)"
            size="small"
          >
            {{ getTypeText(row.type) }}
          </el-tag>
        </template>
        <!-- 状态列 -->
        <template v-slot:status="{ row }">
          <el-switch
            v-model="row.status"
            :active-value="1"
            :inactive-value="-1"
            @change="editStatus(row)"
          />
        </template>
        <!-- 操作列 -->
        <template v-slot:action="{ row }">
          <el-link
            type="primary"
            :underline="false"
            icon="el-icon-edit"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-popconfirm
            class="ele-action"
            title="确定要删除此智能体吗？"
            @confirm="remove(row)"
          >
            <template v-slot:reference>
              <el-link
                type="danger"
                :underline="false"
                icon="el-icon-delete"
              >
                删除
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </div>
    <!-- 编辑弹窗 -->
    <agent-edit
      :data="current"
      :visible.sync="showEdit"
      @done="reload"
    />
  </div>
</template>

<script>
  import { list, remove, update } from '@/api/aiAgent';
  import AgentSearch from '@/views/aiAgent/components/agent-search';
  import AgentEdit from '@/views/aiAgent/components/agent-edit';

  export default {
    name: 'SysAiAgent',
    components: { AgentSearch, AgentEdit },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },
          {
            prop: 'id',
            label: 'ID',
            width: 60,
            align: 'center'
          },
          {
            prop: 'space_id',
            label: '空间ID',
            minWidth: 120,
            showOverflowTooltip: true
          },
          {
            prop: 'agent_id',
            label: '智能体ID',
            minWidth: 120,
            showOverflowTooltip: true
          },
          {
            prop: 'agent_name',
            label: '智能体名称',
            minWidth: 150,
            showOverflowTooltip: true
          },
          {
            prop: 'pic_url',
            label: '图片',
            width: 80,
            align: 'center',
            slot: 'pic_url'
          },
          {
            prop: 'type',
            label: '类型',
            width: 80,
            align: 'center',
            slot: 'type'
          },
          {
            prop: 'status',
            label: '状态',
            width: 80,
            align: 'center',
            slot: 'status'
          },
          {
            prop: 'sort',
            label: '排序',
            width: 80,
            align: 'center'
          },
          {
            prop: 'create_time',
            label: '创建时间',
            minWidth: 160,
            showOverflowTooltip: true
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 130,
            align: 'center',
            resizable: false,
            slot: 'action',
            fixed: 'right'
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false
      };
    },
    computed: {
      // 表格数据源
      datasource() {
        return ({ page, limit, where, order }) => {
          return list({ ...where, ...order, page, limit });
        };
      }
    },
    methods: {
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      /* 删除单个 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      },
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的智能体吗?', '提示', {
          type: 'warning'
        })
          .then(() => {
            const loading = this.$loading({ lock: true });
            Promise.all(this.selection.map((d) => remove(d.id)))
              .then(() => {
                loading.close();
                this.$message.success('删除成功');
                this.reload();
              })
              .catch((e) => {
                loading.close();
                this.$message.error(e.message);
              });
          })
          .catch(() => {});
      },
      /* 更改状态 */
      editStatus(row) {
        const loading = this.$loading({ lock: true });
        update({ id: row.id, status: row.status })
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
          })
          .catch((e) => {
            loading.close();
            row.status = row.status === 1 ? -1 : 1;
            this.$message.error(e.message);
          });
      },
      /* 获取类型标签类型 */
      getTypeTagType(type) {
        const typeMap = {
          1: 'success',
          2: 'warning',
          3: 'danger'
        };
        return typeMap[type] || '';
      },
      /* 获取类型文本 */
      getTypeText(type) {
        const typeMap = {
          1: '文字',
          2: '图片',
          3: '视频'
        };
        return typeMap[type] || '未知';
      }
    }
  };
</script>
