<template>
  <el-card shadow="never" body-style="padding: 0;">
    <div class="ele-cell demo-monitor-tool">
      <div class="ele-cell-content">
        <el-tabs
          v-model="saleSearch.type"
          class="demo-monitor-tabs"
          @tab-click="onSaleTypeChange"
        >
          <el-tab-pane label="合伙人数量" name="partnerCount" />
          <el-tab-pane label="会员数量" name="memberCount" />
          <el-tab-pane label="充值记录" name="pointLog" />
          <el-tab-pane label="消费记录" name="balanceLog" />
        </el-tabs>
      </div>
      <div :class="['ele-inline-block', { 'hidden-xs-only': styleResponsive }]">
        <el-radio-group v-model="saleSearch.dateType" size="small" @change="onSearchTypeChange">
          <el-radio-button :label="0">今天</el-radio-button>
          <el-radio-button :label="1">近一周</el-radio-button>
          <el-radio-button :label="2">近一月</el-radio-button>
          <el-radio-button :label="3">近一年</el-radio-button>
        </el-radio-group>
      </div>
      <div
        :class="['ele-inline-block', { 'hidden-sm-and-down': styleResponsive }]"
        style="width: 260px; margin-left: 10px"
      >
        <el-date-picker
          unlink-panels
          type="daterange"
          class="ele-fluid"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          v-model="saleSearch.datetime"
          range-separator="至"
          size="small"
          value-format="yyyy-MM-dd"
        />
      </div>
      <div style="margin-left: 10px">
          <el-button
            type="primary"
            @click="search"
            icon="el-icon-search"
            class="ele-btn-icon"
            size="small"
          >
            查询
          </el-button>
          <el-button  size="small" @click="reset">重置</el-button>
      </div>
    </div>
    <el-divider />
    <el-row>
      <el-col v-bind="styleResponsive ?  (saleSearch.type === 'partnerCount' || saleSearch.type === 'memberCount' ? { lg: 24, md: 16 } : { lg: 18, md: 16 }) : (saleSearch.type === 'partnerCount' || saleSearch.type === 'memberCount' ?  { span: 24 } : { span: 18 })" >
        <div class="demo-monitor-title">
          <span v-if="saleSearch.type === 'partnerCount'">合伙人趋势</span>
          <span v-else-if="saleSearch.type === 'memberCount'">会员趋势</span>
          <span v-else-if="saleSearch.type === 'pointLog'">充值趋势</span>
          <span v-else-if="saleSearch.type === 'balanceLog'">消费趋势</span>
        </div>
        <v-chart
          ref="saleChart"
          style="height: 285px"
          :option="saleChartOption"
        />
      </el-col>
      <el-col v-bind="styleResponsive ? { lg: 6, md: 8 } : { span: 6 }" v-if="saleSearch.type !== 'partnerCount' && saleSearch.type !== 'memberCount'">
        <div class="demo-monitor-title" style="display: flex">
          <div>
            <span v-if="saleSearch.type === 'partnerCount'">合伙人趋势</span>
            <span v-else-if="saleSearch.type === 'memberCount'">会员趋势</span>
            <span v-else-if="saleSearch.type === 'pointLog'">用户充值趋势</span>
            <span v-else-if="saleSearch.type === 'balanceLog'">消费类型趋势</span>
          </div>
          <div>排名</div>
        </div>
        <div
          v-for="(item, index) in saleroomRankData"
          :key="index"
          class="demo-monitor-rank-item ele-cell"
          style="margin-bottom: 15px"
        >
          <el-tag
            size="mini"
            type="info"
            :effect="index < 3 ? 'dark' : 'light'"
            :color="index < 3 ? '#314659' : 'hsla(0, 0%, 60%, .2)'"
            style="border-color: transparent"
            class="ele-tag-round"
          >
            {{ index + 1 }}
          </el-tag>
          <div class="ele-cell-content" style="display: flex;">
            <div style="flex: 1;" class="itemName">{{ saleSearch.type === 'pointLog' ? item.nickname :  item.desc}}</div>
            <div style="width: 90px; text-align: right;margin-right: 110px;">{{ item.telphone }}</div>
          </div>
          <div class="ele-text-secondary" style="width: 50px;;">{{ item.num }}</div>
        </div>
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
  
  import { getAdminChartData } from '@/api/dashboard/analysis';
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { LineChart,BarChart,PieChart  } from 'echarts/charts';
  import {
    GridComponent,
    TooltipComponent,
    LegendComponent
  } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { echartsMixin } from '@/utils/echarts-mixin';
  use([
    CanvasRenderer,
    LineChart,
    BarChart,
    PieChart,
    GridComponent,
    TooltipComponent,
    LegendComponent
  ]);


  use([CanvasRenderer, BarChart, GridComponent, TooltipComponent]);

  export default {
    components: { VChart },
    mixins: [echartsMixin(['saleChart'])],
    data() {
      return {
        // 销售量搜索参数
        saleSearch: {
          type: 'partnerCount',
          dateType: 0,
          datetime: ''
        },
        // 客户趋势数据
        saleroomData1: [],
        // 会员趋势数据
        saleroomData2: [],
        // 电影订单趋势数据
        saleroomData3: [],
        // 佣金记录趋势数据
        saleroomData4: [],
        // 访问量趋势数据
        saleDate: [],
        // 门店排名数据
        saleroomRankData: [ ],
        saleRankData1:[],
        saleRankData2:[],
        saleRankData3:[],
        saleRankData4:[],
        // 销售额柱状图配置
        saleChartOption: {}
      };
    },
    computed: {
      // 是否开启响应式布局
      styleResponsive() {
        return this.$store.state.theme.styleResponsive;
      }
    },
    created() {
      this.getSaleroomData();
    },
    methods: {
      /* 搜索 */
      search() {
        this.getSaleroomData();
      },
      /*  重置 */
      reset() {
        this.saleSearch.datetime = [];
        this.search();
      },
      onSearchTypeChange(){
        this.saleSearch.datetime = [];
        this.getSaleroomData();
      },
      /* 获取销售量数据 */
      getSaleroomData() {

        let datyType = this.saleSearch.dateType;
        let datetime = this.saleSearch.datetime;

        if(datetime != ''){
          datyType = 5;
        }

        const [d1, d2] = datetime ?? [];

        getAdminChartData({
          type:datyType,
          start_time:d1 ? d1 + ' 00:00:00' : '',
          end_time: d2 ? d2 + ' 23:59:59' : ''
        })
          .then((data) => {
            this.saleDate = data.date;
            this.saleroomData1 = data.partner;
            this.saleroomData2 = data.member;
            this.saleroomData3 = data.point;
            this.saleroomData4 = data.balance;
            this.onSaleTypeChange();
          })
          .catch((e) => {
            this.$message.error(e.message);
          });
      },

      /* 销售量tab选择改变事件 */
      onSaleTypeChange() {
        let data = [];

        

        if(this.saleSearch.type === 'partnerCount'){
          data = this.saleroomData1;
          this.saleroomRankData = this.saleroomData1.ranking;
        }else if(this.saleSearch.type === 'memberCount'){
          data = this.saleroomData2;
          this.saleroomRankData = this.saleroomData2.ranking;
        }else if(this.saleSearch.type === 'pointLog'){
          data = this.saleroomData3;
          this.saleroomRankData = this.saleroomData3.ranking;
        }else if(this.saleSearch.type === 'balanceLog'){
          data = this.saleroomData4;
          this.saleroomRankData = this.saleroomData4.ranking;
        }


        if(this.saleSearch.type === 'partnerCount'){
           
          this.saleChartOption = {
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: '合伙人数量',
              right: 20
            },
            grid: {
              left: "5%",
              bottom: "8%",
              containLabel: true,
            },
            xAxis: [
              {
                type: 'category',
                boundaryGap: false,
                data: this.saleDate
              }
            ],
            yAxis: [
              {
                type: 'value'
              }
            ],
            series: 
              {
                name: '合伙人数量',
                type: 'line',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                  opacity: 0.5
                },
                data: data.count
              }
            
          };

        }else if(this.saleSearch.type === 'memberCount'){
          this.saleChartOption = {
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: ['充值笔数','充值金额'],
              right: 20
            },
            grid: {
              left: "1%",
              bottom: "3%",
              containLabel: true,
            },
            xAxis: [
              {
                type: 'category',
                boundaryGap: false,
                data: this.saleDate,
                axisLabel: {
                  interval: 0,
      			      rotate: 80,
                  textStyle: {
                    fontSize: 12,
                  },
                }
              }
            ],
            yAxis: [
              {
                type: 'value'
              }
            ],
            series: [
              {
                name: '充值笔数',
                type: 'line',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                  opacity: 0.5
                },
                data: data.count
              },
              {
                name: '充值金额',
                type: 'bar',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                  opacity: 0.5
                },
                data: data.value
              }
            ]
          };
        }else if(this.saleSearch.type === 'pointLog' || this.saleSearch.type === 'balanceLog'){




          this.saleChartOption = {
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: ['操作笔数','操作金额'],
              right: 20
            },
            grid: {
              left: "1%",
              bottom: "3%",
              containLabel: true,
            },
            xAxis: [
              {
                type: 'category',
                boundaryGap: false,
                data: this.saleDate,
                axisLabel: {
                  interval: 0,
      			      rotate: 80,
                  textStyle: {
                    fontSize: 12,
                  },
                }
              }
            ],
            yAxis: [
              {
                type: 'value'
              }
            ],
            series: [
              {
                name: '操作笔数',
                type: 'line',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                  opacity: 0.5
                },
                data: data.count
              },
              {
                name: '操作金额',
                type: 'bar',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                  opacity: 0.5
                },
                data: data.value
              }
            ]
          };
        }
        
      }
    }
  };
</script>

<style lang="scss" scoped>
  /* 销售额、访问量工具栏 */
  .demo-monitor-tool {
    padding: 0 20px;

    .demo-monitor-tabs {
      height: 51px;
    }

    :deep(.el-tabs__item) {
      height: 51px;
      line-height: 51px;
      font-size: 15px;
    }

    :deep(.el-tabs__nav-wrap:after) {
      display: none;
    }
  }

  /* 小标题 */
  .demo-monitor-title {
    padding: 0 20px;
    margin: 15px 0 5px 0;
  }

  /* 排名 item */
  .demo-monitor-rank-item {
    padding: 0 20px;
    line-height: 20px;
    margin-top: 18px;
  }
</style>
