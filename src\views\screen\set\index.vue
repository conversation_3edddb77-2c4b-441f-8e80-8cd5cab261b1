<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">大屏设置</div>
      <div class="ele-page-desc">
        用于大屏相关设置。
      </div>
    </div>
    <div class="ele-body" style="padding-bottom: 71px;background: var(--color-white);">
      <el-card shadow="never" style="padding-bottom: 71px;height:500px;">
        <el-row :gutter="20"  style="margin-bottom: 20px;">
          <el-col :md="12" :sm="12" :xs="12">
           
              <el-alert type="info" :closable="false" class="ele-alert-border" >
             
                <div class="ele-cell">
                  <div class="ele-cell-content">
                    <div class="ele-cell-title"> 
                      <h5 style="margin: 5px 0 15px 0">大屏首页  
                        <el-popover
                          placement="right"
                          title="二维码"
                          width="100"
                          height="100"
                          trigger="hover"
                        >
                          <img :src="qrcode" alt=""  style="object-fit: cover;width: 150px;height: 150px;">
                          <i slot="reference" class="el-icon-_qrcode"></i>
                        </el-popover>
                      </h5>
                    </div>
                    <div class="ele-cell-desc"> <h6 style="margin-bottom: 10px">{{ url }}  <el-link type="success" @click="copy_url(url)">复制</el-link></h6></div>
                  </div>
                </div>
              </el-alert>
          
          </el-col>
          
        </el-row>

      </el-card>
    </div>
  </div>
</template>

<script>
  import { set } from '@/api/screen/avatar';
  export default {
    name: 'ScreenSet',
    data() {
      return {
        url:'',
        qrcode:'',
        host:window.location.protocol + "//" +location.host
      };
    },
    methods: {
      querySet() {
        set().then((data) => {
          if(data != null){
            this.url = data.url;
            this.qrcode = data.qrcode;
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
      },
     
      copy_url(content){
       // window.clipboardData.setData('text', context);

        if (window.clipboardData) {
            /*
            window.clipboardData有三个方法:
          （1）clearData(sDataFormat) 删除剪贴板中指定格式的数据。sDataFormat:"text","url"
          （2）getData(sDataFormat) 从剪贴板获取指定格式的数据。 sDataFormat:"text","url"
          （3）setData(sDataFormat, sData) 给剪贴板赋予指定格式的数据。返回 true 表示操作成功。
            */
          window.clipboardData.setData('text', content);
        } else {
          (function (content) {
            //oncopy 事件在用户拷贝元素上的内容时触发。
            document.oncopy = function (e) {
              e.clipboardData.setData('text', content);
              e.preventDefault(); //取消事件的默认动作
              document.oncopy = null;
            }
          })(content);
          //execCommand方法是执行一个对当前文档/当前选择/给出范围的命令。
          //'Copy':将当前选中区复制到剪贴板。 
          document.execCommand('Copy');
        }
        this.$message.success('复制成功！');
      },
    } ,mounted() {
      this.querySet()
    }
  };
</script>
