<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
        <shareholder-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
       

        <!-- 订单编号 -->
       

        

        <template v-slot:user="{ row }">
          <div class="ele-cell" v-if="row.figureUser">
            <el-avatar v-if="row.figureUser.avatar" :src="row.figureUser.avatar" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.figureUser.nickname }} </div>
              <div class="ele-cell-desc">{{ row.figureUser.telphone }} </div>
            </div>
          </div>
        </template>


      
        

        
        
      </ele-pro-table>
    </el-card>
  </div>
</template>

<script>
 import shareholderSearch from './components/brokerage-search';

  import { shareholderLog} from '@/api/brokerage';

  export default {
    name: 'shareholderLog',
    props:{
      pid: Number,
      uid: Number,
    },
    components: {
    shareholderSearch
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            align: 'center',
            showOverflowTooltip: true,
            fixed: 'left'
          },
          
         
         
          {
            prop: 'user',
            label: '用户',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'user'
          },
        
          
          {
            prop: 'money',
            label: '佣金',
            showOverflowTooltip: true,
            minWidth: 110
          },
            {
            prop: 'total',
            label: '营业额',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'amount_time',
            label: '收益时间',
            showOverflowTooltip: true,
            minWidth: 100,
          },
         
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示审核弹窗
        showCheck: false,
        // 是否显示充值弹窗
        showRecharge: false,
        //弹框名称
        id:0,
      };
    },
    methods: {
      beforeHandleCommand(index, row,command){
          
          return {
              'index': index,
              'row': row,
              'command':command
          }
    },
      onDropClick(command) {
        
        switch (command.command) {
            case "a"://编辑
                this.openAddNum(command.row,1);
                break;
            case "b"://充值密码
                this.openAddNum(command.row,2);
                break;
            case "c"://删除
                this.openAddNum(command.row,3);
                break;
            case "d"://删除
                this.openAddNum(command.row,4);
                break;
        }
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        where.pid = this.pid;
        where.uid = this.uid;
        return shareholderLog({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        where = !where ? {} : where;
        where.pid = this.pid;
        where.uid = this.uid;
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },

      /* 打开编辑弹窗 */
      openCheck(row) {
        this.id = row.id;
        this.current = row;
        this.showCheck = true;
      },

      /* 打开编辑弹窗 */
      openRecharge(row) {
        this.id = row.id;
        this.current = row;
        this.showRecharge = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      },
      /* 更新二维码 */
      reloadQrcode(row) {
        const loading = this.$loading({ lock: true });
        reloadQrcode({user_id:row.userId})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      },

      
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的合伙人吗?', '提示', {
          type: 'warning'
        })
          .then(() => {
            const loading = this.$loading({ lock: true });
            remove(this.selection.map((d) => d.id))
              .then((msg) => {
                loading.close();
                this.$message.success(msg);
                this.reload();
              })
              .catch((e) => {
                loading.close();
                this.$message.error(e.message);
              });
          })
          .catch(() => {});
      },
      /* 重置合伙人密码 */
      resetPsw(row) {
        this.$confirm('确定要重置此合伙人的密码为"123456"吗?', '提示', {
          type: 'warning'
        })
          .then(() => {
            const loading = this.$loading({ lock: true });
            resetPassword(row.id)
              .then((msg) => {
                loading.close();
                this.$message.success(msg);
              })
              .catch((e) => {
                loading.close();
                this.$message.error(e.message);
              });
          })
          .catch(() => {});
      },
      /* 更改状态 */
      editStatus(row) {
        const loading = this.$loading({ lock: true });
        modify({id:row.id,field:'is_freeze',value: row.is_freeze})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
          })
          .catch((e) => {
            loading.close();
            row.status = !row.status ? 1 : 0;
            this.$message.error(e.message);
          });
      }
    },
    watch:{
      pid(){
        this.reload();
      },
      uid(){
        this.reload();
      }
    }
  };
</script>
<style lang="scss" scoped>

  .ele-cell-content {
   
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

</style>
