<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <log-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            class="ele-btn-icon"
            @click="removeBatch"
          >
            删除
          </el-button>
        </template>

      

        <template v-slot:user="{ row }">
          <div class="ele-cell" v-if="row.figureUser">
            <el-avatar v-if="row.figureUser.avatar" :src="row.figureUser.avatar" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.figureUser.nickname }} </div>
            </div>
          </div>
          <span v-else>无</span>
          
        </template>

        <template v-slot:type="{ row }">
          <el-tag v-if="row.type==1" type="info" effect="plain">生产工厂</el-tag>
          <el-tag v-if="row.type==2" type="warning" effect="plain">团购商家</el-tag>
          <el-tag v-if="row.type==3" type="success" effect="plain">零售商家</el-tag>
          <el-tag v-if="row.type==4" type="primary" effect="plain">智能代写</el-tag>
          <el-tag v-if="row.type==5" type="primary" effect="plain">文案仿写</el-tag>


           <el-tag v-if="row.type==6" type="info" effect="plain">企业宣传</el-tag>
          <el-tag v-if="row.type==7" type="warning" effect="plain">同城团购</el-tag>
          <el-tag v-if="row.type==8" type="success" effect="plain">电商带货</el-tag>
          <el-tag v-if="row.type==9" type="primary" effect="plain">知识科普</el-tag>
          <el-tag v-if="row.type==10" type="primary" effect="plain">情感专家</el-tag>
           <el-tag v-if="row.type==11" type="info" effect="plain">口播文案</el-tag>
          <el-tag v-if="row.type==12" type="warning" effect="plain">朋友圈营销</el-tag>
          <el-tag v-if="row.type==13" type="success" effect="plain">小红书笔记</el-tag>
          <el-tag v-if="row.type==14" type="primary" effect="plain">智能代写</el-tag>
          <el-tag v-if="row.type==15" type="primary" effect="plain">智能代写</el-tag>
            <el-tag v-if="row.type==16" type="primary" effect="plain">视频提取</el-tag>
             <el-tag v-if="row.type==17" type="primary" effect="plain">一键仿写</el-tag>
               <el-tag v-if="row.type==18" type="primary" effect="plain">deekseep仿写</el-tag>
                   <el-tag v-if="row.type==20" type="primary" effect="plain">本地视频提取</el-tag>
        </template>


       

        <template v-slot:image_url="{ row }">

          <div class="ele-cell" v-if="row.image_url">
            <el-avatar :src="row.image_url" shape="square" :size="40" />
          </div>

          <span v-else>---</span>
        </template>


        <template v-slot:video_url="{ row }">
         
          <div class="ele-cell" v-if="row.video_url">
            <video width="320" height="240" controls :src="row.video_url"  v-if="row.video_url"> </video>
          </div>

          <span v-else>---</span>
          
        </template>

        <template v-slot:is_status="{ row }">
         
          <el-tag v-if="row.is_status== 'initialized'" >生成中</el-tag>
          <el-tag v-if="row.is_status== 'completed'" type="success">生成成功</el-tag>
          <el-tag v-if="row.is_status== 'failed'" type="danger">生成失败</el-tag>
          <div v-if="row.faild_message " style="margin-top: 5px;font-size: 2px;">
            失败原因：<span style="font-weight: bold;color: red;">{{row.faild_message}}</span>
          </div>
        </template>
      
        
        <!-- 操作列 -->
        <template v-slot:action="{ row }">
         
          <el-popconfirm
            class="ele-action"
            title="确定要删除此数据吗？"
            @confirm="remove(row)"
          >
            <template v-slot:reference>
              <el-link type="danger" :underline="false" icon="el-icon-delete">
                删除
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
  </div>
</template>

<script>
  import LogSearch from './components/log-search';
  import { list, remove, modify,sortChange  } from '@/api/aiCreate';
  import Sortable from 'sortablejs';

  export default {
    name: 'Avatar',
    components: {
      LogSearch
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },
          {
            prop: 'user',
            label: '用户',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'user'
          },
          {
            prop: 'name',
            label: '名称',
            showOverflowTooltip: true,
            minWidth: 110,
          },
         
          {
            prop: 'type',
            label: '类型',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'type'
          },

          {
            prop: 'question',
            label: '请求内容',
            showOverflowTooltip: true,
            minWidth: 110,
          },

          {
            prop: 'words',
            label: '文案字数',
            showOverflowTooltip: true,
            minWidth: 110,
          },

          {
            prop: 'answer',
            label: '文案内容',
            showOverflowTooltip: true,
            minWidth: 110,
          },
          {
            prop: 'create_time',
            label: '创建时间',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 100
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 150,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            showOverflowTooltip: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示分配回收员
        showCollector: false,
        //弹框名称
        id:0,
      };
    },
    methods: {
      rowDrop() {
        const tbody = document.querySelector('.el-table__body-wrapper tbody')
        const _this = this
        Sortable.create(tbody, {
          forceFallback: true,
          dragClass: 'drag',
          delay:100,
          animation: 1000,
          onEnd({ newIndex, oldIndex }) {
            console.log(newIndex);
            console.log(oldIndex);
            //更换表格数据 后续拖动坐标才会正确
            let data = _this.$refs.table.getData();
             
            let nid = data[newIndex].id;
            let oid = data[oldIndex].id;

            console.log(nid);
            console.log(oid);
            
            const currRow = data.splice(oldIndex, 1)[0];
            data.splice(newIndex, 0, currRow);

            sortChange(data).then((msg) => {
              
            }).catch((e) => {
              _this.$refs.table.reload();
            });
            //去数据库更换排序
            console.log(_this.$refs.table.getData());
          }
        })
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return list({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id).then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
      },
    
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的数据吗?', '提示', {
          type: 'warning'
        }).then(() => {
          const loading = this.$loading({ lock: true });
          remove(this.selection.map((d) => d.id))
            .then((msg) => {
              loading.close();
              this.$message.success(msg);
              this.reload();
            })
            .catch((e) => {
              loading.close();
              this.$message.error(e.message);
            });
        }).catch(() => {});
      },
      
      /* 更改状态 */
      editStatus(row) {
        const loading = this.$loading({ lock: true });
        modify({id:row.id,field:'is_display',value: row.is_display})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
          })
          .catch((e) => {
            loading.close();
            row.status = !row.status ? 1 : 0;
            this.$message.error(e.message);
          });
      }
    },mounted() {
      this.rowDrop()
      //this.columnDrop()
    }
  };
</script>

<style lang="scss" scoped>

  .drag {
    background: #000 !important;
    background-image: linear-gradient(#333, #999) !important;
  }

  .ele-cell-content {
   
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

</style>
