<!-- 用户编辑弹窗 -->
<template>
  <ele-modal
    width="740px"
    :visible="visible"
    :append-to-body="true"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? '修改卡密' : '添加卡密'"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="130px">
     
    
      
     

    

      <el-form-item label="生成数量:" prop="count">
        <el-input-number
          :min="0"
          v-model="form.count"
          placeholder="请输入生成数量"
          controls-position="right"
          class="ele-fluid ele-text-left"
        />
      </el-form-item>

     

    

     

       
    </el-form>
    <template v-slot:footer>
      <el-button @click="updateVisible(false)">取消 </el-button>
      <el-button type="primary" :loading="loading" @click="save"
        >保存
      </el-button>
    </template>
    <uploadPictures
      :isChoice="isChoice"
      :visible.sync="modalPic"
      @getPic="getPic"
      :gridBtn="gridBtn"
      :gridPic="gridPic"
      :title="modalTitle"
    ></uploadPictures>
  </ele-modal>
</template>

<script>
  import PagesSearch from "@/views/common/pages/pages-search";
  import uploadPictures from "@/components/uploadPictures";
  import EleImageUpload from 'ele-admin/es/ele-image-upload';
  import TinymceEditor from '@/components/TinymceEditor';
  import { add,update } from '@/api/card';
  import { getPages } from '@/api/layout';
  const DEFAULT_FORM = {
    type:1,
    label:'',
    quantity:0,
    count: 0,
    use_count: 1,
    one_level:'',
    two_level:'',
  };

  export default {
    name: 'MemberEdit',
    components: { PagesSearch,EleImageUpload,uploadPictures,TinymceEditor },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object,
      type:0
    },
    data() {
      return {
        modalTitle:'选择图片',
        modalPic: false,
        isChoice: "单选",
        gridBtn: {
          xl: 4,
          lg: 8,
          md: 8,
          sm: 8,
          xs: 8,
        },
        gridPic: {
          xl: 6,
          lg: 8,
          md: 12,
          sm: 12,
          xs: 12,
        },
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          label: [
            {
              required: true,
              message: '请输入卡密标签',
              trigger: 'blur'
            }
          ],
          quantity: [
            {
              required: true,
              message: '请输入会员天数',
              trigger: 'blur'
            }
          ],
          count: [
            {
              required: true,
              message: '请输入生成数量',
              trigger: 'blur'
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false,
        tableConfig: {
          datasource: ({ page, limit, where, order }) => {
            return getPages({ ...where, ...order, page, limit });
          },
          columns: [
            {
                columnKey: 'index',
                type: 'index',
                width: 45,
                align: 'center'
            },
            {
              prop: 'name',
              label: '名称',
              showOverflowTooltip: true,
              minWidth: 110,
              //slot: 'nickname'
            },
            {
              prop: 'url',
              label: '路径',
              showOverflowTooltip: true,
              minWidth: 110
            }
          ],
          rowClickChecked: true,
          rowClickCheckedIntelligent: false,
          toolkit: ['reload', 'columns'],
          size: 'small',
          toolStyle: { padding: '0 10px' }
        }
      };
    },
    methods: {
      search(where) {
        // debugger
        this.$refs.select.reload({
          where: where,
          page: 1
        });
      },
      // 选择图片
      modalPicTap(tit, picTit,openTitle) {
        this.modalTitle = openTitle;
        this.isChoice = tit === "dan" ? "单选" : "多选";
        this.picTit = picTit;
        this.modalPic = true;
      },
      // 选中图片
      getPic(pc) {
        switch (this.picTit) {
          case "pic_url":
            this.form.pic_url = pc.satt_dir;
            break;
        }
        this.modalPic = false;
      },
      //删除图片
      handleRemove() {
        this.form.pic_url = '';
      },

      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }

          this.loading = true;
          const data = {
            ...this.form,
          };

          data.type = this.type;

          const saveOrUpdata = this.isUpdate ? update : add;

          saveOrUpdata(data).then((msg) => {
            this.loading = false;
            this.$message.success(msg);
            this.updateVisible(false);
            this.$emit('done',{ type: this.type });
          }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
          });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      }
    },
    watch: {
      visible(visible) {
        if (visible) {
          if (this.data) {

            this.$util.assignObject(this.form, {
              ...this.data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
