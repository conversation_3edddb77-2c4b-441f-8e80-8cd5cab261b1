<!-- 搜索表单 -->
<template>
  <el-form
    label-width="77px"
    class="ele-form-search"
    @keyup.enter.native="search"
    @submit.native.prevent
  >
    <el-row :gutter="15">
      
      <el-col :lg="4" :md="12">
        <el-form-item label="用户:">
          <el-input clearable v-model="where.nickname" />
        </el-form-item>
      </el-col>

      <el-col :lg="4" :md="12">
        <el-form-item label="名称:">
          <el-input clearable v-model="where.name" />
        </el-form-item>
      </el-col>
      <!-- <el-col :lg="3" :md="12">
        <el-form-item label="类型:">
          <el-select
            clearable
            v-model="where.a_type"
            placeholder="请选择"
            class="ele-fluid"
          >
            <el-option label="形象克隆" :value="1" />
            <el-option label="照片克隆" :value="2" />
          </el-select>
        </el-form-item>
      </el-col> 

      <el-col :lg="3" :md="12">
        <el-form-item label="模式:">
          <el-select
            clearable
            v-model="where.type"
            placeholder="请选择"
            class="ele-fluid"
          >
            <el-option label="快速模式" :value="1" />
            <el-option label="高级模式" :value="2" />
          </el-select>
        </el-form-item>
      </el-col>  -->

      <el-col :lg="4" :md="12">
        <el-form-item label="状态:">
          <el-select
            clearable
            v-model="where.current_status"
            placeholder="请选择"
            class="ele-fluid"
          >
            <el-option label="生成中" value="initialized" />
            <el-option label="生成成功" value="completed" />
            <el-option label="生成失败" value="failed" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :lg="4" :md="12">
        <el-form-item label="审核状态:">
          <el-select
            clearable
            v-model="where.is_status"
            placeholder="请选择"
            class="ele-fluid"
          >
            <el-option label="待审核" value="1" />
            <el-option label="审核通过" value="2" />
          </el-select>

        </el-form-item>
      </el-col>  

      <el-col :lg="3" :md="12">
        <div class="ele-form-actions">
          <el-button
            type="primary"
            icon="el-icon-search"
            class="ele-btn-icon"
            @click="search"
          >
            查询
          </el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>

  const DEFAULT_WHERE = {
    nickname: '',
    name:'',
    a_type:'',
    type:'',
    current_status:'',
    is_status:'',
  };

  export default {
    name: 'LogSearch',
    data() {
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE }
      };
    },
    methods: {
      /* 搜索 */
      search() {
        this.$emit('search', this.where);
      },
      /*  重置 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.search();
      }
    }
  };
</script>
