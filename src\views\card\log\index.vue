<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <log-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            class="ele-btn-icon"
            @click="removeBatch"
          >
            删除
          </el-button>
        </template>

        <!-- 订单编号 -->
        <template v-slot:log_no="{ row }">
          <span v-if="row.log_no"> {{ row.log_no }}</span>
          <span v-else> -- </span>
        </template>

        <template v-slot:user="{ row }">
          <div class="ele-cell" v-if="row.figureUser">
            <el-avatar v-if="row.figureUser.avatar" :src="row.figureUser.avatar" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.figureUser.nickname }} </div>
              <div class="ele-cell-desc">{{ row.figureUser.telphone }} </div>
            </div>
          </div>
        </template>

        <template v-slot:partner="{ row }">
          <div class="ele-cell" v-if="row.figurePartner">
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.figurePartner.name }} </div>
              <div class="ele-cell-desc">{{ row.figurePartner.telphone }} </div>
            </div>
          </div>
        </template>

        <template v-slot:goods="{ row }">
          <div class="ele-cell">
            <el-avatar v-if="row.goods_pic" :src="row.goods_pic" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.goods_name }} </div>
              <div class="ele-cell-desc">卡密数量：{{ row.goods_quantity }} </div>
            </div>
          </div>
        </template>

        <template v-slot:quantity="{ row }">
          <div v-if="row.goods_day">会员天数：{{row.goods_day}}</div>
          <div v-if="row.goods_day">视频合成秒数：{{row.goods_second_infinite == 0 ? row.goods_second : '无限'}}</div>
          <div v-if="row.goods_day">声音高保真次数：{{row.goods_voice_twin_count}}</div>

          <div v-if="row.goods_day">声音高保真合成字数：{{row.high_fidelity_words_number}}</div>

          <div v-if="row.goods_day">ai文案次数：{{row.ai_copywriting_times}}</div>
          <div v-if="row.goods_day">声音专业版字数：{{row.xunfei_sound_generate_words_number}}</div>
          <div v-if="row.goods_day">声音专业版次数次数：{{row.xunfei_sound_clone_words_number}}</div>
          <div v-if="row.goods_day">ai标题次数{{row.ai_title_times}}</div>
          <div v-if="row.goods_point">充值点数：{{row.goods_point}}</div>
        </template>

        
        <!-- 操作列 -->
        <template v-slot:action="{ row }">
          <el-popconfirm
            class="ele-action"
            title="确定要删除此数据吗？"
            @confirm="remove(row)"
          >
            <template v-slot:reference>
              <el-link type="danger" :underline="false" icon="el-icon-delete">
                删除
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
  </div>
</template>

<script>
  import LogSearch from './components/log-search';
  import { list, remove } from '@/api/card/log';

  export default {
    name: 'CardLog',
    props:{
      pid: Number,
      uid: Number,
    },
    components: {
      LogSearch,
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            align: 'center',
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'log_no',
            label: '订单编号',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'log_no'
          },
          {
            prop: 'user',
            label: '用户',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'user'
          },
          {
            prop: 'partner',
            label: '合伙人',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'partner'
          },
          {
            prop: 'goods',
            label: '商品',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'goods'
          },
          {
            prop: 'quantity',
            label: '套餐',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 180,
            slot: 'quantity'
          },
          {
            prop: 'count',
            label: '数量',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 90
          },
          {
            prop: 'money',
            label: '实付',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 90
          },
          {
            prop: 'create_time',
            label: '购买时间',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 100,
            formatter: (row, column, cellValue) => {
              return this.$util.toDateString(cellValue);
            }
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 150,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            showOverflowTooltip: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示审核弹窗
        showCheck: false,
        // 是否显示充值弹窗
        showRecharge: false,
        //弹框名称
        id:0,
      };
    },
    methods: {
      beforeHandleCommand(index, row,command){
          
          return {
              'index': index,
              'row': row,
              'command':command
          }
    },
      onDropClick(command) {
        
        switch (command.command) {
            case "a"://编辑
                this.openAddNum(command.row,1);
                break;
            case "b"://充值密码
                this.openAddNum(command.row,2);
                break;
            case "c"://删除
                this.openAddNum(command.row,3);
                break;
            case "d"://删除
                this.openAddNum(command.row,4);
                break;
        }
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        where.pid = this.pid;
        where.uid = this.uid;
        return list({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        where = !where ? {} : where;
        where.pid = this.pid;
        where.uid = this.uid;
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },

      /* 打开编辑弹窗 */
      openCheck(row) {
        this.id = row.id;
        this.current = row;
        this.showCheck = true;
      },

      /* 打开编辑弹窗 */
      openRecharge(row) {
        this.id = row.id;
        this.current = row;
        this.showRecharge = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      },
      /* 更新二维码 */
      reloadQrcode(row) {
        const loading = this.$loading({ lock: true });
        reloadQrcode({user_id:row.userId})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      },

      
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的合伙人吗?', '提示', {
          type: 'warning'
        })
          .then(() => {
            const loading = this.$loading({ lock: true });
            remove(this.selection.map((d) => d.id))
              .then((msg) => {
                loading.close();
                this.$message.success(msg);
                this.reload();
              })
              .catch((e) => {
                loading.close();
                this.$message.error(e.message);
              });
          })
          .catch(() => {});
      },
      /* 重置合伙人密码 */
      resetPsw(row) {
        this.$confirm('确定要重置此合伙人的密码为"123456"吗?', '提示', {
          type: 'warning'
        })
          .then(() => {
            const loading = this.$loading({ lock: true });
            resetPassword(row.id)
              .then((msg) => {
                loading.close();
                this.$message.success(msg);
              })
              .catch((e) => {
                loading.close();
                this.$message.error(e.message);
              });
          })
          .catch(() => {});
      },
      /* 更改状态 */
      editStatus(row) {
        const loading = this.$loading({ lock: true });
        modify({id:row.id,field:'is_freeze',value: row.is_freeze})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
          })
          .catch((e) => {
            loading.close();
            row.status = !row.status ? 1 : 0;
            this.$message.error(e.message);
          });
      }
    },
    watch:{
      pid(){
        this.reload();
      },
      uid(){
        this.reload();
      }
    }
  };
</script>
<style lang="scss" scoped>

  .ele-cell-content {
   
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

</style>
