<template>
  <div class="ele-body ele-body-card">
    <el-row :gutter="15">
      <el-col :md="12" :sm="12">
        <el-card class="monitor-count-card" shadow="never">
          <template v-slot:header>
            <div class="ele-cell">
              <div class="ele-cell-content title">DY数据统计</div>
              <div
                :class="['ele-inline-block', { 'hidden-sm-and-down': styleResponsive }]"
                style="width: 260px; margin-left: 10px"
              >
                <el-date-picker
                  unlink-panels
                  type="daterange"
                  class="ele-fluid"
                  end-placeholder="结束日期"
                  start-placeholder="开始日期"
                  v-model="dyDatetime"
                  range-separator="至"
                  size="small"
                  value-format="yyyy-MM-dd"
                  @change="search(1)"
                />
              </div>
            </div>
          </template>
          <div class="ele-cell">
            <div class="ele-cell-content">
              <el-tag size="large"  class="ele-tag-round">
                <i class="el-icon-_database-solid"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up :end-val="indexData.dyPlay" />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                曝光量
              </div>
            </div>
            <div class="ele-cell-content">
              <el-tag size="large" type="success" class="ele-tag-round">
                <i class="el-icon-s-marketing"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up :end-val="indexData.dyLike" />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                点赞量
              </div>
            </div>
           
            <div class="ele-cell-content">
              <el-tag size="large" type="warning" class="ele-tag-round">
                <i class="el-icon-s-order"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up :end-val="indexData.dyComment"/>
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                评论量
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :md="12" :sm="12">
        <el-card class="monitor-count-card" shadow="never">
          <template v-slot:header>
            <div class="ele-cell">
              <div class="ele-cell-content title">KS数据统计</div>
              <div
                :class="['ele-inline-block', { 'hidden-sm-and-down': styleResponsive }]"
                style="width: 260px; margin-left: 10px"
              >
                <el-date-picker
                  unlink-panels
                  type="daterange"
                  class="ele-fluid"
                  end-placeholder="结束日期"
                  start-placeholder="开始日期"
                  v-model="ksDatetime"
                  range-separator="至"
                  size="small"
                  value-format="yyyy-MM-dd"
                  @change="search(2)"
                />
              </div>
            </div>
          </template>
          <div class="ele-cell">
            <div class="ele-cell-content">
              <el-tag size="large"  class="ele-tag-round">
                <i class="el-icon-_database-solid"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up :end-val="indexData.ksPlay * 1"  />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                曝光量
              </div>
            </div>
            <div class="ele-cell-content">
              <el-tag size="large" type="success" class="ele-tag-round">
                <i class="el-icon-s-marketing"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up :end-val="indexData.ksLike * 1"  />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                点赞量
              </div>
            </div>
           
            <div class="ele-cell-content">
              <el-tag size="large" type="warning" class="ele-tag-round">
                <i class="el-icon-s-order"></i>
              </el-tag>
              <div class="monitor-count-card-num ele-text-heading">
                <vue-count-up :end-val="indexData.ksComment * 1"  />
              </div>
              <div class="monitor-count-card-text ele-text-secondary">
                评论量
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <statis-chart></statis-chart>
  </div>
</template>

<script>
  import statisChart from './components/statis-chart' 
  import VueCountUp from 'vue-countup-v2';
  import { getRepostData } from '@/api/videoRepost';
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { LineChart,BarChart,PieChart  } from 'echarts/charts';
  import {
    GridComponent,
    TooltipComponent,
    LegendComponent
  } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { echartsMixin } from '@/utils/echarts-mixin';
  use([
    CanvasRenderer,
    LineChart,
    BarChart,
    PieChart,
    GridComponent,
    TooltipComponent,
    LegendComponent
  ]);
  export default {
    name: 'StatisticsIndex',
    components: { statisChart,VueCountUp,VChart },
    mixins: [echartsMixin(['orderChart'])],
    computed: {
      // 是否开启响应式布局
      styleResponsive() {
        return this.$store.state.theme.styleResponsive;
      }
    },
    data() {
      return {
        dyDatetime: '',
        ksDatetime: '',
        orderChartOption: {},
        countUpOptions: {
          useEasing: true,
          useGrouping: true,
          decimalPlaces:2,
          separator: ',',
          decimal: '.',
          prefix: '',
          suffix: ''
        },
        indexData:{
          dyPlay:0,
          dyLike:0,
          dyComment:0,
          ksPlay:0,
          ksLike:0,
          ksComment:0,
        },
      };
    },
    created() {
      this.getShowData();
    },
    methods: {
      search(way) {
        if(way == 1){
          this.getDyData();
        }else if(way == 2){
          this.getKsData();
        }
      },
      getShowData(){
        this.getDyData();
        this.getKsData();
      },
      getDyData() {

        let datetime = this.dyDatetime;

        const [d1, d2] = datetime ?? [];
        getRepostData({
          way:1,
          start_time:d1 ? d1 + ' 00:00:00' : '',
          end_time: d2 ? d2 + ' 23:59:59' : ''
        }).then((data) => {
          this.indexData.dyPlay = data.play;
          this.indexData.dyLike = data.like;
          this.indexData.dyComment = data.comment;
        }).catch((e) => {
          this.$message.error(e.message);
        });
      },
      getKsData() {

        let datetime = this.ksDatetime;

        const [d1, d2] = datetime ?? [];
          getRepostData({
            way:2,
            start_time:d1 ? d1 + ' 00:00:00' : '',
            end_time: d2 ? d2 + ' 23:59:59' : ''
          }).then((data) => {
            this.indexData.ksPlay = data.play;
            this.indexData.ksLike = data.like;
            this.indexData.ksComment = data.comment;
          }).catch((e) => {
            this.$message.error(e.message);
          });
        }
    }
  };
</script>

<style scoped>
  .monitor-count-card ::v-deep .el-card__body {
    padding-top: 18px;
    text-align: center;
    position: relative;
  }

  .monitor-count-card ::v-deep .el-tag {
    border-color: transparent;
    font-size: 15px;
  }

  .monitor-count-card .monitor-count-card-num {
    font-weight: 500;
    font-size: 32px;
    margin-top: 12px;
  }

  .monitor-count-card .monitor-count-card-text {
    font-size: 12px;
    margin: 10px 0;
  }

  .monitor-count-card .monitor-count-card-trend {
    font-weight: 600;
    padding: 6px 0;
  }

  .monitor-count-card .monitor-count-card-trend > i {
    font-size: 12px;
    font-weight: 600;
    margin-right: 5px;
  }

  .monitor-count-card .monitor-count-card-tips {
    position: absolute;
    top: 15px;
    right: 15px;
    cursor: pointer;
  }
</style>
