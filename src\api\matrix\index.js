import axios from '@/utils/request';






/**
 * 一级分类
 * @param params 查询条件
 */
export async function account(params) {
 
  const res = await axios.get('/figure.matrix/account', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function video(params) {
 
  const res = await axios.get('/figure.matrix/video', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}