<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">账号配置</div>
      <div class="ele-page-desc"> 用于账号相关参数配置。 </div>
    </div>
    <div class="ele-body">
      <el-card shadow="never">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="130px"
          style="margin: 10px auto"
        >
          <el-row :gutter="15">
            <el-col :sm="20">
              <el-card
                shadow="never"
                header="数字人配置"
                body-style="padding: 22px 22px 0 22px;"
              >
                 <el-form-item label="版本:" prop="operation_version">
                  <el-radio-group v-model="form.operation_version">
                    <el-radio label="1" value="1">直推运营版</el-radio>
                    <el-radio label="2" value="2">间推运营版</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="请求地址:" prop="agent_url" v-if="form.operation_version == 2">
                  <el-input
                    v-model="form.agent_url"
                  
                    placeholder="请求地址"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="APPID:" prop="account_appid">
                  <el-input
                    v-model="form.account_appid"
                    type="text"
                    placeholder="请输入账号appid"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="APPSECRET:" prop="account_appsecret">
                  <el-input
                    v-model="form.account_appsecret"
                    type="password"
                    show-password
                    placeholder="请输入账号appsecret"
                    clearable
                  />
                </el-form-item>

             

                <el-form-item v-if="form.msg">
                  <el-alert
                    style="width: 400px"
                    :title="form.msg"
                    type="error"
                    show-icon
                    :closable="false"
                    class="ele-alert-border"
                  />
                </el-form-item>

                <el-form-item label="到期时间:" v-if="type == 1">
                  <span style="font-size: 24px">{{
                    maturity_time ? maturity_time : '无'
                  }}</span>
                </el-form-item>
                <el-form-item label="点数:" v-if="type == 2">
                  <span style="font-size: 24px">{{
                    balance ? balance : '0.00'
                  }}</span>
                </el-form-item>
              </el-card>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button type="primary" :loading="loading" @click="submit">
              提交
            </el-button>
          </el-form-item>

          <el-card shadow="never" header="消费记录" v-if="type == 2">
            <ele-pro-table
              ref="table"
              :columns="columns"
              :datasource="datasource"
              :selection.sync="selection"
            >
              <template v-slot:point="{ row }">
                <el-tag v-if="row.type == 1" type="success"
                  >+{{ row.point }}</el-tag
                >
                <el-tag v-if="row.type == 2" type="danger"
                  >-{{ row.point }}</el-tag
                >
              </template>

              <template v-slot:way="{ row }">
                <el-tag v-if="row.way == 0" type="info" effect="light"
                  >后台操作</el-tag
                >
                <el-tag v-if="row.way == 1" type="success" effect="light"
                  >点数充值</el-tag
                >
                <el-tag v-if="row.way == 2" type="warning" effect="light"
                  >声音克隆入门版</el-tag
                >
                <el-tag v-if="row.way == 3" type="warning" effect="light"
                  >声音克隆高保真</el-tag
                >
                <el-tag v-if="row.way == 4" type="primary" effect="light"
                  >形象克隆线路三版</el-tag
                >
                <el-tag v-if="row.way == 5" type="success" effect="light"
                  >视频换脸</el-tag
                >
                <el-tag v-if="row.way == 6" type="danger" effect="light"
                  >视频合成高级版</el-tag
                >
                <el-tag v-if="row.way == 7" type="primary" effect="light"
                  >形象克隆高级版</el-tag
                >
                <el-tag v-if="row.way == 8" type="warning" effect="light"
                  >声音克隆高保真合成</el-tag
                >
                <el-tag v-if="row.way == 9" type="danger" effect="light"
                  >视频合成线路三</el-tag
                >

                <el-tag v-if="row.way == 10" type="danger" effect="light"
                  >ai文案创作</el-tag
                >
                <el-tag v-if="row.way == 11" type="danger" effect="light"
                  >视频提交文案</el-tag
                >
                <el-tag v-if="row.way == 12" type="danger" effect="light"
                  >视频合成高级版2</el-tag
                >
                    <el-tag v-if="row.way == 13" type="danger" effect="light"
                  >专业版声音克隆</el-tag
                >
          
                   <el-tag v-if="row.way == 14" type="danger" effect="light"
                  >专业版声音合成</el-tag
                >
                  <el-tag v-if="row.way == 15" type="danger" effect="light"
                  >生成ai标题</el-tag
                >
                  <el-tag v-if="row.way == 16" type="danger" effect="light"
                  >发布抖音</el-tag
                >
                  <el-tag v-if="row.way == 17" type="danger" effect="light"
                  >发布快手</el-tag
                >
                  <el-tag v-if="row.way == 18" type="danger" effect="light"
                  >发布视频号</el-tag
                >
                  <el-tag v-if="row.way == 19" type="danger" effect="light"
                  >发布小红薯</el-tag
                >

                    <el-tag v-if="row.way == 20" type="danger" effect="light"
                  >获取抖音二维码</el-tag
                >
                    <el-tag v-if="row.way == 21" type="danger" effect="light"
                  >抖音授权</el-tag
                >
                    <el-tag v-if="row.way == 22" type="danger" effect="light"
                  >获取视频号二维码</el-tag
                >
                    <el-tag v-if="row.way == 23" type="danger" effect="light"
                  >视频号授权</el-tag
                >
                    <el-tag v-if="row.way == 24" type="danger" effect="light"
                  >获取小红薯二维码</el-tag
                >
                    <el-tag v-if="row.way == 25" type="danger" effect="light"
                  >小红薯授权</el-tag
                >
                   <el-tag v-if="row.way == 26" type="danger" effect="light"
                  >线路4合成</el-tag
                >
                  <el-tag v-if="row.way == 27" type="danger" effect="light"
                  >获取主页视频</el-tag
                >
                  <el-tag v-if="row.way == 28" type="danger" effect="light"
                  >主页视频分析</el-tag
                >
              </template>
               
            

              <!-- 操作列 -->
              <template v-slot:action="{ row }">
                <el-popconfirm
                  class="ele-action"
                  title="您将要删除此数据？"
                  @confirm="remove(row)"
                >
                  <template v-slot:reference>
                    <el-link
                      type="danger"
                      :underline="false"
                      icon="el-icon-delete"
                    >
                      删除
                    </el-link>
                  </template>
                </el-popconfirm>
              </template>
            </ele-pro-table>
          </el-card>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import VueCountUp from 'vue-countup-v2';
import { save, query, logList } from '@/api/system/config';
export default {
  name: 'SysAccount',
  components: { VueCountUp },
  data() {
    return {
      type: 0,
      maturity_time: '',
      balance: 0,
      host: window.location.protocol + '//' + location.host,
      // 提交状态
      loading: false,
      // 表单数据
      form: {
        msg: '',
        account_appid: '',
        account_appsecret: '',
        account_api: '',
        account_apiid: '',
        account_apikey: '',
        account_apisecret: '',
        operation_version: '',
        agent_url: ''
      },
      // 表格列配置
      columns: [
        {
          prop: 'account',
          label: '账号',
          showOverflowTooltip: true,
          minWidth: 110
        },
        {
          prop: 'current',
          label: '操作前点数',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110
        },
        {
          prop: 'point',
          label: '操作点数',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110,
          slot: 'point'
        },
        {
          prop: 'way',
          label: '使用类型',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110,
          slot: 'way'
        },
        {
          prop: 'desc',
          label: '备注',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 110
        },
        {
          prop: 'create_time',
          label: '操作时间',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 100
        }
      ],
      // 表单验证规则
      rules: {
        account_appid: [
          {
            required: true,
            message: '请输入账号appid',
            trigger: 'blur'
          }
        ],
        account_appsecret: [
          {
            required: true,
            message: '请输入账号appsecret',
            trigger: 'blur'
          }
        ]
      }
    };
  },
  methods: {
    /* 表格数据源 */
    datasource({ page, limit, where, order }) {
      return logList({ ...where, ...order, page, limit });
    },
    /* 刷新表格 */
    reload(where) {
      this.$refs.table.reload({ page: 1, where: where });
    },
    query() {
      query({ group: 'account' })
        .then((data) => {
          if (data != null) {
            this.form.msg = data?.msg;
            this.form.account_appsecret = data.account_appsecret;
            this.form.account_appid = data.account_appid;
            this.type = data?.data?.type;
            this.maturity_time = data?.data?.maturity_time;
            this.balance = data?.data?.balance;

            this.form.account_api = data.account_api;
            this.form.account_apiid = data.account_apiid;
            this.form.account_apikey = data.account_apikey;
            this.form.operation_version = data.operation_version;
            this.form.agent_url = data.agent_url;
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },

    /* 提交 */
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true;
          save(this.form)
            .then((msg) => {
              this.loading = false;
              this.$message.success(msg);
              this.query();
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });
        } else {
          return false;
        }
      });
    }
  },
  mounted() {
    this.query();
  }
};
</script>

<style scoped>
.el-form-item {
  margin-left: 100px;
}
.monitor-count-card ::v-deep .el-card__body {
  padding-top: 18px;
  text-align: center;
  position: relative;
}

.monitor-count-card ::v-deep .el-tag {
  border-color: transparent;
  font-size: 15px;
}

.monitor-count-card .monitor-count-card-num {
  font-weight: 500;
  font-size: 32px;
  margin-top: 12px;
}

.monitor-count-card .monitor-count-card-text {
  font-size: 12px;
  margin: 10px 0;
}

.monitor-count-card .monitor-count-card-trend {
  font-weight: 600;
  padding: 6px 0;
}

.monitor-count-card .monitor-count-card-trend > i {
  font-size: 12px;
  font-weight: 600;
  margin-right: 5px;
}

.monitor-count-card .monitor-count-card-tips {
  position: absolute;
  top: 15px;
  right: 15px;
  cursor: pointer;
}
</style>
