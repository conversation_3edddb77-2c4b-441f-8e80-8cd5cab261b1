import axios from '@/utils/request';

/**
 * 获取已上传的视频列表
 * @param params 查询条件 { page, limit, type_id }
 */
export async function getFinishVideoList(params) {
  const res = await axios.post('/finish_video/index', params);
  if (res.data.errno === 0) {
    return {
      list: res.data.data.list,
      total: res.data.data.total,
      page: res.data.data.page,
      size: res.data.data.size
    };
  }
  return Promise.reject(new Error(res.data.message || '获取视频列表失败'));
}

/**
 * 获取分类列表
 */
export async function getVideoTypeList() {
  const res = await axios.post('/finish_video/type');
  if (res.data.errno === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message || '获取分类列表失败'));
}

/**
 * 添加视频
 * @param data { url, type_id, name, cover }
 */
export async function addFinishVideo(data) {
  const res = await axios.post('/finish_video/add', data);
  if (res.data.errno === 0) {
    return res.data.message || '添加成功';
  }
  return Promise.reject(new Error(res.data.message || '添加失败'));
}

/**
 * 修改视频
 * @param data { id, url, type_id, name, cover }
 */
export async function updateFinishVideo(data) {
  const res = await axios.post('/finish_video/upd', data);
  if (res.data.errno === 0) {
    return res.data.message || '修改成功';
  }
  return Promise.reject(new Error(res.data.message || '修改失败'));
}

/**
 * 删除视频
 * @param id 视频ID
 */
export async function deleteFinishVideo(id) {
  const res = await axios.post('/finish_video/del', { id });
  if (res.data.errno === 0) {
    return res.data.message || '删除成功';
  }
  return Promise.reject(new Error(res.data.message || '删除失败'));
}

/**
 * 新增分类
 * @param name 分类名称
 */
export async function addVideoType(name) {
  const res = await axios.post('/finish_video/type_add', { name });
  if (res.data.errno === 0) {
    return res.data.message || '添加成功';
  }
  return Promise.reject(new Error(res.data.message || '添加失败'));
}

/**
 * 修改分类
 * @param data { id, name }
 */
export async function updateVideoType(data) {
  const res = await axios.post('/finish_video/type_upd', data);
  if (res.data.errno === 0) {
    return res.data.message || '修改成功';
  }
  return Promise.reject(new Error(res.data.message || '修改失败'));
}

/**
 * 删除分类
 * @param id 分类ID
 */
export async function deleteVideoType(id) {
  const res = await axios.post('/finish_video/type_del', { id });
  if (res.data.errno === 0) {
    return res.data.message || '删除成功';
  }
  return Promise.reject(new Error(res.data.message || '删除失败'));
}

/**
 * 上传视频文件
 * @param file 文件对象
 */
export async function uploadVideoFile(file) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', 1);

  const res = await axios.post('/common/uploadFile', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });

  if (res.data.code === 0) {
    return res.data.data.url || res.data.data;
  }
  return Promise.reject(new Error(res.data.message || '上传失败'));
}

/**
 * 上传图片文件
 * @param file 文件对象
 */
export async function uploadImageFile(file) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', 2); // 图片类型

  const res = await axios.post('/common/uploadFile', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });

  if (res.data.code === 0) {
    return res.data.data.url || res.data.data;
  }
  return Promise.reject(new Error(res.data.message || '上传失败'));
}
