<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">上传配置</div>
      <div class="ele-page-desc">
        用于图片上传保存等场景。
      </div>
    </div>
    <div class="ele-body">
      <el-card shadow="never">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="120px"
          style="max-width: 700px; margin: 10px auto"
        >

          <el-form-item label="存储方式:">
            <el-radio-group v-model="form.upload_type">
              <el-radio label="alioss" value="alioss">阿里云oss</el-radio>
              <!-- <el-radio label="qnoss" value="qnoss">七牛云oss</el-radio>
              <el-radio label="txcos" value="txcos">腾讯云oss</el-radio> -->
            </el-radio-group>
          </el-form-item>

          <el-form-item  label="允许类型:" prop="upload_allow_ext">
            <el-input v-model="form.upload_allow_ext" placeholder="请输入" clearable />
            <div class="text-info">英文逗号做分隔符</div>
          </el-form-item>

          <el-form-item  label="允许大小:" prop="upload_allow_size">
            <el-input v-model="form.upload_allow_size" placeholder="请输入" clearable />
            <div class="text-info">设置允许上传大小，单位：b</div>
          </el-form-item>


          <el-form-item v-if="form.upload_type === 'alioss'" label="公钥信息:" prop="alioss_access_key_id">
            <el-input v-model="form.alioss_access_key_id" placeholder="请输入阿里云公钥信息" clearable />
            <div class="text-info">示例：FSGGshu64642THSk</div>
          </el-form-item>

          <el-form-item v-if="form.upload_type === 'alioss'" label="私钥信息:" prop="alioss_access_key_secret">
            <el-input v-model="form.alioss_access_key_secret" type="password" show-password placeholder="请输入阿里云私钥信息" clearable />
            <div class="text-info">示例：5fsfPReYKkFSGGshu64642THSkmTInaIm</div>
          </el-form-item>

          <el-form-item v-if="form.upload_type === 'alioss'" label="数据中心:" prop="alioss_endpoint">
            <el-input v-model="form.alioss_endpoint" placeholder="请输入阿里云数据中心" clearable />
            <div class="text-info">示例：https://oss-cn-shenzhen.aliyuncs.com</div>
          </el-form-item>

 


            <el-form-item v-if="form.upload_type === 'alioss'" label="智能媒体服务数据存储节点:" prop="alioss_clip_endpoint">
            <el-input v-model="form.alioss_clip_endpoint" placeholder="请输入阿里云智能媒体服务数据存储节点" clearable />
            <div class="text-info">示例：ice.cn-hangzhou.aliyuncs.com</div>
          </el-form-item>

           <!-- <el-form-item v-if="form.upload_type === 'alioss'" label="智能媒体管理项目名称:" prop="alioss_manage_name">
            <el-input v-model="form.alioss_manage_name" placeholder="智能媒体管理项目名称" clearable />
            <div class="text-info">示例：imm.cn-hangzhou.aliyuncs.com</div>
          </el-form-item>

           <el-form-item v-if="form.upload_type === 'alioss'" label="智能媒体管理数据存储节点:" prop="alioss_manage_endpoint">
            <el-input v-model="form.alioss_manage_endpoint" placeholder="请输入阿里云智能媒体管理数据存储节点" clearable />
            <div class="text-info">示例：imm.cn-hangzhou.aliyuncs.com</div>
          </el-form-item> -->

          <el-form-item v-if="form.upload_type === 'alioss'" label="空间名称:" prop="alioss_bucket">
            <el-input v-model="form.alioss_bucket" placeholder="请输入阿里云BUCKET空间名称" clearable />
            <div class="text-info">示例：oss-template</div>
          </el-form-item>

           <el-form-item v-if="form.upload_type === 'alioss'" label="访问域名:" prop="alioss_domain">
            <el-input v-model="form.alioss_domain" placeholder="请输入阿里云访问域名" clearable />
            <div class="text-info">示例：oss-template.oss-cn-shenzhen.aliyuncs.com</div>
          </el-form-item>


          <el-form-item v-if="form.upload_type === 'qnoss'" label="公钥信息:" prop="qnoss_access_key">
            <el-input v-model="form.qnoss_access_key" placeholder="请输入七牛云公钥信息" clearable />
            <div class="text-info">示例：v-lV3tXev7yyfsfa1jRc6_8rFOhFYGQvvjsAQxdrB</div>
          </el-form-item>

          <el-form-item v-if="form.upload_type === 'qnoss'" label="私钥信息:" prop="qnoss_secret_key">
            <el-input v-model="form.qnoss_secret_key" type="password" show-password placeholder="请输入七牛云私钥信息" clearable />
            <div class="text-info">示例：XOhYRR9JNqxsWVEO-mHWB4193vfsfsQADuORaXzr</div>
          </el-form-item>

          <el-form-item v-if="form.upload_type === 'qnoss'" label="存储空间:" prop="qnoss_bucket">
            <el-input v-model="form.qnoss_bucket" placeholder="请输入七牛云存储空间" clearable />
            <div class="text-info">示例：qntemplate</div>
          </el-form-item>

           <el-form-item v-if="form.upload_type === 'qnoss'" label="访问域名:" prop="qnoss_domain">
            <el-input v-model="form.qnoss_domain" placeholder="请输入七牛云访问域名" clearable />
            <div class="text-info">示例：https://q0xqzappp.bkt.clouddn.com</div>
          </el-form-item>


          <el-form-item v-if="form.upload_type === 'txcos'" label="公钥信息:" prop="txcos_secret_id">
            <el-input v-model="form.txcos_secret_id" placeholder="请输入腾讯云公钥信息" clearable />
            <div class="text-info">示例：AKIDta6OQCbALQGrCI6ngKwQffR3dfsfrwrfs</div>
          </el-form-item>

          <el-form-item v-if="form.upload_type === 'txcos'" label="私钥信息:" prop="txcos_secret_key">
            <el-input v-model="form.txcos_secret_key" type="password" show-password placeholder="请输入腾讯云私钥信息" clearable />
            <div class="text-info">示例：VllEWYKtClAbpqfFdTqysXxGQM6dsfs</div>
          </el-form-item>

          <el-form-item v-if="form.upload_type === 'txcos'" label="存储桶地域:" prop="txcos_region">
            <el-input v-model="form.txcos_region" placeholder="请输入腾讯云存储桶地域" clearable />
            <div class="text-info">示例：ap-guangzhou</div>
          </el-form-item>

           <el-form-item v-if="form.upload_type === 'txcos'" label="存储桶名称:" prop="txcos_bucket">
            <el-input v-model="form.txcos_bucket" placeholder="请输入腾讯云访问域名" clearable />
            <div class="text-info">示例：txcos-1234567890</div>
          </el-form-item>
          
         
          <el-form-item>
            <el-button type="primary" :loading="loading" @click="submit">
              提交
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
  import { save ,query } from '@/api/system/config';
  export default {
    name: 'SysUpload',
    data() {
      return {
        // 提交状态
        loading: false,
        // 表单数据
        form: {
          upload_type:'local',
          upload_allow_ext:'doc,gif,ico,icon,jpg,mp3,mp4,p12,pem,png,rar,jpeg,php',
          upload_allow_size:'102400000',
          alioss_access_key_id: '',
          alioss_access_key_secret: '',
          alioss_endpoint: '',
          alioss_clip_endpoint:'',
          alioss_bucket: '',
          alioss_domain: '',
          qnoss_access_key:'',
          qnoss_secret_key:'',
          qnoss_bucket:'',
          qnoss_domain:'',
          txcos_secret_id:'',
          txcos_secret_key:'',
          txcos_region:'',
          txcos_bucket:'',
        },

        // 表单验证规则
        rules: {
          alioss_access_key_id: [
            {
              required: true,
              message: '请输入阿里云公钥',
              trigger: 'blur'
            }
          ],
          alioss_access_key_secret: [
            {
              required: true,
              message: '请输入阿里云秘钥',
              trigger: 'blur'
            }
          ],
          alioss_endpoint: [
            {
              required: true,
              message: '请输入阿里云数据中心',
              trigger: 'blur'
            }
          ],
          alioss_bucket: [
            {
              required: true,
              message: '请输入阿里云资BUCKET空间名称',
              trigger: 'blur'
            }
          ],
          alioss_domain: [
            {
              required: true,
              message: '请输入阿里云资源访问路径',
              trigger: 'blur'
            }
          ],
          qnoss_access_key: [
            {
              required: true,
              message: '请输入七牛云公钥',
              trigger: 'blur'
            }
          ],
          qnoss_secret_key: [
            {
              required: true,
              message: '请输入七牛云私钥',
              trigger: 'blur'
            }
          ],
          qnoss_bucket: [
            {
              required: true,
              message: '请输入七牛云存储空间',
              trigger: 'blur'
            }
          ],
          qnoss_domain: [
            {
              required: true,
              message: '请输入七牛云访问域名',
              trigger: 'blur'
            }
          ],
          txcos_secret_id: [
            {
              required: true,
              message: '请输入腾讯云公钥',
              trigger: 'blur'
            }
          ],
          txcos_secret_key: [
            {
              required: true,
              message: '请输入腾讯云私钥',
              trigger: 'blur'
            }
          ],
          txcos_region: [
            {
              required: true,
              message: '请输入腾讯云存储桶地域',
              trigger: 'blur'
            }
          ],
          txcos_bucket: [
            {
              required: true,
              message: '请输入腾讯云存储桶名称',
              trigger: 'blur'
            }
          ],
        },
        
      };
    },
    methods: {
      /* 提交 */
      submit() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            this.loading = true;
          this.loading = true;
          save(this.form)
            .then((msg) => {
              this.loading = false;
              this.$message.success(msg);
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });


          } else {
            return false;
          }
        });
      },
   
    },
     mounted() {
           query({group:'upload'})
            .then((msg) => {
             if(msg != null){
                this.form = msg;  
              }
              
            })
            .catch((e) => {
              this.$message.error(e.message);
            });
     }
    
  };
</script>

<style scoped>
.el-form-item{
  margin-left: 100px;
}
</style>
