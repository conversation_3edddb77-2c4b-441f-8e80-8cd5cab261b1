<template>
  <div class="ele-body">
       <!-- 搜索表单 -->
  
    <el-card shadow="never">
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
    
      >
        <!-- 表头工具栏 -->
      
      

       

        <!-- 状态列 -->
        <template v-slot:status="{ row }">
          <el-switch
            :active-value="1"
            :inactive-value="2"
            v-model="row.status"
            @change="editStatus(row)"
          />
        </template>


        <!-- 状态列 -->
     


        <template v-slot:sort>
          <i class="sort-handle el-icon-rank ele-text-secondary" style="cursor: move;"></i>
        </template>

        
        <!-- 操作列 -->
        <template v-slot:action="{ row }">
          <el-link
            type="primary"
            :underline="false"
            icon="el-icon-edit"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          
        </template>
      </ele-pro-table>
    </el-card>
    <!-- 编辑弹窗 -->
    <way-edit :visible.sync="showEdit" :data="current"  @done="reload"/>
  </div>
</template>

<script>


  import { list, remove, modify,sortChange,doDefault  } from '@/api/way/list';
    import wayEdit from './components/way-edit';
  import Sortable from 'sortablejs';

  export default {
    name: 'way',
    components: {
        wayEdit
   
   
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },
          {
            prop: 'sort',
            label: '排序',
            showOverflowTooltip: true,
            minWidth: 60,
            slot: 'sort'
          },
           {
            prop: 'name',
            label: '小程序端显示名称',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'reception',
            label: '线路名称',
            showOverflowTooltip: true,
            minWidth: 110
          },
       
        
          {
            prop: 'status',
            label: '是否开启',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'status'
          },
          
        
          {
            columnKey: 'action',
            label: '操作',
            width: 250,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            showOverflowTooltip: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示分配回收员
        showCollector: false,
        //弹框名称
        id:0,
      };
    },
    methods: {
      rowDrop() {
        const tbody = document.querySelector('.el-table__body-wrapper tbody')
        const _this = this
        Sortable.create(tbody, {
          forceFallback: true,
          dragClass: 'drag',
          delay:100,
          animation: 1000,
          onEnd({ newIndex, oldIndex }) {
            console.log(newIndex);
            console.log(oldIndex);
            //更换表格数据 后续拖动坐标才会正确
            let data = _this.$refs.table.getData();
             
            let nid = data[newIndex].id;
            let oid = data[oldIndex].id;

            console.log(nid);
            console.log(oid);
            
            const currRow = data.splice(oldIndex, 1)[0];
            data.splice(newIndex, 0, currRow);

            sortChange(data).then((msg) => {
              
            }).catch((e) => {
              _this.$refs.table.reload();
            });
            //去数据库更换排序
            console.log(_this.$refs.table.getData());
          }
        })
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return list({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id).then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
      },

      /* 一键默认 */
      doDefault() {
        
        this.$confirm('请谨慎使用该功能，一旦使用则会变成系统默认内容。确定一键默认？', '提示', {
          type: 'warning'
        }).then(() => {
          const loading = this.$loading({ lock: true });
          doDefault().then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
        }).catch(() => {});
      },
      
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的数据吗?', '提示', {
          type: 'warning'
        }).then(() => {
          const loading = this.$loading({ lock: true });
          remove(this.selection.map((d) => d.id))
            .then((msg) => {
              loading.close();
              this.$message.success(msg);
              this.reload();
            })
            .catch((e) => {
              loading.close();
              this.$message.error(e.message);
            });
        }).catch(() => {});
      },
      
      /* 更改状态 */
      editStatus(row) {
        const loading = this.$loading({ lock: true });
        modify({id:row.id,field:'status',value: row.status})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            row.status = row.status == 1 ? 2 : 1;
            this.$message.error(e.message);
          });
      },
       /* 更改状态 */
       editRecommend(row) {
        const loading = this.$loading({ lock: true });
        modify({id:row.id,field:'is_recommend',value: row.is_recommend})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            row.is_recommend = row.is_recommend  == 1 ? 2 : 1;
            this.$message.error(e.message);
          });
      }
    },mounted() {
      this.rowDrop()
      //this.columnDrop()
    }
  };
</script>

<style>
 .drag {
    background: #000 !important;
    background-image: linear-gradient(#333, #999) !important;
  }
</style>
