<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <log-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            class="ele-btn-icon"
            @click="removeBatch"
          >
            删除
          </el-button>
        </template>

        <template v-slot:user="{ row }">
          <div class="ele-cell" v-if="row.figureUser">
            <el-avatar
              v-if="row.figureUser.avatar"
              :src="row.figureUser.avatar"
              shape="square"
              :size="40"
            />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.figureUser.nickname }} </div>
              <div class="ele-cell-desc">{{ row.figureUser.telphone }} </div>
            </div>
          </div>
          <span v-else>无</span>
        </template>

        <template v-slot:name="{ row }">
          <el-tag v-if="row.type == 1" size="medium" type="primary"
            >线路一</el-tag
          >
          <el-tag v-if="row.type == 3" size="medium" type="red">线路二</el-tag>
          <el-tag v-if="row.type == 2" size="medium" type="success"
            >线路三</el-tag
          >
              <el-tag v-if="row.type == 4" size="medium" type="success"
            >线路四</el-tag
          >

          <span style="margin-left: 10px">{{ row.name }}</span>
        </template>

        <template v-slot:anchor_type="{ row }">
          <el-tag v-if="row.anchor_type == 0" type="primary" effect="plain"
            >默认形象</el-tag
          >
          <el-tag v-if="row.anchor_type == 1" type="success" effect="plain"
            >自定义形象</el-tag
          >
        </template>

        <template v-slot:current_status="{ row }">
          <el-tag v-if="row.current_status == 'success'" type="success"
            >生成成功</el-tag
          >
           <el-tag v-if="row.current_status == 'wait_sent'" 
            >等待生成</el-tag
          >
          <el-tag v-else-if="row.current_status == 'fail'" type="danger"
            >生成失败   <el-tag v-if="row.is_refund == '1'">已退款</el-tag>
            <el-tag v-if="row.is_refund != '1'">未退款</el-tag></el-tag
          >
          <div v-if="row.faild_message" style="margin-top: 5px; font-size: 2px">
            失败原因：<span style="font-weight: bold; color: red">{{
              row.faild_message
            }}</span>
          </div>
          <el-tag
            v-else-if="
         
              row.current_status == '' ||
              row.current_status == 'copy' ||
              row.current_status == 'init' ||
              row.current_status == 'start' ||
              row.current_status == 'pending' ||
              row.current_status == 'process'
            "
            >生成中</el-tag
          >
        </template>

        <template v-slot:result="{ row }">
          <video
            width="320"
            height="200"
            controls
            :src="row.local_url"
            v-if="row.local_url"
          >
          </video>
          <video
            width="320"
            height="200"
            controls
            :src="row.result"
            v-else-if="row.result"
          >
          </video>
          <span v-else>--</span>
        </template>

    

        <!-- 操作列 -->
        <template v-slot:action="{ row }">
          <el-link
            type="success"
            :underline="false"
            icon="el-icon-position"
            @click="openRepost(row)"
          >
            转发
          </el-link>
          <div v-if="row.is_local == 1">
            <el-link
              type="warm"
              :underline="false"
              icon="el-icon-position"
              @click="toLocal(row)"
            >
              重新下载
            </el-link>
          </div>

          <el-popconfirm
            class="ele-action"
            title="确定要删除此数据吗？"
            @confirm="remove(row)"
          >
            <template v-slot:reference>
              <el-link type="danger" :underline="false" icon="el-icon-delete">
                删除
              </el-link>
            </template>
          </el-popconfirm>

         <el-popconfirm
            v-if="row.current_status == 'fail' && row.is_refund != 1"
            class="ele-action"
            title="退款"
            @confirm="refund(row)"
          >
            <template v-slot:reference>
              <el-link type="refund" :underline="false" icon="el-icon-key">
                退款
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>

    <ele-modal
      width="80%"
      :close-on-click-modal="false"
      custom-class="ele-dialog-form"
      :title="videoName + '转发数据'"
      :visible.sync="showRepost"
    >
      <el-row>
        <el-col>
          <el-form label-width="130px" style="margin-bottom: -15px">
            <el-row :gutter="20">
              <el-col
                :md="12"
                :sm="12"
                :xs="12"
                v-if="current && current.dy_qrcode"
              >
                <el-form-item label="D音二维码">
                  <el-image
                    style="width: 80px; height: 80px"
                    :src="current.dy_qrcode"
                    :preview-src-list="dyPic"
                    :zIndex="9999"
                  />
                </el-form-item>
              </el-col>
              <el-col
                :md="12"
                :sm="12"
                :xs="12"
                v-if="current && current.ks_qrcode"
              >
                <el-form-item label="K手二维码">
                  <el-image
                    style="width: 80px; height: 80px"
                    :src="current.ks_qrcode"
                    :preview-src-list="ksPic"
                    :zIndex="9999"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <video-repost :data="current" :videoId="id" @done="reload" />
        </el-col>
      </el-row>
    </ele-modal>
  </div>
</template>

<script>
import VideoRepost from '@/views/repost/list';
import LogSearch from './components/log-search';
import { list, remove, modify, sortChange, toLocal,refund } from '@/api/video';

export default {
  name: 'Video',
  components: {
    VideoRepost,
    LogSearch
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          columnKey: 'selection',
          type: 'selection',
          width: 45,
          align: 'center',
          fixed: 'left'
        },
        {
          prop: 'user',
          label: '用户',
          showOverflowTooltip: true,
          minWidth: 110,
          slot: 'user'
        },
        {
          prop: 'name',
          label: '名称',
          showOverflowTooltip: true,
          minWidth: 100,
          slot: 'name'
        },
        {
          prop: 'anchor_type',
          label: '形象类型',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 90,
          slot: 'anchor_type'
        },
        {
          prop: 'current_status',
          label: '状态',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 90,
          slot: 'current_status'
        },
        {
          prop: 'result',
          label: '结果链接',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 200,
          slot: 'result'
        },

     
        {
          prop: 'create_time',
          label: '创建时间',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 90
        },
        {
          columnKey: 'action',
          label: '操作',
          width: 240,
          align: 'center',
          resizable: false,
          slot: 'action',
          hideInSetting: true,
          showOverflowTooltip: true
        }
      ],
      // 表格选中数据
      selection: [],
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showEdit: false,
      // 是否显示分配回收员
      showRepost: false,
      //弹框名称
      //弹框名称
      id: 0,
      videoName: '',
      dyPic: [],
      ksPic: []
    };
  },
  methods: {
    /* 表格数据源 */
    datasource({ page, limit, where, order }) {
      return list({ ...where, ...order, page, limit });
    },
    /* 刷新表格 */
    reload(where) {
      this.$refs.table.reload({ page: 1, where: where });
    },
    /* 打开编辑弹窗 */
    openEdit(row) {
      this.current = row;
      this.showEdit = true;
    },
    /* 打开编辑弹窗 */
    openRepost(row) {
      this.id = row.id;
      this.current = row;
      this.showRepost = true;
      this.videoName = row.name;
      this.dyPic = [row.dy_qrcode];
      this.ksPic = [row.ks_qrcode];
    },
    /* 删除 */
    remove(row) {
      const loading = this.$loading({ lock: true });
      remove(row.id)
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
    },

    refund(row) {
      const loading = this.$loading({ lock: true });
      refund(row.id)
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
    },

    /* 批量删除 */
    removeBatch() {
      if (!this.selection.length) {
        this.$message.error('请至少选择一条数据');
        return;
      }
      this.$confirm('确定要删除选中的数据吗?', '提示', {
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading({ lock: true });
          remove(this.selection.map((d) => d.id))
            .then((msg) => {
              loading.close();
              this.$message.success(msg);
              this.reload();
            })
            .catch((e) => {
              loading.close();
              this.$message.error(e.message);
            });
        })
        .catch(() => {});
    },

    toLocal(row) {
      const loading = this.$loading({ lock: true });
      toLocal({ id: row.id })
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          row.status = !row.status ? 1 : 0;
          this.$message.error(e.message);
        });
    },

    /* 更改状态 */
    editStatus(row) {
      const loading = this.$loading({ lock: true });
      modify({ id: row.id, field: 'is_display', value: row.is_display })
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
        })
        .catch((e) => {
          loading.close();
          row.status = !row.status ? 1 : 0;
          this.$message.error(e.message);
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.drag {
  background: #000 !important;
  background-image: linear-gradient(#333, #999) !important;
}

.ele-cell-content {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
