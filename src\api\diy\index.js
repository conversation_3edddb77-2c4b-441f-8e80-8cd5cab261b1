import axios from '@/utils/request';

/**
 * 分页查询角色
 * @param params 查询条件
 */
export async function categoryList(params) {
  const res = await axios.get('/mealInfo/list', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 保存diy设计
 * @param 设计表单数据
 */
 export async function saveDiy(id,params) {
  const res = await axios.post('/diy/saveDiy/'+id, 
    params
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询diy模板
 * @param params 查询模板
 */
 export async function getDiyInfo(id) {
  const res = await axios.get('/diy/getDiyInfo/'+id);
  debugger;
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


