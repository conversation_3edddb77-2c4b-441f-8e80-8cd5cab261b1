<template>
  <div class="ele-body">
    <el-card shadow="never">
      <el-row :gutter="31" style="margin-bottom: 20px;">
        <el-col :sm="12" :xs="12" :md="4" :span="4">
          <div class="piece-all flex-align-center">
            <el-avatar :size="43" class="icon-right" :src="require('@/assets/reposts/1.png')" />
            <div class="flex-columns piece-text">
              <div>{{ dyPlay  }}</div>
              <span>曝光量</span>
            </div>
          </div>
        </el-col>
        <el-col :sm="12" :xs="12" :md="4" :span="4">
          <div class="piece-all flex-align-center">
            <el-avatar :size="43" class="icon-right" :src="require('@/assets/reposts/2.png')" />
            <div class="flex-columns piece-text">
              <div>{{ dyLike }}</div>
              <span>点赞量</span>
            </div>
          </div>
        </el-col>
        <el-col :sm="12" :xs="12" :md="4" :span="4">
          <div class="piece-all flex-align-center">
            <el-avatar :size="43" class="icon-right" :src="require('@/assets/reposts/3.png')" />
            <div class="flex-columns piece-text">
              <div>{{ dyComment }}</div>
              <span>评论量</span>
            </div>
          </div>
        </el-col>
        <el-col :sm="12" :xs="12" :md="4" :span="4">
          <div class="piece-all  flex-align-center">
            <el-avatar :size="43" class="icon-right" :src="require('@/assets/reposts/4.png')" />
            <div class="flex-columns piece-text">
              <div>{{ ksPlay }}</div>
              <span>曝光量</span>
            </div>
          </div>
        </el-col>
        <el-col :sm="12" :xs="12" :md="4" :span="4">
          <div class="piece-all flex-align-center">
            <el-avatar :size="43" class="icon-right" :src="require('@/assets/reposts/5.png')" />
            <div class="flex-columns piece-text">
              <div>{{ ksLike }}</div>
              <span>点赞量</span>
            </div>
          </div>
        </el-col>
        <el-col :sm="12" :xs="12" :md="4" :span="4">
          <div class="piece-all flex-align-center">
            <el-avatar :size="43" class="icon-right" :src="require('@/assets/reposts/6.png')" />
            <div class="flex-columns piece-text">
              <div>{{ ksComment }}</div>
              <span>评论量</span>
            </div>
          </div>
        </el-col>
      </el-row>
      <!-- 搜索表单 -->
      <log-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            class="ele-btn-icon"
            @click="removeBatch"
          >
            删除
          </el-button>
        </template>

      

        <template v-slot:user="{ row }">
          <div class="ele-cell" v-if="row.figureUser">
            <el-avatar v-if="row.figureUser.avatar" :src="row.figureUser.avatar" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.figureUser.nickname }} </div>
              <div class="ele-cell-desc">{{ row.figureUser.telphone }} </div>
            </div>
          </div>
          <span v-else>无</span>
          
        </template>

        <template v-slot:repost="{ row }">

        
          <div class="ele-cell" v-if="row.figureDyUser">
            <el-avatar v-if="row.figureDyUser.avatar" :src="row.figureDyUser.avatar" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.figureDyUser.nickname }} </div>
            </div>
          </div>

          <div class="ele-cell" v-if="row.figureKsUser">
            <el-avatar v-if="row.figureKsUser.avatar" :src="row.figureKsUser.avatar" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.figureKsUser.nickname }} </div>
            </div>
          </div>

        </template>

        <template v-slot:video="{ row }">


          <div class="ele-cell" v-if="row.figureVideo">
            
            <el-tag v-if="row.type == 1 " size="small" effect="light" type="primary" class="ele-tag-round">合</el-tag>
            <el-tag v-if="row.type == 2 " size="small" effect="light" type="success" class="ele-tag-round">换</el-tag>
            <el-avatar v-if="row.video_cover" :src="row.video_cover" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.name }} </div>
            </div>
          </div>
          <span v-else>无</span>
        </template>


        <template v-slot:way="{ row }">
          <el-tag v-if="row.way==1" type="primary" effect="plain">D音</el-tag>
          <el-tag v-if="row.way==2" type="warning" effect="plain">K手</el-tag>
        </template>

        <template v-slot:share_url="{ row }">
          <a :href="row.share_url" target ="_blank" v-if="row.share_url">播放链接</a>
          <a :href="row.video_url" target ="_blank" v-else>播放链接</a>
        </template>

        
        <!-- 操作列 -->
        <template v-slot:action="{ row }">
         
          <el-popconfirm
            class="ele-action"
            title="确定要删除此数据吗？"
            @confirm="remove(row)"
          >
            <template v-slot:reference>
              <el-link type="danger" :underline="false" icon="el-icon-delete">
                删除
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
  </div>
</template>

<script>
  
  import LogSearch from './components/log-search';
  import { repost } from '@/api/clip';

  export default {
    name: 'VideoRepost',
    components: {
      LogSearch,
    },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      videoId: {
        type: Number,
        default: 0
      },
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },
          // {
          //   prop: 'user',
          //   label: '用户',
          //   showOverflowTooltip: true,
          //   minWidth: 110,
          //   slot: 'user'
          // },
          {
            prop: 'video',
            label: '视频',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'video'
          },
          {
            prop: 'repost',
            label: '用户',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'repost'
          },
          
          {
            prop: 'way',
            label: '渠道',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 90,
            slot: 'way'
          },
          {
            prop: 'share_url',
            label: '链接',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 90,
            slot: 'share_url'
          },
          {
            prop: 'total_play',
            label: '曝光',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 90
          },
          {
            prop: 'total_like',
            label: '点赞',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 90
          },
          {
            prop: 'total_comment',
            label: '评论',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 90
          },
          
          {
            prop: 'total_share',
            label: '分享',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 90
          },
          
          {
            prop: 'create_time',
            label: '创建时间',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 90,
           
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 150,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            showOverflowTooltip: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示分配回收员
        showCollector: false,
        //弹框名称
        id:0,
        dyPlay:0,
        dyLike:0,
        dyComment:0,
        ksPlay:0,
        ksLike:0,
        ksComment:0,
      };
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        where.video_id = this.videoId;
        return repost({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id).then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
      },
    
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的数据吗?', '提示', {
          type: 'warning'
        }).then(() => {
          const loading = this.$loading({ lock: true });
          remove(this.selection.map((d) => d.id))
            .then((msg) => {
              loading.close();
              this.$message.success(msg);
              this.reload();
            })
            .catch((e) => {
              loading.close();
              this.$message.error(e.message);
            });
        }).catch(() => {});
      },
      dataStatis(){
        
        statis({video_id:this.videoId}).then((data) => {
          if(data != null){
         
            this.dyPlay = data.dyPlay; 
            this.dyLike = data.dyLike;   
            this.dyComment = data.dyComment;  
            this.ksPlay = data.ksPlay; 
            this.ksLike = data.ksLike;   
            this.ksComment = data.ksComment;  

          }
          
        }).catch((e) => {
          this.$message.error(e.message);
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
    },
    mounted() {
      this.dataStatis();
    },
    watch: {
      videoId(){
        this.reload();
        this.dataStatis();
      }
    },
  };
</script>

<style lang="scss" scoped>

  .drag {
    background: #000 !important;
    background-image: linear-gradient(#333, #999) !important;
  }

  .ele-cell-content {
   
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .icon {
    width: 32px;
    height: 32px;
  }

  .singlePalaceGrid {
    flex: 50%;
    height: 93px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding-left: 28px;
  }
  .flex-column {
      flex-direction: column;
      justify-content: center;
  }
  .singlePalaceGrid .accumulateNum {
    margin-left: 8px;
    height: 62px;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
  }
  .singlePalaceGrid .accumulateNum .Num {
    color: #1e6ceb;
    font-size: 20px;
  }

  .flex-align-center {
    display: flex;
    align-items: center;
  }
 
  .purple-bj{
    background-color: #EEE7FF;
  }
  .blue-bj{
    background-color: #E2F6F6;
  }
  .yellow-bj{
    background-color: #FFF0E1;
  }
  .green-bj{
    background-color: #E4FFD5;
  }
  .red-bj{
    background-color: #FFD8D8;
  }
  .piece-all{
    border-radius: 10px;
    height: 94px;
    padding: 0 11px;
    .icon-right{
      margin-right: 12px;
    }
    .piece-text{
      div{
        color: #333;
        font-size: 30px;
        font-weight: bold;
      }
      span{
        color: #666;
        font-size: 16px;
      }
    }
  }

</style>
