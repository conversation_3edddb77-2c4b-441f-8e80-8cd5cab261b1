<!-- 用户信息 -->
<template>
  <el-card shadow="never" body-style="padding: 20px;">
    <div class="ele-cell workplace-user-card">
      <div class="ele-cell-content ele-cell">
        <el-avatar :size="68" src="/user.png" />
        <div class="ele-cell-content">
          <h4 class="ele-elip">
            <span v-if="(new Date().getHours())<10">早安,</span>
            <span v-if="(new Date().getHours())<12 && (new Date().getHours())>10">上午好,</span>
            <span v-if="(new Date().getHours())<18 && (new Date().getHours())>12">下午好,</span>
            <span v-if="(new Date().getHours())<23 && (new Date().getHours())>18">晚上好,</span>
            {{ loginUser.nickname }} , 开始您一天的工作吧!
          </h4>
          <div class="ele-text-secondary ele-elip" style="margin-top: 8px">
            <i class="el-icon-sunny"></i>
            <em>今天又是新的一天。</em>
          </div>
        </div>
      </div>
      <div class="workplace-count-group">
        <div class="workplace-count-item">
          <div class="workplace-count-header">
            <el-tag size="small" class="ele-tag-round">
              <i class="el-icon-time"></i>
            </el-tag>
            <span class="workplace-count-name">回收订单数</span>
          </div>
          <div class="workplace-count-num ele-text-heading">{{ orderData.reserve_count }}</div>
        </div>
        <div class="workplace-count-item">
          <div class="workplace-count-header">
            <el-tag type="warning" size="small" class="ele-tag-round">
              <i class="el-icon-connection"></i>
            </el-tag>
            <span class="workplace-count-name">待审核合伙人</span>
          </div>
          <div class="workplace-count-num ele-text-heading">{{ orderData.uncheck_partner_count }}</div>
        </div>
        <div class="workplace-count-item">
          <div class="workplace-count-header">
            <el-tag type="warning" size="small" class="ele-tag-round">
              <i class="el-icon-_infinite"></i>
            </el-tag>
            <span class="workplace-count-name">待审核回收员</span>
          </div>
          <div class="workplace-count-num ele-text-heading">{{ orderData.uncheck_collector_count }}</div>
        </div>
        <div class="workplace-count-item">
          <div class="workplace-count-header">
            <el-tag type="warning" size="small" class="ele-tag-round">
              <i class="el-icon-wind-power"></i>
            </el-tag>
            <span class="workplace-count-name">待审核回收站</span>
          </div>
          <div class="workplace-count-num ele-text-heading">{{ orderData.uncheck_station_count }}</div>
        </div>
        <div class="workplace-count-item">
          <div class="workplace-count-header">
            <el-tag type="warning" size="small" class="ele-tag-round">
              <i class="el-icon-box"></i>
            </el-tag>
            <span class="workplace-count-name">待审核打包站</span>
          </div>
          <div class="workplace-count-num ele-text-heading">{{ orderData.uncheck_pack_count }}</div>
        </div>
        <div class="workplace-count-item">
          <div class="workplace-count-header">
            <el-tag type="warning" size="small" class="ele-tag-round">
              <i class="el-icon-_rmb"></i>
            </el-tag>
            <span class="workplace-count-name">待处理提现</span>
          </div>
          <div class="workplace-count-num ele-text-heading">{{ orderData.tx_count }}</div>
        </div>
        

        
      </div>
    </div>
  </el-card>
</template>

<script>
import { getAgencyProfileData  } from '@/api/user/info';
  export default {
    name: 'ProfileCard',
    computed: {
      // 当前登录用户信息
      loginUser() {
        return this.$store.state.user.info;
      }
    },
    data() {
      return {
       
        // 表单数据
        orderData: {
          order_count:0,
          dai_kp_count:0,
          tx_count:0,
          use_yunvideo_count:0,
          can_yunvideo_count:0,
          use_video_count:0,
          can_video_count:0
        },

        
      };
    },
    mounted() {
      getAgencyProfileData().then((msg) => {
        this.orderData = msg;
      })
      .catch((e) => {
        this.$message.error(e.message);
      });
    }
  };
</script>

<style scoped>
  .workplace-user-card .ele-cell-content {
    overflow: hidden;
  }

  .workplace-count-group {
    white-space: nowrap;
  }

  .workplace-count-item {
    padding: 0 5px 0 25px;
    box-sizing: border-box;
    display: inline-block;
    text-align: right;
  }

  .workplace-count-name {
    padding-left: 8px;
  }

  .workplace-count-num {
    font-size: 24px;
    margin-top: 6px;
  }

  @media screen and (max-width: 992px) {
    .workplace-count-item {
      padding: 0 5px 0 10px;
    }
  }

  @media screen and (max-width: 768px) {
    .workplace-user-card,
    .workplace-count-group {
      display: block;
    }

    .workplace-count-group {
      margin-top: 15px;
      text-align: right;
    }
  }
</style>
