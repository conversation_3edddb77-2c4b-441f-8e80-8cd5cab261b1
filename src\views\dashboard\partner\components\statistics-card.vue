<!-- 统计卡片 -->
<template>
  <el-row :gutter="15">
    <el-col :md="6" :sm="12">
      <el-card shadow="never" class="monitor-count-card">
        <el-tag size="large" class="ele-tag-round">
          <i class="el-icon-s-custom"></i>
        </el-tag>
        <div class="monitor-count-card-num ele-text-heading">{{ orderData.collector_count }}</div>
        <div class="monitor-count-card-text ele-text-secondary">
          回收员数
        </div>
        <div class="monitor-count-card-trend ele-text-success">
          
          <span>名下回收员数量</span>
        </div>
      </el-card>
    </el-col>
    <el-col :md="6" :sm="12">
      <el-card shadow="never" class="monitor-count-card">
        <el-tag size="large" type="warning" class="ele-tag-round">
          <i class="el-icon-s-flag"></i>
        </el-tag>
        <div class="monitor-count-card-num ele-text-heading"> {{ orderData.station_count }}</div>
        <div class="monitor-count-card-text ele-text-secondary">
          回收站数
        </div>
        <div class="monitor-count-card-trend ele-text-success">
          
          <span>名下回收站数量</span>
        </div>
      </el-card>
    </el-col>
    <el-col :md="6" :sm="12">
      <el-card shadow="never" class="monitor-count-card">
        <el-tag size="large" type="danger" class="ele-tag-round">
          <i class="el-icon-s-order"></i>
        </el-tag>
        <div class="monitor-count-card-num ele-text-heading"> {{ orderData.reserve_count }}</div>
        <div class="monitor-count-card-text ele-text-secondary">
          回收订单数
        </div>
        <div class="monitor-count-card-trend ele-text-success">
          
          <span>名下回收订单数量</span>
        </div>
      </el-card>
    </el-col>
    <el-col :md="6" :sm="12">
      <el-card shadow="never" class="monitor-count-card">
        <el-tag size="large" type="success" class="ele-tag-round">
          <i class="el-icon-_money-solid"></i>
        </el-tag>
        <div class="monitor-count-card-num ele-text-heading"> {{ orderData.balance }}</div>
        <div class="monitor-count-card-text ele-text-secondary">
          余额
        </div>
        <div class="monitor-count-card-trend ele-text-success">
          
          <span>当前余额</span>
        </div>
      </el-card>
    </el-col>
    
    
  </el-row>
</template>

<script>
import { getPartnerSaleData  } from '@/api/user/info';
  export default {
    name: 'StatisticsCard',
    data() {
      return {
         // 表单数据
         orderData: {
          collector_count:0,
          station_count:0,
          reserve_count:0,
          balance:0,
        },
      };
    },
    mounted() {
      getPartnerSaleData().then((data) => {
        this.orderData = data;
      })
      .catch((e) => {
        this.$message.error(e.message);
      });
     }
  };
</script>

<style scoped>
  .monitor-count-card ::v-deep .el-card__body {
    padding-top: 18px;
    text-align: center;
    position: relative;
  }

  .monitor-count-card ::v-deep .el-tag {
    border-color: transparent;
    font-size: 15px;
  }

  .monitor-count-card .monitor-count-card-num {
    font-weight: 500;
    font-size: 32px;
    margin-top: 12px;
  }

  .monitor-count-card .monitor-count-card-text {
    font-size: 12px;
    margin: 10px 0;
  }

  .monitor-count-card .monitor-count-card-trend {
    font-weight: 600;
    padding: 6px 0;
  }

  .monitor-count-card .monitor-count-card-trend > i {
    font-size: 12px;
    font-weight: 600;
    margin-right: 5px;
  }

  .monitor-count-card .monitor-count-card-tips {
    position: absolute;
    top: 15px;
    right: 15px;
    cursor: pointer;
  }
</style>
