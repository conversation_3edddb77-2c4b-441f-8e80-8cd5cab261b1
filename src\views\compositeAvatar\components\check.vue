<!-- 机构编辑弹窗 -->
<template>
  <ele-modal
    width="680px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="'审核克隆'"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form"  label-width="100px">
      <el-row :gutter="15">
        <el-col :sm="12">
         
          <el-form-item label="操作:">
            <el-radio-group v-model="form.is_status">
              <el-radio :label="2" :value="2">审核通过</el-radio>
              <el-radio :label="3" :value="3">审核拒绝</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="form.is_status=='3'" label="拒绝原因:" prop="refuse">
            <el-input v-model="form.refuse" label="拒绝原因"></el-input>
          </el-form-item>
         
          
        </el-col>
      </el-row>
    </el-form>
    <template v-slot:footer>
      <el-button @click="updateVisible(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
    
  </ele-modal>
 
</template>

<script>
  import { check } from '@/api/compositeAvatar';
  const DEFAULT_FORM = {
    id:0,
    is_status:2,
    refuse:''
  };

  export default {
    name: 'Check',
    components: { },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data:[]
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
  
          const data = {
            ...this.form
          };
          
          check(data).then((msg) => {
            this.loading = false;
            this.$message.success(msg);
            this.updateVisible(false);
            this.$emit('done');
          }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
          });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      }
    },
    watch: {
      visible(visible) {
        if (visible) {
          if (this.data) {
            this.form = { ...DEFAULT_FORM };
            this.$util.assignObject(this.form, this.data);
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
         
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
