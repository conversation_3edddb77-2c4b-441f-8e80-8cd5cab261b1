<!-- 搜索表单 -->
<template>
  <el-form
    label-width="77px"
    class="ele-form-search"
    @keyup.enter.native="search"
    @submit.native.prevent
  >
    <el-row :gutter="15">
      <el-col :lg="4" :md="12">
        <el-form-item label="订单编号:">
          <el-input clearable v-model="where.log_no" placeholder="请输入订单编号" />
        </el-form-item>
      </el-col>

      <el-col :lg="4" :md="12">
        <el-form-item label="方式:">
          <el-select
            v-model="where.way"
            placeholder="请选择"
            clearable
            class="ele-fluid"
          >
            <el-option label="后台操作" value="0" />
            <el-option label="点数充值" value="1" />
            <el-option label="新人赠送" value="2" />
            <el-option label="声音克隆" value="3" />
            <el-option label="形象克隆" value="4" />
            <el-option label="视频换脸" value="5" />
            <el-option label="视频高级创作" value="6" />
            <el-option label="视频线路三创作" value="10" />
            <el-option label="会员操作" value="7" />
            <el-option label="AI文案" value="8" />
            <el-option label="照片克隆" value="9" />

          </el-select>
        </el-form-item>
      </el-col>

      <el-col :lg="4" :md="12">
        <el-form-item label="描述:">
          <el-input clearable v-model="where.desc" />
        </el-form-item>
      </el-col>

      <el-col :lg="4" :md="12">
        <el-form-item label="操作:">
          <el-select
            v-model="where.type"
            placeholder="请选择"
            clearable
            class="ele-fluid"
          >
            <el-option label="充值" value="1" />
            <el-option label="扣除" value="2" />
          </el-select>
        </el-form-item>
      </el-col>

    

      
      <el-col :lg="4" :md="12">
        <el-form-item label="卡密:">
          <el-input clearable v-model="where.card" />
        </el-form-item>
      </el-col>

     

      <el-col :lg="4" :md="12">
        <div class="ele-form-actions">
          <el-button
            type="primary"
            icon="el-icon-search"
            class="ele-btn-icon"
            @click="search"
          >
            查询
          </el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
  const DEFAULT_WHERE = {
    log_no: '',
    type: '',
    desc:'',
    way:'',
    card:''
  };

  export default {
    name: 'UserPointLogSearch',
    data() {
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE }
      };
    },
    methods: {
      /* 搜索 */
      search() {
        this.$emit('search', this.where);
      },
      /*  重置 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.search();
      }
    }
  };
</script>
