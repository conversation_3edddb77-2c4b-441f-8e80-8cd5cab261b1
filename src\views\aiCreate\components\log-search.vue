<!-- 搜索表单 -->
<template>
  <el-form
    label-width="77px"
    class="ele-form-search"
    @keyup.enter.native="search"
    @submit.native.prevent
  >
    <el-row :gutter="15">
      
      <el-col :lg="4" :md="12">
        <el-form-item label="用户:">
          <el-input clearable v-model="where.nickname" />
        </el-form-item>
      </el-col>

      <el-col :lg="4" :md="12">
        <el-form-item label="名称:">
          <el-input clearable v-model="where.name" />
        </el-form-item>
      </el-col>

      <el-col :lg="4" :md="12">
        <el-form-item label="类型:">
          <el-select
            clearable
            v-model="where.type"
            placeholder="请选择"
            class="ele-fluid">
            <el-option label="生产工厂" :value="1" />
            <el-option label="团购商家" :value="2" />
            <el-option label="零售商家" :value="3" />
            <el-option label="智能代写" :value="4" />
            <el-option label="文案仿写" :value="5" />

              <el-option label="同城团购" :value="6" />
            <el-option label="同城团购" :value="7" />
            <el-option label="电商带货" :value="8" />
            <el-option label="知识科普" :value="9" />
            <el-option label="情感专家" :value="10" />
              <el-option label="口播文案" :value="11" />
            <el-option label="朋友圈营销" :value="12" />
            <el-option label="小红书笔记" :value="13" />
            <el-option label="智能代写" :value="14" />
            <el-option label="文案改写" :value="15" />
              <el-option label="视频提取" :value="16" />
                    <el-option label="本地视频提取" :value="20" />
           

       

          </el-select>
        </el-form-item>
      </el-col> 

      <el-col :lg="4" :md="12">
        <div class="ele-form-actions">
          <el-button
            type="primary"
            icon="el-icon-search"
            class="ele-btn-icon"
            @click="search"
          >
            查询
          </el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>

  const DEFAULT_WHERE = {
    nickname: '',
    name:'',
    type:'',
  };

  export default {
    name: 'LogSearch',
    data() {
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE }
      };
    },
    methods: {
      /* 搜索 */
      search() {
        this.$emit('search', this.where);
      },
      /*  重置 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.search();
      }
    }
  };
</script>
