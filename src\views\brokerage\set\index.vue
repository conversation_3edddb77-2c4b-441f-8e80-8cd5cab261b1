<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">分销设置</div>
    </div>
    <div class="ele-body" style="padding-bottom: 71px;background: var(--color-white);">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="130px"
        @keyup.enter.native="submit"
        @submit.native.prevent
      >
        <el-card shadow="never" body-style="padding: 22px 22px 0 22px;">
          <el-row :gutter="15">
            <el-col :sm="8">


                <el-form-item label="分销等级升级模式:" prop="distribution_mode" v-if="distribution_mode == 1 || distribution_mode== 2 " > <span style="color:red">注:确认后无法修改</span>
               
                <el-radio-group v-model="distribution_mode">
             
                  <el-radio :label="1"  disabled value="1" >下级消费</el-radio>
                  <el-radio :label="2" disabled value="2" >下级购买套餐数量</el-radio>
                </el-radio-group>

              </el-form-item> 
              
                <el-form-item label="分销等级升级模式:" prop="distribution_mode" v-if="!form.distribution_mode ||!distribution_mode"  > <span style="color:red">注:确认后无法修改</span>
                <el-radio-group v-model="form.distribution_mode">
             
                  <el-radio :label="1" value="1" >下级消费</el-radio>
                  <el-radio :label="2" value="2" >下级购买套餐数量</el-radio>
                </el-radio-group>
                
              </el-form-item>


              <el-form-item label="是否开启:" prop="is_open">
                <el-radio-group v-model="form.is_open">
                  <el-radio :label="1" value="1" >开启</el-radio>
                  <el-radio :label="2" value="2" >隐藏</el-radio>
                </el-radio-group>
              </el-form-item>


            
              <el-form-item label="分销等级:" prop="is_level" >
                <el-radio-group v-model="form.is_level">
                  <el-radio :label="0" value="0" >无</el-radio>
                  <el-radio :label="1" value="1" >一级分销</el-radio>
                  <el-radio :label="2" value="2" >二级分销</el-radio>
                </el-radio-group>
              </el-form-item>

                
       <el-form-item label="是否开启股东分红:" prop="is_shareholder_swich">
                <el-radio-group v-model="form.is_shareholder_swich">
                  <el-radio :label="1" value="1" >开启</el-radio>
                  <el-radio :label="2" value="2" >关闭</el-radio>
                </el-radio-group>
              </el-form-item>
              
             <el-form-item  label="股东分红比例:" prop="shareholder_radio" >
                <el-input style="width:250px" v-model="form.shareholder_radio" placeholder="股东分红比例" clearable >
                  <template slot="append">%</template>
                </el-input>
    
              </el-form-item>

              <el-form-item label="成为分销商是否需要自购:" prop="self_purchase_swich" >
                <el-radio-group v-model="form.self_purchase_swich">
                  
                  <el-radio :label="1" value="1" >是</el-radio>
                  <el-radio :label="0" value="0" >否</el-radio>
                </el-radio-group>
              </el-form-item>
       <el-form-item  label="选择成为分销商自购套餐:" v-if="form.self_purchase_swich == 1"  prop="shareholder_radio" >
                  <el-select
                  v-model="form.self_purchase_member_id"
                  placeholder="请选择"
                  @change="changeValue"
                  value-key="label"
                >
                  <el-option
                    v-for="item in packageList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>

               </el-form-item>
              
            </el-col>
            <el-col :sm="8">
              <el-form-item label="提现是否开启:" prop="cash_open">
                <el-radio-group v-model="form.cash_open">
                  <el-radio :label="1" value="1" >开启</el-radio>
                  <el-radio :label="2" value="2" >隐藏</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="提现方式:" label-width="130px" prop="cash_type" >
                <el-checkbox-group v-model="form.cash_type">
                  <el-checkbox label="1" value="1">线下提现</el-checkbox>
                  <el-checkbox label="2" value="2">线上提现到微信</el-checkbox>
                  <!-- <el-checkbox label="3" value="3">提现到余额</el-checkbox> -->
                </el-checkbox-group>
              </el-form-item> 

              <el-form-item label="最低提现:" label-width="130px" prop="cash_limit" >
                <el-input v-model="form.cash_limit" style="width: 250px;" placeholder="最低提现" clearable >
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>

              <el-form-item label="最高提现:" label-width="130px" prop="cash_highest" >
                <el-input v-model="form.cash_highest" style="width: 250px;" placeholder="最高提现" clearable >
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>


              <!-- <el-form-item label="转换比例:" label-width="130px" prop="cash_charge" >
                <el-input v-model="form.cash_charge" style="width: 250px;" placeholder="转换比例" clearable >
                  <template slot="append"> 1元 = {{form.cash_charge}} 点</template>
                </el-input>
              </el-form-item> -->

              <el-form-item label="提现手续费:" label-width="130px" prop="service_charge" >
                <el-input v-model="form.service_charge" style="width: 250px;" placeholder="提现手续费" clearable >
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
                 <el-form-item v-if="form.is_level" label="资产市场一级分销比例:" prop="oneLevel" >
                <el-input style="width:250px" v-model="form.oneLevel" placeholder="一级分销比例" clearable >
                  <template slot="append">%</template>
                </el-input>
                <div class="text-info">支付完成时计算，分销佣金  =  用户总价  *   分销比例</div>
              </el-form-item>
            
              <el-form-item v-if="form.is_level == 2" label="资产市场二级分销比例:" prop="twoLevel" >
                <el-input style="width:250px" v-model="form.twoLevel" placeholder="二级分销比例" clearable >
                  <template slot="append">%</template>
                </el-input>
                <div class="text-info">支付完成时计算，分销佣金  =  用户总价  *   分销比例</div>
              </el-form-item> 

            </el-col>
            
          
            
            <el-col :sm="8">
              <el-form-item   label="邀请好友得:" prop="invite" >
                <el-input style="width:250px" v-model="form.invite" placeholder="邀请好友得" clearable >
                </el-input>
                <div class="text-info">邀请好友首次进入可得佣金</div>
              </el-form-item>
              <el-form-item label="分销海报:" prop="poster">
                <span slot="label">
                  分销海报
                  <el-tooltip placement="top">
                    <div slot="content">
                      建议上传630px * 952px尺寸,或者等比例图片
                    </div>
                    <i class="el-icon-question" />
                  </el-tooltip>
                </span> 
                <!--<ele-image-upload v-model="img" :limit="1" :drag="true" :multiple="false"  @upload="onUpload" /> --> 
                  <div class="ele-image-upload-list">
                    <div class="ele-image-upload-item ele-image-upload-button" @click="modalPicTap('dan','poster','分销海报')">
                      <div>
                        <div tabindex="0" class="el-upload el-upload--text">
                          <div class="el-upload-dragger">
                            <i class="el-icon-plus ele-image-upload-icon"></i>
                          </div>
                          <div class="ele-image-                                    upload-item" style="margin:0 0 0 0;" v-if="form.poster !=''">
                            <div class="el-image" >
                              <img :src="form.poster" width="100%" height="100%"  class="el-image__inner" style="object-fit: cover;">
                            </div>
                            <div class="ele-image-upload-close" @click="handleRemove('poster')"><i class="el-icon-close"></i></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
              </el-form-item>
               <!-- <div style="margin-top:50px"></div>
                  <el-form-item label="分销横幅图:" prop="banner">
                <span slot="label">
                  分销横幅图
                  <el-tooltip placement="top">
                    <div slot="content">
                          建议上传710 * 230px尺寸,或者等比例图片
                    </div>
                    <i class="el-icon-question" />
                  </el-tooltip>
                </span> 
        
                  <div class="ele-image-upload-list">
                    <div class="ele-image-upload-item ele-image-upload-button" @click="modalPicTap('dan','banner','分销横幅图')">
                      <div>
                        <div tabindex="0" class="el-upload el-upload--text">
                          <div class="el-upload-dragger">
                            <i class="el-icon-plus ele-image-upload-icon"></i>
                          </div>
                          <div class="ele-image-                                    upload-item" style="margin:0 0 0 0;" v-if="form.banner !=''">
                            <div class="el-image" >
                              <img :src="form.banner" width="100%" height="100%"  class="el-image__inner" style="object-fit: cover;">
                            </div>
                            <div class="ele-image-upload-close" @click="handleRemove('banner')"><i class="el-icon-close"></i></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
              </el-form-item> -->
            </el-col>


              

              
             
          </el-row>

          <el-form-item label="分销说明:" >
                <tinymce-editor v-model="form.explain" :init="editorConfig" />
              </el-form-item>
          
          <el-form-item>
            <el-button type="primary" :loading="loading" @click="submit">
              提交
            </el-button>
          </el-form-item>
        </el-card>
        
       <uploadPictures
          :isChoice="isChoice"
          :visible.sync="modalPic"
           @getPic="getPic"
          :gridBtn="gridBtn"
          :gridPic="gridPic"
          :title="modalTitle"
        ></uploadPictures>

       
      </el-form>
    </div>
  </div>
  
</template>

<script>
  import uploadPictures from "@/components/uploadPictures";
  import EleImageUpload from 'ele-admin/es/ele-image-upload';
  import TinymceEditor from '@/components/TinymceEditor';
  import request from '@/utils/request';
  import { queryBrokerageSet ,saveBrokerageSet } from '@/api/brokerage';

    import { list  } from '@/api/member/list';
  const DEFAULT_FORM = {
    is_open:2,
    is_level: 0,
    oneLevel: 0,
    twoLevel: 0,
    invite:0,
    poster: '',
    explain: '',
    cash_open:2,
    cash_type:['1'],
    cash_limit:'',
    cash_highest:'',
    service_charge: '',
    cash_charge: '',
    distribution_mode :'',
    banner:'',
    shareholder_radio:'',
    self_purchase_swich:'',
    self_purchase_member_id:''
  };

  export default {
    name: 'FormAdvanced',
    components: { EleImageUpload,uploadPictures,TinymceEditor },
    computed: {
      brokerageName() {
        return this.$store.state.user.brokerage_name;
      },
    },
    data() {
      return {
        packageList:[],
        distribution_mode:'',
        modalTitle:'',
        modalPic: false,
        isChoice: "单选",
        gridBtn: {
          xl: 4,
          lg: 8,
          md: 8,
          sm: 8,
          xs: 8,
        },
        gridPic: {
          xl: 6,
          lg: 8,
          md: 12,
          sm: 12,
          xs: 12,
        },
        // 表单提交状态
        loading: false,
        // 表单数据
        form: {
          ...DEFAULT_FORM
        },
        // 表单验证规则
        rules: {
          name: [
            {
              required: true,
              message: '请输入仓库名',
              trigger: 'blur'
            }
          ]
        },
        // 表单验证信息
        validMsg: '',
        editorConfig:{
          height: 525,
          relative_urls : false,
          convert_urls: false,
          images_upload_handler: (blobInfo, success, error) => {
            const file = blobInfo.blob();
            // 使用 axios 上传，实际开发这段建议写在 api 中再调用 api
            const formData = new FormData();
            formData.append('file', file, file.name);
            request({
              url: '/common/uploadFile',
              method: 'post',
              data: formData,
              onUploadProgress: (e) => {  // 文件上传进度回调
                     
              }
            }).then((res) => {
                if(res.data.code === 0) {
                    success(res.data.data.url);
                }else{
                  error(res.data.message);
                }
            }).catch((e) => {
                error('exception');
            });
          }
        },
      };
    },
    mounted() {
      this.queryBrokerageSetFn()
      this.levelListFn()
    },
    methods: {
      changeValue(e) {
      // this.form.package_id = e;
      console.log(e);
      this.form.self_purchase_member_id = ex;
     
    },
      levelListFn() {
      list({ page: 1, limit: 10 })
        .then((data) => {
          if (data != null) {
            this.packageList = data.list.map((item) => {
              return {
                value:  item.id,
                // value: item.id,
                label: item.name
              };
            });
            console.log(this.packageList);
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },


      queryBrokerageSetFn(){
         queryBrokerageSet().then((data) => {
        if(data != null){
          this.form = data;
          this.distribution_mode=data.distribution_mode
        }
      }).catch((e) => {
        this.$message.error(e.message);
      });
      },

      // 选择图片
      modalPicTap(tit, picTit,openTitle) {
        this.modalTitle = openTitle;
        this.isChoice = tit === "dan" ? "单选" : "多选";
        this.picTit = picTit;
        this.modalPic = true;
      },
      // 选中图片
      getPic(pc) {
        switch (this.picTit) {
          case "poster":
            this.form.poster = pc.satt_dir;
            break;
               case "banner":
            this.form.banner = pc.satt_dir;
            break;
        }
        this.modalPic = false;
      },
      //删除图片
      handleRemove(type) {
         if(type =='poster' ){
           this.form.poster = '';
         }
         if(type =='banner' ){
           this.form.banner = '';
         }
       
      },
      /* 表单提交 */
      submit() {
        this.$refs['form'].validate((valid, obj) => {
          if (valid) {
            this.validMsg = '';
            this.loading = true;
            const data = {
            ...this.form
            };
            saveBrokerageSet(data).then((msg) => {
              this.loading = false;
              this.$message.success(msg);
               this.queryBrokerageSetFn()
              
            }).catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });
          } else {
            this.validMsg = ` 共有校验 ${Object.keys(obj).length} 项不通过`;
            return false;
          }
        });
      },
      onUpload(item){
         console.log('item:', item);
          item.status = 'uploading';
          const formData = new FormData();
          formData.append('file', item.file);
          request({
              url: '/common/uploadFile',
              method: 'post',
              data: formData,
              onUploadProgress: (e) => {  // 文件上传进度回调
                  if (e.lengthComputable) {
                      item.progress = e.loaded / e.total * 100;
                  }
              }
          }).then((res) => {
              if(res.data.code === 0) {
                  item.status = 'done';
                  item.url = res.data.data.url;
                  // 如果你上传的不是图片格式, 建议将 url 字段作为缩略图, 再添加其它字段作为最后提交数据
                  //item.url = res.data.data.thumbnail;  // 也可以不赋值 url 字段, 默认会显示为一个文件图标
                  item.fileUrl = res.data.data.url;
              }
          }).catch((e) => {
              item.status = 'exception';
          });
      }
    }
  };
</script>
