import axios from '@/utils/request';

/**
 * 一级分类
 * @param params 查询条件
 */
export async function list(params) {
 
  const res = await axios.get('/figure.partner/index', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 审核
 * @param params nid oid 两个排序的id
 */
 export async function check(data) {
  const res = await axios.post('/figure.partner/check', data);
  if (res.data.code === 0) {
      return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 修改
 * @param data 机构信息
 */
export async function update(data) {
  const res = await axios.post('/figure.partner/edit', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 排序
 * @param params nid oid 两个排序的id
 */
 export async function sortChange(params) {
  const res = await axios.post('/figure.partner/sortChange', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 排序
 * @param params nid oid 两个排序的id
 */
 export async function modify(data) {
  const res = await axios.post('/figure.partner/modify', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除
 * @param id id
 */
export async function remove(id) {
  const res = await axios.get('/figure.partner/delete?id=' + id);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 一键默认
 * @param id id
 */
 export async function doDefault(id) {
  const res = await axios.get('/figure.partner/do_default');
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}




