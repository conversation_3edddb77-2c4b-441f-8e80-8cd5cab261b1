<template>
  <div class="ele-body ele-body-card" style="background-image: url('/static/images/datamonitor-bg.png');background-size: cover;overflow: hidden;">
    <el-row :gutter="15">
      <el-col :lg="7">
        <dv-border-box-1 style="width:100%;height: 550px;overflow: auto;">
          <dv-decoration-7 style="width: 100%;height: 20px;margin-bottom:20px;color: white;"><em></em>回收品类排行<em></em></dv-decoration-7>
          <dv-capsule-chart :option="option" :config="config" style="width:100%;height: 500px;" v-if="config.data.length>0" />
        </dv-border-box-1>
        
        
        
        <dv-border-box-8 style="width:100%;height: 520px;margin-top: 16px;">
          <dv-decoration-7 style="width: 100%;height: 20px;margin-bottom:20px;color: white;"><em></em>商城订单交易<em></em></dv-decoration-7>
          <v-chart
            ref="orderDayChart"
            style="height: 440px"
            :option="orderDayChartOption"
          />
        </dv-border-box-8>

      </el-col>
      <el-col :lg="10" :md="12">

        <dv-border-box-11 title="业绩总览" style="width:100%;height: 240px;color: #ffffff;font-size: 20px;">
          
      
         
          <div class="text-conme">
              <div>
                 <dv-decoration-9 style="width:100px;height:100px;">{{ statisData.partnerCount }} </dv-decoration-9>
              </div>
         
              <div>
                <div style="font-size:0.8em;margin-top: 10px;">合伙人数量</div>
              </div>
          </div>

          <div class="text-conme">
              <div>
                <dv-decoration-9 style="width:100px;height:100px;">{{ statisData.collectorCount }}</dv-decoration-9>
              </div>
         
              <div>
                <div style="font-size:0.8em;margin-top: 10px;">回收员数量</div>
              </div>
          </div>

          <div class="text-conme">
              <div>
                <dv-decoration-9 style="width:100px;height:100px;">{{ statisData.stationCount }}</dv-decoration-9>
              </div>
         
              <div>
                <div style="font-size:0.8em;margin-top: 10px;">回收站数量</div>
              </div>
          </div>

          <div class="text-conme">
              <div>
                <dv-decoration-9 style="width:100px;height:100px;">{{ statisData.packCount }}</dv-decoration-9>
              </div>
         
              <div>
                <div style="font-size:0.8em;margin-top: 10px;">打包站数量</div>
              </div>
          </div>

          
        </dv-border-box-11>

        <dv-border-box-10 style="width:100%;height: 360px;margin-top: 16px; ">
          <dv-decoration-7 style="width: 100%;height: 20px;margin-bottom:20px;color: white;"><em></em>近七天回收订单<em></em></dv-decoration-7>
          <v-chart
            ref="reserveOrderDayChartOption"
            style="height: 300px"
            :option="reserveOrderDayChartOption"
          />
        </dv-border-box-10>

        <dv-border-box-12 style="width:100%;height: 455px;margin-top: 16px;">
          <dv-decoration-7 style="width: 100%;height: 20px;margin-bottom:20px;color: white;"><em></em>回收站出库入库记录<em></em></dv-decoration-7>
          <v-chart
            ref="stockChart"
            style="height: 395px"
            :option="stockChartOption"
          />
        </dv-border-box-12>
      </el-col>
      <el-col :lg="7" :md="12">
        <dv-border-box-8 style="width:100%;height: 420px;margin-top: 16px;">
          <dv-decoration-7 style="width: 100%;height: 20px;margin-bottom:20px;color: white;"><em></em>近一年回收订单<em></em></dv-decoration-7>
          <v-chart
            ref="reserveOrderMonthChartOption"
            style="height: 380px"
            :option="reserveOrderMonthChartOption"
          />
        </dv-border-box-8>

        <dv-border-box-11 style="width:100%;height: 320px;" title="回收员订单数据">

          <dv-scroll-board :config="boardConfig" ref="scrollBoard" style="width:90%;height:160px;padding-top: 55px;" v-if="boardConfig.data.length>0" />
        </dv-border-box-11>
        
        <dv-border-box-1 style="width:100%;height: 335px;">
          <dv-decoration-7 style="width: 100%;height: 20px;margin-bottom:20px;color: white;"><em></em>{{balanceName}}统计<em></em></dv-decoration-7>
          <div style="display: flex;padding:0 15px">
            <dv-active-ring-chart :config="activeRignConfig1" style="width:100%;height:114px;margin-top: 0.8em;"  v-if="activeRignConfig1.data.length>0" />
            <dv-active-ring-chart :config="activeRignConfig2" style="width:100%;height:114px;margin-top: 0.8em;"  v-if="activeRignConfig2.data.length>0" />
          </div>
          <div style="display: flex;padding:0 15px">
            <dv-active-ring-chart :config="activeRignConfig3" style="width:100%;height:114px;margin-top: 0.8em;"  v-if="activeRignConfig3.data.length>0" />
            <dv-active-ring-chart :config="activeRignConfig4" style="width:100%;height:114px;margin-top: 0.8em;"  v-if="activeRignConfig4.data.length>0" />
          </div>
          
    
        </dv-border-box-1>

       

      </el-col>
    </el-row>
  </div>
</template>

<script>  
  import { 
    getReserveCategoryRanking,
    getStatisInfo,
    getReserveOrderByDay,
    getReserveOrderByMonth,
    getCollectorOrderRanking,
    getOrderByDay,
    getStockLog,
    getBalanceData,
  } from '@/api/datamonitor';
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { LineChart,BarChart,PieChart  } from 'echarts/charts';
  import {
    GridComponent,
    TooltipComponent,
    LegendComponent
  } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { echartsMixin } from '@/utils/echarts-mixin';

  use([
    CanvasRenderer,
    LineChart,
    BarChart,
    PieChart,
    GridComponent,
    TooltipComponent,
    LegendComponent
  ]);
  
  export default {
    name: 'UserRate',
    components: { VChart },
    computed: {
      darkMode() {
        return this.$store?.state?.theme?.darkMode;
      },
      balanceName() {
        return this.$store.state.user.balance_name;
      },
      configs() {
        return this.config;
      },
    },
    mixins: [echartsMixin(['reserveOrderDayChart','reserveOrderMonthChart','orderDayChart'])],
    data() {
      return {
        
        activeRignConfig1:{
          radius: '90%',
          activeRadius: '90%',
          data: [],
          lineWidth:10,
          digitalFlopStyle: {
            fontSize: 20
          },
          digitalFlopToFixed:2,
          showOriginValue: true
        },
        activeRignConfig2:{
          radius: '90%',
          activeRadius: '90%',
          data: [],
          lineWidth:10,
          digitalFlopStyle: {
            fontSize: 20
          },
          digitalFlopToFixed:2,
          showOriginValue: true
        },
        activeRignConfig3:{
          radius: '90%',
          activeRadius: '90%',
          data: [],
          lineWidth:10,
          digitalFlopStyle: {
            fontSize: 20
          },
          digitalFlopToFixed:2,
          showOriginValue: true
        },
        activeRignConfig4:{
          radius: '90%',
          activeRadius: '90%',
          data: [],
          lineWidth:10,
          digitalFlopStyle: {
            fontSize: 20
          },
          digitalFlopToFixed:2,
          showOriginValue: true
        },
        boardConfig:{
          header: ['回收员名称', '接单数量', '转单数量','完成数量'],
          data: [
           
          ],
          index: true,
          columnWidth: [80],
          align: ['center'],
          carousel: 'page',
          waitTime:2500
        },
        option:{
          series: [
            {
              axisLabel: {
                fontSize: 14,
                color: '#FFFFFF',
              },
            }
          ],
            
 
          },
        config:{
          data: [
          // {value: 16, name: "纸品-黄纸"},
          // {value: 2, name: "纸品-纸箱"},
          // {value: 0, name: "家电-冰箱"},
          // {value: 0, name: "家电-手机"},
          // {value: 0, name: "金属-铂金"},
          // {value: 0, name: "金属-青铜"},
          // {value: 0, name: "金属-黑铁"},
          ],
          colors: ['#e062ae', '#fb7293', '#e690d1', '#32c5e9', '#96bfff'],
          showValue: true,
          option:{
            yAxis: [
            {
              axisLabel: {
                textStyle: {
                  fontSize: 14,
                  color: '#FFFFFF',
                },
              }
            }
          ],
          },
          legend: {
            
            textStyle: {
              fontSize: 14,
              color: '#FFFFFF',
            },
          },
          xAxis: [
            {
              
              axisLabel: {
                textStyle: {
                  fontSize: 14,
                  color: '#FFFFFF',
                },
              }
            }
          ],
          yAxis: [
            {
              axisLabel: {
                textStyle: {
                  fontSize: 14,
                  color: '#FFFFFF',
                },
              }
            }
          ],
          title:{
            text:'ddd',
            style: {
              fill: '#333',
              fontSize: 17,
              fontWeight: 'bold',
              textAlign: 'center',
              textBaseline: 'bottom'
            }
          }
        },
        categoryData:{},
        statisData:[],
        reserveOrderDayChartOption: {},
        reserveOrderMonthChartOption: {},
        collectorOrderList:[],
        orderDayChartOption: {},
        stockChartOption: {},
        balanceChartOption: {},
        isDatamonitor:sessionStorage.getItem("IS_DATAMONITOR"),
        

      };
    },
    mounted() {

    },
    created() {
      this.getReserveCategoryData();
      this.getStatisData();
      this.getReserveOrderDayData();
      this.getReserveOrderMonthData();
      this.queryCollectorOrderList();
      this.getOrderDayData();
      this.getStockData();
      this.getPieData();
    },
    beforeDestroy(){
      sessionStorage.setItem("IS_DATAMONITOR", false);
    },
    methods: {
      getReserveCategoryData(){
        getReserveCategoryRanking().then((data) => {
          if(data != null){
            this.$set(this.config,'data',data)
            // this.config.data = data;
          }
          console.log(this.config,'111111111111111111',this.config.data);
        }).catch((e) => {
          this.$message.error(e.message);
        })
      },
      

      /* 获取平台统计数据 */
      getStatisData(){
        getStatisInfo().then((data) => {
          if(data != null){
            this.statisData = data;
          }
        }).catch((e) => {
          this.$message.error(e.message);
        })
      },

      
      /* 获取最近7天回收订单 */
      getReserveOrderDayData() {
        getReserveOrderByDay().then((data) => {
          this.reserveOrderDayChartOption = {
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: ['订单数量', '订单金额'],
              right: 20,
              textStyle: {
                fontSize: 14,
                color: '#FFFFFF',
              },
            },
            grid: {
              left: "5%",
              bottom: "8%",
              containLabel: true,
            },
            xAxis: [
              {
                type: 'category',
                boundaryGap: true,
                data: data.date,
                axisLabel: {
                  interval: 0,
      			      rotate: 40,
                  textStyle: {
                    fontSize: 14,
                    color: '#FFFFFF',
                  },
                }
              }
            ],
            yAxis: [
              {
                type: 'value',
                axisLabel: {
                  textStyle: {
                    interval: 0,
      			        rotate: 40,
                    fontSize: 14,
                    color: '#FFFFFF',
                  },
                }
              }
            ],
            series: [
              {
                name: '订单数量',
                type: 'bar',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                  opacity: 0.5
                },
                data: data.count
              },
              {
                name: '订单金额',
                type: 'line',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                  opacity: 0.5
                },
                data: data.value
              }
            ]
          };
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
      },
      /* 获取近1年回收订单 */
      getReserveOrderMonthData() {
        getReserveOrderByMonth().then((data) => {
          this.reserveOrderMonthChartOption = {
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: ['订单数量', '订单金额'],
              right: 20,
              textStyle: {
                fontSize: 14,
                color: '#FFFFFF',
              },
            },
            grid: {
              left: "5%",
              bottom: "8%",
              containLabel: true,
            },
            xAxis: [
              {
                type: 'category',
                boundaryGap: true,
                data: data.date,
                axisLabel: {
                  interval: 0,
      			      rotate: 40,
                  textStyle: {
                    fontSize: 14,
                    color: '#FFFFFF',
                  },
                }
              }
            ],
            yAxis: [
              {
                type: 'value',
                axisLabel: {
                  textStyle: {
                    fontSize: 14,
                    color: '#FFFFFF',
                  },
                }
              }
            ],
            series: [
              {
                name: '订单数量',
                type: 'bar',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                  opacity: 0.5
                },
                data: data.count
              },
              {
                name: '订单金额',
                type: 'line',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                  opacity: 0.5
                },
                data: data.value
              }
            ]
          };
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
      },
      /* 获取回收员订单数据 */
      queryCollectorOrderList() {
        getCollectorOrderRanking().then((data) => {
          this.collectorOrderList = data;

          let boardData = [];
          data.forEach(element => {
            const item = [];
            item.push(element.name);
            item.push(element.receive_count);
            item.push(element.transfer_count);
            item.push(element.finish_count);
            boardData.push(item);
          });


          this.$set(this.boardConfig,'data',boardData);

          console.log(this.boardConfig,'----------------------',this.boardConfig.data);
        }).catch((e) => {
          this.$message.error(e.message);
        });
      },

      doUpdate () {
        this.$refs['scrollBoard'].updateRows(rows, index)
      },
      /* 获取商城订单交易数据 */
      getOrderDayData() {
        getOrderByDay().then((data) => {
          this.orderDayChartOption = {
            color: ['#93CE07', '#FBDB0F', '#FC7D02', '#AA069F'],
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              show: true,
              type: 'scroll',
              orient: 'horizontal', // vertical
              data: ['兑换订单数量', '兑换订单金额', '积分订单数量', '积分订单金额'],
              right: 20,
              textStyle: {
                fontSize: 14,
                lineHeight: 16, 
                color: '#FFFFFF',
              },
            },
            grid: {
              left: "5%",
              bottom: "8%",
              containLabel: true,
            },
            xAxis: [
              {
                type: 'category',
                boundaryGap: true,
                data: data.date,
                axisLabel: {
                  interval: 0,
      			      rotate: 40,
                  textStyle: {
                    fontSize: 14,
                    color: '#FFFFFF',
                  },
                }
              }
            ],
            yAxis: [
              {
                type: 'value',
                axisLabel: {
                  textStyle: {
                    fontSize: 14,
                    color: '#FFFFFF',
                  },
                }
              }
            ],
            series: [
              {
                name: '兑换订单数量',
                type: 'bar',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                  opacity: 0.5
                },
                data: data.goods_order.count
              },
              {
                name: '兑换订单金额',
                type: 'line',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                  opacity: 0.5
                },
                data: data.goods_order.value
              },
              {
                name: '积分订单数量',
                type: 'bar',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                  opacity: 0.5
                },
                data: data.integral_order.count
              },
              {
                name: '积分订单金额',
                type: 'line',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                  opacity: 0.5
                },
                data: data.integral_order.value
              }
            ]
          };
        }).catch((e) => {
          this.$message.error(e.message);
        });
      },
      /* 获取回收站出库入库记录 */
      getStockData() {
        getStockLog().then((data) => {
          this.stockChartOption = {
            color: ['#80FFA5', '#00DDFF', '#37A2FF', '#FF0087'],
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: ['入库订单数量', '入库订单金额','出库订单数量', '出库订单金额',],
              right: 20,
              textStyle: {
                fontSize: 14,
                color: '#FFFFFF',
              },
            },
            grid: {
              left: "5%",
              bottom: "5%",
              containLabel: true,
            },
            xAxis: [
              {
                type: 'category',
                boundaryGap: true,
                data: data.x,
                axisLabel: {
                  interval: 0,
      			      rotate: 40,
                  textStyle: {
                    fontSize: 14,
                    color: '#FFFFFF',
                  },
                }
              }
            ],
            yAxis: [
              {
                type: 'value',
                axisLabel: {
                  textStyle: {
                    fontSize: 14,
                    color: '#FFFFFF',
                  },
                }
              }
            ],
            series: [
              {
                name: '入库订单数量',
                type: 'bar',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                  opacity: 0.5
                },
                data: data.stock_in.count
              },
              {
                name: '入库订单金额',
                type: 'line',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                  opacity: 0.5
                },
                data: data.stock_in.value
              },
              {
                name: '出库订单数量',
                type: 'bar',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                  opacity: 0.5
                },
                data: data.stock_out.count
              },
              {
                name: '出库订单金额',
                type: 'line',
                smooth: true,
                symbol: 'none',
                areaStyle: {
                  opacity: 0.5
                },
                data: data.stock_out.value
              }
            ]
          };
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
      },
      /* 获取用户浏览器分布数据 */
      getPieData() {
        getBalanceData()
          .then((data) => {



            let acitveData = [];
            acitveData.push(data[0]);

            this.$set(this.activeRignConfig1,'data',acitveData);

            acitveData = [];
            acitveData.push(data[1]);

            this.$set(this.activeRignConfig2,'data',acitveData);
            acitveData = [];
            acitveData.push(data[2]);
            this.$set(this.activeRignConfig3,'data',acitveData);
            acitveData = [];
            acitveData.push(data[3]);
            this.$set(this.activeRignConfig4,'data',acitveData);

            this.balanceChartOption = {
              tooltip: {
                trigger: 'item'
              },
              legend: {
                bottom: 5,
                itemWidth: 10,
                itemHeight: 10,
                icon: 'circle',
                data:  [ '合伙人','回收员','回收站','用户'],
              },
              series: [
                {
                  type: 'pie',
                  radius: ['45%', '70%'],
                  center: ['50%', '43%'],
                  label: {
                    show: false
                  },
                  data: data
                }
              ]
            };
          })
          .catch((e) => {
            this.$message.error(e.message);
          });
      }
    }
  };
</script>

<style scoped>

.dv-capsule-chart .label-column {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-sizing: border-box;
    padding-right: 10px;
    text-align: right;
    font-size: 16px;
}
.dv-capsule-chart .unit-label {
    height: 20px;
    font-size: 14px;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.dv-capsule-chart .unit-text {
    text-align: right;
    display: flex;
    align-items: flex-end;
    font-size: 14px;
    line-height: 20px;
    margin-left: 10px;
}

.dv-capsule-chart .capsule-item .capsule-item-column .capsule-item-value {
    font-size: 14px;
    transform: translateX(100%);
}
  /* 笑脸、哭脸 */
  .monitor-face-smile,
  .monitor-face-cry {
    width: 50px;
    height: 50px;
    display: inline-block;
    background-color: #fbd971;
    border-radius: 50%;
    position: relative;
  }

  .monitor-face-smile > span,
  .monitor-face-smile:before,
  .monitor-face-smile:after,
  .monitor-face-cry > span,
  .monitor-face-cry:before,
  .monitor-face-cry:after {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    transform: rotate(225deg);
    border: 3px solid #f0c419;
    border-right-color: transparent !important;
    border-bottom-color: transparent !important;
    position: absolute;
    bottom: 8px;
    left: 11px;
  }

  .monitor-face-smile:before,
  .monitor-face-smile:after,
  .monitor-face-cry:before,
  .monitor-face-cry:after {
    content: '';
    width: 6px;
    height: 6px;
    left: 8px;
    top: 14px;
    border-color: #f29c1f;
    transform: rotate(45deg);
  }

  .monitor-face-smile:after,
  .monitor-face-cry:after {
    left: auto;
    right: 8px;
  }

  .monitor-face-cry > span {
    transform: rotate(45deg);
    bottom: -6px;
  }

  /* 圆形进度条组合 */
  .monitor-progress-group {
    position: relative;
    display: inline-block;
  }

  .monitor-progress-group .el-progress:not(:first-child) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin-top: -1px;
  }

  .monitor-progress-legends > div + div {
    margin-top: 8px;
  }

  .workplace-goal-wrap {
    height: 125px;
    display: flex;
    justify-content: space-around;
  }

  .workplace-goal-group {
    text-align: center;
    position: relative;
  }

  .workplace-goal-group .workplace-goal-content {
    position: absolute;
    top: 25px;
    left: 0;
    width: 100%;
  }

  .workplace-goal-group .workplace-goal-num {
    font-size: 25px;
  }

  .text-conme{
    text-align: center;
    margin: auto;
  }
</style>
