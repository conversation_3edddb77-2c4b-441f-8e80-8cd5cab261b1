import axios from '@/utils/request';


/**
 * 
 * @param params 查询条件
 */
 export async function fontList(params) {
  const res = await axios.get('/figure.font/fontList', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 删除
 * @param id id
 */
export async function fontRemove(id) {
  const res = await axios.get('/figure.font/delete?id=' + id);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加
 * @param data 机构信息
 */
export async function fontAdd(data) {
  const res = await axios.post('/figure.font/add', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 修改
 * @param data 机构信息
 */
export async function fontUpdate(data) {
  const res = await axios.post('/figure.font/edit', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function task(params) {
  const res = await axios.get('/figure.clip/task', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function video(params) {
  const res = await axios.get('/figure.clip/video', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


export async function repost(params) {
  const res = await axios.get('/figure.clip/repost', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}




export async function taskInfo (params) {
  const res = await axios.get('/figure.clip/taskInfo', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
export async function material(params) {
    const res = await axios.get('/figure.clip/material', {
        params
    });
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

export async function model(params) {
    const res = await axios.get('/figure.clip/model', {
        params
    });
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}