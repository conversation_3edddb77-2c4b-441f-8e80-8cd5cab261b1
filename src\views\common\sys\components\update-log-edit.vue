<!-- 版本记录编辑弹窗 -->
<template>
  <ele-modal
    width="600px"
    :visible="visible"
    :append-to-body="true"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? '修改版本记录' : '添加版本记录'"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="类型:" prop="type">
        <el-radio-group v-model="form.type">
          <el-radio :label="1">重大更新</el-radio>
          <el-radio :label="2">功能更新</el-radio>
          <el-radio :label="3">问题修复</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="版本号:" prop="version">
        <el-input
          v-model="form.version"
          placeholder="请输入版本号，如：v2.0.2"
          maxlength="20"
        />
      </el-form-item>

      <el-form-item label="更新时间:" prop="sys_update_time">
        <el-date-picker
          v-model="form.sys_update_time"
          type="date"
          placeholder="请选择更新时间"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          style="width: 100%;"
        />
      </el-form-item>

      <el-form-item label="更新内容:" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="4"
          placeholder="请输入更新内容"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template v-slot:footer>
      <el-button @click="updateVisible(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script>
import { add, update } from '@/api/system/update-log';

const DEFAULT_FORM = {
  id: 0,
  type: 2,
  version: '',
  content: '',
  sys_update_time: ''
};

export default {
  name: 'UpdateLogEdit',
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  data() {
    return {
      // 表单数据
      form: { ...DEFAULT_FORM },
      // 表单验证规则
      rules: {
        type: [
          {
            required: true,
            message: '请选择类型',
            trigger: 'change'
          }
        ],
        version: [
          {
            required: true,
            message: '请输入版本号',
            trigger: 'blur'
          },
          {
            pattern: /^v?\d+\.\d+\.\d+$/,
            message: '版本号格式不正确，如：v2.0.2 或 2.0.2',
            trigger: 'blur'
          }
        ],
        sys_update_time: [
          {
            required: true,
            message: '请选择更新时间',
            trigger: 'change'
          }
        ],
        content: [
          {
            required: true,
            message: '请输入更新内容',
            trigger: 'blur'
          },
          {
            min: 5,
            message: '更新内容至少5个字符',
            trigger: 'blur'
          }
        ]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  methods: {
    /* 保存编辑 */
    save() {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          return false;
        }

        this.loading = true;
        const data = { ...this.form };
        
        const saveOrUpdate = this.isUpdate ? update : add;

        saveOrUpdate(data)
          .then((msg) => {
            this.loading = false;
            this.$message.success(msg);
            this.updateVisible(false);
            this.$emit('done');
          })
          .catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
          });
      });
    },
    /* 更新visible */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  },
  watch: {
    visible(visible) {
      if (visible) {
        if (this.data) {
          this.$util.assignObject(this.form, {
            ...this.data
          });
          this.isUpdate = true;
        } else {
          this.isUpdate = false;
        }
      } else {
        this.$refs['form'].clearValidate();
        this.form = { ...DEFAULT_FORM };
      }
    }
  }
};
</script>

<style scoped>
</style>
