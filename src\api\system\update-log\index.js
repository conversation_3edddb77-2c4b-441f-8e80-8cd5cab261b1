import axios from '@/utils/request';

/**
 * 获取版本记录列表
 * @param params 查询条件
 */
export async function list(params) {
  console.log('API请求参数:', params);
  const res = await axios.post('/figure.system_updateLog/list', params);
  console.log('API原始返回:', res.data);

  // 根据用户提供的返回格式，应该检查errno而不是code
  if (res.data.errno === 1) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加版本记录
 * @param data 版本记录信息
 */
export async function add(data) {
  const res = await axios.post('/figure.system_updateLog/add', data);
  if (res.data.errno === 1) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 修改版本记录
 * @param data 版本记录信息
 */
export async function update(data) {
  const res = await axios.post('/figure.system_updateLog/upd', data);
  if (res.data.errno === 1) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除版本记录
 * @param id 记录ID
 */
export async function remove(id) {
  const res = await axios.post('/figure.system_updateLog/del', { id });
  if (res.data.errno === 1) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
