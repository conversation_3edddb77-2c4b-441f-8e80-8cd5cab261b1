<!-- 快捷方式 -->
<template>
  <vue-draggable
    tag="el-row"
    v-model="data"
    :component-data="{ props: { gutter: 15 } }"
    :animation="300"
    :set-data="() => void 0"
    @end="onEnd"
  >
    <el-col
      v-for="item in data"
      :key="item.url"
      :lg="4"
      :md="4"
      :sm="4"
      :xs="12"
    >
      <el-card shadow="hover" body-style="padding: 0;">
        <router-link :to="item.url" class="app-link-block">
          <i
            :class="['app-link-icon', item.icon]"
            :style="{ color: item.color }"
          >
          </i>
          <div class="app-link-title">{{ item.title }}</div>
        </router-link>
      </el-card>
    </el-col>
  </vue-draggable>
</template>

<script>
  import VueDraggable from 'vuedraggable';
  export default {
    name: 'LinkCard',
    components: { VueDraggable },
    data() {
      // 默认布局
      const defaultData = [
        {
          icon: 'el-icon-_infinite',
          title: '合伙人',
          url: '/partner/list',
          color: '#ff0000'
        },
        {
          icon: 'el-icon-_connecting-line',
          title: '智能创作',
          url: '/cloneSet',
          color: '#1890ff'
        },
        {
          icon: 'el-icon-_fire',
          title: '会员',
          url: '/member/list',
          color: '#95de64'
        },
        {
          icon: 'el-icon-_user-group',
          title: '用户',
          url: '/user/list',
          color: '#ffd666'
        },
        {
          icon: 'el-icon-_trending-up',
          title: '统计',
          url: '/statis/index',
          color: '#ff9c6e'
        },
        {
          icon: 'el-icon-s-operation',
          title: '配置',
          url: '/common/sys',
          color: '#b37feb'
        }
      ];
      // 获取缓存顺序
      const cache = null;
     /* const cache = (() => {
        const str = localStorage.getItem('workplace-links');
        try {
          return str ? JSON.parse(str) : null;
        } catch (e) {
          return null;
        }
      })();*/
      return {
        defaultData,
        data: [...(cache ?? defaultData)]
      };
    },
    methods: {
      /* 排序改变 */
      onEnd() {
        this.cacheData();
      },
      /* 重置布局 */
      reset() {
        this.data = [...this.defaultData];
        this.cacheData();
      },
      /* 缓存布局 */
      cacheData() {
        localStorage.setItem('workplace-links', JSON.stringify(this.data));
      }
    }
  };
</script>

<style scoped>
  .app-link-block {
    display: block;
    color: inherit;
    padding: 15px 0;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
  }

  .app-link-block .app-link-icon {
    color: #69c0ff;
    font-size: 30px;
    margin-top: 5px;
  }

  .app-link-block .app-link-title {
    margin-top: 8px;
  }
</style>
