<template>
    <el-card class="monitor-count-card" shadow="never">
      <template v-slot:header>
        <div class="ele-cell">
          <div class="ele-cell-content title">作品统计</div>
          <div
            :class="['ele-inline-block', { 'hidden-sm-and-down': styleResponsive }]"
            style="width: 260px; margin-left: 10px"
          >
            <el-date-picker
              unlink-panels
              type="daterange"
              class="ele-fluid"
              end-placeholder="结束日期"
              start-placeholder="开始日期"
              v-model="datetime"
              range-separator="至"
              size="small"
              value-format="yyyy-MM-dd"
              @change="search(1)"
            />
          </div>
        </div>
      </template>
      <v-chart
        ref="workChart"
        style="height: 450px"
        :option="workChartOption"
      />
    </el-card>
    
  </template>
  
  <script>
  import VChart from 'vue-echarts';
  import { echartsMixin } from '@/utils/echarts-mixin';
  import { getWorkStatis} from '@/api/videoRepost';

  export default {
    name: "RepostStatis",
    components: { VChart },
    mixins: [echartsMixin(['workChart'])],
    computed: {
      // 是否开启响应式布局
      styleResponsive() {
        return this.$store.state.theme.styleResponsive;
      }
    },
    data() {
      return {
        grid: {
          xl: 8,
          lg: 8,
          md: 8,
          sm: 24,
          xs: 24,
        },
        options: this.$timeOptions,
        name: "近30天",
        timeVal: [],
        datetime: "",
        workChartOption: {},
        style: { height: "400px" },
        getExcel: "",
        spinShow: false,
      };
    },
    created() {
      const end = new Date();
      const start = new Date();
      start.setTime(
        start.setTime(
          new Date(
            new Date().getFullYear(),
            new Date().getMonth(),
            new Date().getDate() - 29
          )
        )
      );
      this.timeVal = [start, end];
  
      this.dataTime = this.$util.toDateString(start) + "~" +  this.$util.toDateString(end);
  
    },
    mounted() {
      this.getStatistics();
    },
    methods: {
      onSeach() {
        this.getStatistics();
      },
      // 具体日期
      onchangeTime(e) {
        
        this.timeVal = e;
        this.dataTime = this.timeVal.join("~");
        this.name = this.dataTime;
      },
      // 统计
      getStatistics() {
        let datyType = 0;
        let datetime = this.voiceDatetime;

        if(datetime != ''){
          datyType = 1;
        }

        const [d1, d2] = datetime ?? [];

        getWorkStatis({
          type:datyType,
          start_time:d1 ? d1 + ' 00:00:00' : '',
          end_time: d2 ? d2 + ' 23:59:59' : ''
        })
          .then((data) => {
            this.workChartOption = {
              tooltip: {
                trigger: 'axis',
                
              },
              xAxis: {
                type: 'category',
                data: data.date,
                axisTick: {
                  show: false
                },
                splitLine: {
                  show: false
                }
              },
              yAxis: {
                type: 'value',
                axisTick: {
                  show: false
                },
                splitLine: {
                    show: false
                }
              },
              series: [
                {
                  name:'D音发布',
                  data:data.dy,
                  type: 'bar',
                  showBackground: true,
                  backgroundStyle: {
                    color: 'rgba(180, 180, 180, 0.2)'
                  },
                  itemStyle: {
                    borderRadius: [20, 20, 0, 0]
                  }
                },
                {
                  name:'K手发布',
                  data:data.ks,
                  type: 'bar',
                  showBackground: true,
                  backgroundStyle: {
                    color: 'rgba(180, 180, 180, 0.2)'
                  },
                  itemStyle: {
                    borderRadius: [20, 20, 0, 0]
                  }
                }
              ]
            };
          })
          .catch((e) => {
            this.$message.error(e.message);
          });
      },
      excel() {
        window.location.href = this.getExcel;
      },
    },
  };
  </script>
  
  <style scoped lang="less">
  .one {
    background: #1890ff;
  }
  .two {
    background: #00c050;
  }
  .three {
    background: #ffab2b;
  }
  .four {
    background: #b37feb;
  }
  .up,
  .el-icon-caret-top {
    color: #f5222d;
    font-size: 12px;
    opacity: 1 !important;
  }
  
  .down,
  .el-icon-caret-bottom {
    color: #39c15b;
    font-size: 12px;
  }
  .curP {
    cursor: pointer;
  }
  .header {
    &-title {
      font-size: 20px;
    }
    &-time {
      font-size: 12px;
      opacity: 0.45;
    }
  }
  
  
  .iconfont {
    font-size: 16px;
  }
  
  .iconCrl {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    text-align: center;
    line-height: 32px;
    opacity: 0.7;
    color: white;
  }
  
  .lan {
    background: #1890ff;
  }
  
  .iconshangpinliulanliang {
    color: #fff;
  }
  
  .infoBox {
    width: 20%;
    @media screen and (max-width: 1300px) {
      width: 25%;
    }
    @media screen and (max-width: 1200px) {
      width: 33%;
    }
    @media screen and (max-width: 900px) {
      width: 50%;
    }
  }
  .flex-aling-center{
    display: flex;
    align-items: center;
  }
  .flex-wrp{
    display: flex;
    flex-wrap: wrap;
  }
  .flex-end{
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }
  .acea-row{
    -webkit-box-lines: multiple;
    -moz-box-lines: multiple;
    -o-box-lines: multiple;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  
  }
  .mr15{
    margin-right: 15px!important;
  }
  
  .mb20{
    margin-bottom: 20px!important;
  }
  .mb30{
    margin-bottom: 30px!important;
  }
  
  .info {
    .sp1 {
      color: #666;
      font-size: 14px;
      display: block;
    }
    .sp2 {
      font-weight: 400;
      font-size: 30px;
      display: block;
    }
    .sp3 {
      font-size: 12px;
      font-weight: 400;
      display: block;
    }
    .acea-row.row-between-wrapper {
      -webkit-box-pack: justify;
      -o-box-pack: justify;
      -ms-flex-pack: justify;
      justify-content: space-between;
    }
  
  .row-between-wrapper {
    -webkit-box-pack: justify;
    -o-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }
  
  
  }
  </style>