<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">支付配置</div>
      <div class="ele-page-desc"> 用于支付参数配置。 </div>
    </div>
    <div class="ele-body">
      <el-card shadow="never">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="230px"
          style="margin: 10px auto"
        >
          <el-row :gutter="15">
            <el-col :lg="12" :md="8">
              <el-form-item label="IOS支付:" prop="pay_open">
                <el-radio-group v-model="form.pay_open">
                  <el-radio label="1" value="1">开启</el-radio>
                  <el-radio label="2" value="2">隐藏</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item
                label="商家转账到零钱版本:"
                v-if="form.pay_remit_type == '2'"
                prop="wx_transfer_version"
              >
                <el-radio-group v-model="form.wx_transfer_version">
                  <el-radio label="1" value="1">2025年3月31日前</el-radio>
                  <el-radio label="2" value="2">2025年3月31日后</el-radio>
                </el-radio-group>
              </el-form-item>

              <!-- <el-form-item label="商家转账到零钱 现金营销 场景id" v-if="form.pay_remit_type == '2' " prop="transfer_scene_id">
                <el-input v-model="form.pay_service_appid" placeholder="商家转账到零钱现金营销场景id" class="my_input" clearable />
              </el-form-item>
 -->
 
              <el-form-item   v-if="form.pay_remit_type == '2' &&  form.wx_transfer_version == 2 " prop="name">
                <span slot="label">
                  <span>商家转账到零钱</span>
                  <span style="color: red"><strong>现金营销</strong> </span>
                  <span style="color: green"><strong>场景id</strong> </span>
                </span>
                <el-input
                  v-model="form.transfer_scene_id"
                  placeholder="商家转账到零钱现金营销场景id"
                  class="my_input"
                  clearable
                />
              </el-form-item>

              <el-form-item label="支付类型:" prop="pay_type">
                <el-radio-group v-model="form.pay_type">
                  <el-radio label="1" value="1">微信支付</el-radio>
                  <el-radio label="2" value="2">微信服务商支付</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item
                label="服务商小程序(AppId):"
                v-if="form.pay_type === '2'"
                prop="pay_service_appid"
              >
                <el-input
                  v-model="form.pay_service_appid"
                  placeholder="请输入服务商小程序APPID"
                  class="my_input"
                  clearable
                />
              </el-form-item>

              <el-form-item
                label="服务商支付商户号(Mch_Id):"
                v-if="form.pay_type === '2'"
                prop="pay_service_mchid"
              >
                <el-input
                  v-model="form.pay_service_mchid"
                  class="my_input"
                  placeholder="请输入服务商支付商户号"
                  clearable
                />
              </el-form-item>

              <el-form-item label="小程序(AppId):" prop="pay_appid">
                <el-input
                  v-model="form.pay_appid"
                  placeholder="请输入APPID"
                  class="my_input"
                  clearable
                />
              </el-form-item>

              <el-form-item label="小程序(AppSecret):" prop="pay_appsecret">
                <el-input
                  v-model="form.pay_appsecret"
                  type="password"
                  show-password
                  class="my_input"
                  placeholder="请输入appsecret"
                  clearable
                />
              </el-form-item>

              <el-form-item label="支付商户号(Mch_Id):" prop="pay_mchid">
                <el-input
                  v-model="form.pay_mchid"
                  placeholder="请输入支付商户号"
                  class="my_input"
                  clearable
                />
              </el-form-item>

              <el-form-item label="支付秘钥:" prop="pay_api_key">
                <el-input
                  v-model="form.pay_api_key"
                  type="password"
                  show-password
                  placeholder="请输入支付秘钥"
                  class="my_input"
                  clearable
                />
                <div v-if="form.pay_type === '2'" class="el-upload__text"
                  >服务商的APIKEY，并不是子商户的APIKEY</div
                >
              </el-form-item>
            </el-col>
            <el-col :lg="12" :md="8">
              <el-form-item label="打款类型:" prop="pay_remit_type">
                <el-radio-group v-model="form.pay_remit_type">
                  <el-radio label="1" value="1">企业付款到零钱</el-radio>
                  <el-radio label="2" value="2">商家转账到零钱</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item
                label="证书序列号:"
                prop="pay_serial_no"
                v-if="form.pay_remit_type === '2'"
              >
                <el-input
                  v-model="form.pay_serial_no"
                  placeholder="请输入子证书序列号"
                  class="my_input"
                  clearable
                />
              </el-form-item>

              <el-form-item label="cert文件:">
                <div v-if="form.pay_type === '2'" class="el-upload__text"
                  >服务商的cert证书文件</div
                >
                <el-upload
                  class="upload-demo"
                  ref="upload"
                  drag
                  :on-success="handleSuccess1"
                  :http-request="handleHttpRequest1"
                  :limit="1"
                  :disabled="disabled"
                  :beforeDelete="beforeDelete"
                  accept=".pem"
                  action=""
                  :file-list="fileList"
                >
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text"
                    >将文件拖到此处，或<em>点击上传</em></div
                  >
                </el-upload>
                <span v-if="form.pay_cert === '2'" style="color: #18b53c"
                  >已上传证书</span
                >
              </el-form-item>

              <el-form-item label="key文件:">
                <div v-if="form.pay_type === '2'" class="el-upload__text"
                  >服务商的key证书文件</div
                >
                <el-upload
                  class="upload-demo"
                  ref="upload"
                  drag
                  :on-success="handleSuccess2"
                  :http-request="handleHttpRequest2"
                  :limit="1"
                  :disabled="disabled"
                  :beforeDelete="beforeDelete"
                  accept=".pem"
                  action=""
                  :file-list="fileList"
                >
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text"
                    >将文件拖到此处，或<em>点击上传</em></div
                  >
                </el-upload>
                <span v-if="form.pay_key === '2'" style="color: #18b53c"
                  >已上传证书</span
                >
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button type="primary" :loading="loading" @click="submit">
              提交
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import EleImageUpload from 'ele-admin/es/ele-image-upload';
import request from '@/utils/request';
import { save, query } from '@/api/system/config';
export default {
  name: 'AgencyPay',
  data() {
    return {
      // 提交状态
      fileList: [],
      // 提交状态
      loading: false,
      disabled: false,
      is_cert_upload: 1,
      is_key_upload: 1,
      // 表单数据
      form: {
        pay_open: '2',
        pay_type: '1',
        pay_service_appid: '',
        pay_service_mchid: '',
        pay_appid: '',
        pay_appsecret: '',
        pay_mchid: '',
        pay_api_key: '',
        pay_cert: '',
        pay_key: '',
        pay_remit_type: '1',
        pay_serial_no: ''
      },

      // 表单验证规则
      rules: {
        pay_service_appid: [
          {
            required: true,
            message: '请输入服务商APPID',
            trigger: 'blur'
          }
        ],
        pay_service_mchid: [
          {
            required: true,
            message: '请输入服务商支付商户号',
            trigger: 'blur'
          }
        ],
        pay_appid: [
          {
            required: true,
            message: '请输入APPID',
            trigger: 'blur'
          }
        ],
        pay_appsecret: [
          {
            required: true,
            message: '请输入APP秘钥',
            trigger: 'blur'
          }
        ],

        pay_mchid: [
          {
            required: true,
            message: '请输入商户号',
            trigger: 'blur'
          }
        ],
        pay_api_key: [
          {
            required: true,
            message: '请输入支付秘钥',
            trigger: 'blur'
          }
        ]
      }
    };
  },
  methods: {
    /* 提交 */
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.validMsg = '';
          this.loading = true;
          const data = {
            ...this.form
          };
          save(data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(msg);
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });
        } else {
          return false;
        }
      });
    },
    onUpload(item) {
      item.status = 'uploading';
      const formData = new FormData();
      formData.append('file', item.file);
      request({
        url: '/common/uploadFile',
        method: 'post',
        data: formData,
        onUploadProgress: (e) => {
          // 文件上传进度回调
          if (e.lengthComputable) {
            item.progress = (e.loaded / e.total) * 100;
          }
        }
      })
        .then((res) => {
          if (res.data.code === 0) {
            item.status = 'done';
            item.url = res.data.data.url;
            // 如果你上传的不是图片格式, 建议将 url 字段作为缩略图, 再添加其它字段作为最后提交数据
            //item.url = res.data.data.thumbnail;  // 也可以不赋值 url 字段, 默认会显示为一个文件图标
            item.fileUrl = res.data.data.url;
          }
        })
        .catch((e) => {
          item.status = 'exception';
        });
    },
    beforeUpload() {},
    beforeDelete(elIndex) {
      let vm = this;
      return (file, name) => {
        let fileIndex = name.index;
        // console.log(fileIndex)
        vm.fileList[elIndex].splice(fileIndex, 1);
      };
    },
    handleHttpRequest1(option) {
      //上传OSS
      try {
        const formData = new FormData();
        formData.append('file', option.file);
        formData.append('type', 'cert');
        request({
          url: '/common/uploadPem',
          method: 'post',
          data: formData,
          onUploadProgress: (e) => {
            // 文件上传进度回调
            if (e.lengthComputable) {
              option.progress = (e.loaded / e.total) * 100;
            }
          }
        })
          .then((res) => {
            if (res.data.code === 0) {
              option.status = 'done';
              this.form.pay_cert = '2';
              // option.url = res.data.data.url;
              // 如果你上传的不是图片格式, 建议将 url 字段作为缩略图, 再添加其它字段作为最后提交数据
              //item.url = res.data.data.thumbnail;  // 也可以不赋值 url 字段, 默认会显示为一个文件图标
              //option.fileUrl = res.data.data.url;
            }
          })
          .catch((e) => {
            option.status = 'exception';
            this.disabled = false;
            option.onError('上传失败');
          });
      } catch (error) {
        console.error(error);
        this.disabled = false;
        option.onError('上传失败');
      }
    },
    handleHttpRequest2(option) {
      //上传OSS
      try {
        const formData = new FormData();
        formData.append('file', option.file);
        formData.append('type', 'key');
        request({
          url: '/common/uploadPem',
          method: 'post',
          data: formData,
          onUploadProgress: (e) => {
            // 文件上传进度回调
            if (e.lengthComputable) {
              option.progress = (e.loaded / e.total) * 100;
            }
          }
        })
          .then((res) => {
            if (res.data.code === 0) {
              option.status = 'done';
              this.form.pay_key = '2';
              // option.url = res.data.data.url;
              // 如果你上传的不是图片格式, 建议将 url 字段作为缩略图, 再添加其它字段作为最后提交数据
              //item.url = res.data.data.thumbnail;  // 也可以不赋值 url 字段, 默认会显示为一个文件图标
              //option.fileUrl = res.data.data.url;
            }
          })
          .catch((e) => {
            option.status = 'exception';
            this.disabled = false;
            option.onError('上传失败');
          });
      } catch (error) {
        console.error(error);
        this.disabled = false;
        option.onError('上传失败');
      }
    },
    handleSuccess1(response, file, fileList) {
      this.fileList = this.fileList.concat(fileList);
      this.form.pay_cert = '2';
    },
    handleSuccess2(response, file, fileList) {
      this.fileList = this.fileList.concat(fileList);
      this.form.pay_key = '2';
      debugger;
    }
  },
  mounted() {
    query({ group: 'pay' })
      .then((msg) => {
        if (msg != null) {
          this.form = msg;
        }
      })
      .catch((e) => {
        this.$message.error(e.message);
      });
  }
};
</script>
<style>
.my_input {
  width: 400px;
}
.pay_header {
  margin-bottom: 30px;
  font-size: 17px;
}
</style>
