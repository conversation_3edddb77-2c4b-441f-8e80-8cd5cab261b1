<template>
    <div>
        <div class="ele-page-header">
            <div class="ele-page-title">定时任务</div>
            <div class="ele-page-desc">
                用于定时任务配置。
            </div>
        </div>
        <div class="ele-body">
            <el-card shadow="never" style="padding-bottom: 71px;height:1800px;">
                <el-row :gutter="20" style="margin-bottom: 20px;">

                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">视频合成定时任务</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每2分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/videoTimer'}}
                                            <el-link type="success" @click="copy_url(host +'/mobile/timer/videoTimer')">
                                                复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>
                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">高级形象克隆定时任务</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每2分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/avatarTimer'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/timer/avatarTimer')">复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>
                </el-row>
                <el-row :gutter="20" style="margin-bottom: 20px;">

                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">入门版声音克隆定时任务</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每2分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/voiceTimer'}}
                                            <el-link type="success" @click="copy_url(host +'/mobile/timer/voiceTimer')">
                                                复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>
                       <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">二次剪辑提交媒资</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每1分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/clip/clipVideo'}}
                                            <el-link type="success" @click="copy_url(host +'/mobile/clip/clipVideo')">
                                                复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>
                </el-row>

                <el-row :gutter="20" style="margin-bottom: 20px;">
                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">获取DY转发数据定时任务</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每2分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/dyDataTimer'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/timer/dyDataTimer')">复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>
                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">获取KS转发数据定时任务</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每2分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/ksDataTimer'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/timer/ksDataTimer')">复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>
                </el-row>


                <el-row :gutter="20" style="margin-bottom: 20px;">

                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">二次剪辑提交合成任务</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每1分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/clip/clipVideoResource'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/clip/clipVideoResource')">复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>

                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">二次剪辑查询合成任务状态</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每1分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/clip/selectClipVideo'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/clip/selectClipVideo')">复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>
                </el-row>


                <el-row :gutter="20" style="margin-bottom: 20px;">

                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">
                                        2025-3-31,提现需配置定时任务</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每1分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/getTransfer'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/timer/getTransfer')">复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>
                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">合伙人分成</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每天00:01跑一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/shareholder'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/timer/shareholder')">复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>

                </el-row>

            

            


                <el-row :gutter="20" style="margin-bottom: 20px;">

                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">抖音发布视频</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每1分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/releaseDouyinVideo'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/timer/releaseDouyinVideo')">复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>

                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">抖音视频详情</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每1分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/douyinVideoInfo'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/timer/douyinVideoInfo')">复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>


                </el-row>


                <el-row :gutter="20" style="margin-bottom: 20px;">

                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">快手发布视频</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每1分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/releaseKuaishouVideo'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/timer/releaseKuaishouVideo')">复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>

                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">快手视频详情</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每1分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/KuaishuoVideoInfo'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/timer/KuaishuoVideoInfo')">复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>


                </el-row>


                <el-row :gutter="20" style="margin-bottom: 20px;">

                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">视频号异步上传视频</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每5分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/uploadShipinghaoVideo'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/timer/uploadShipinghaoVideo')">复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>

                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">查询视频号上传状态</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每5分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/shipinghaoVideoUploadStatus'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/timer/shipinghaoVideoUploadStatus')">
                                                复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>


                </el-row>


                <el-row :gutter="20" style="margin-bottom: 20px;">

                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">视频号发布视频</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每10分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/releaseShipinghaoVideo'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/timer/releaseShipinghaoVideo')">复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>

                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">发布小红书视频</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每3分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host
                                            +'/mobile/timer/releaseXiaohongshuVideo'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/timer/releaseXiaohongshuVideo')">复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>


                </el-row>

                <el-row :gutter="20" style="margin-bottom: 20px;">
                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">小红书视频详情</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每1分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/xiaohongshuVideoInfo'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/timer/xiaohongshuVideoInfo')">复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>

                     <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">批量合成视频</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每2分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/generate'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/timer/generate')">复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>

                </el-row>
        <el-row :gutter="20" style="margin-bottom: 20px;">
                    <el-col :md="12" :sm="12" :xs="12">
                        <el-alert type="info" :closable="false" class="ele-alert-border">
                            <div class="ele-cell">
                                <div class="ele-cell-content">
                                    <div class="ele-cell-title"><h5 style="margin: 5px 0 15px 0">IP账号采集视频分析</h5></div>
                                    <div class="ele-cell-desc" style="font-size:14px;"> 执行时间：建议每5分钟执行一次</div>
                                    <div class="ele-cell-desc">
                                        <h6 style="margin-bottom: 10px">{{host +'/mobile/timer/homepageVideo'}}
                                            <el-link type="success"
                                                     @click="copy_url(host +'/mobile/timer/homepageVideo')">复制
                                            </el-link>
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </el-alert>
                    </el-col>

                    
                </el-row>

            </el-card>
        </div>
    </div>
</template>

<script>

    export default {
        name: 'SysTimer',
        data() {
            return {
                host: window.location.protocol + "//" + location.host
            };
        },
        methods: {
            copy_url(content) {
                // window.clipboardData.setData('text', context);

                if (window.clipboardData) {
                    /*
                    window.clipboardData有三个方法:
                  （1）clearData(sDataFormat) 删除剪贴板中指定格式的数据。sDataFormat:"text","url"
                  （2）getData(sDataFormat) 从剪贴板获取指定格式的数据。 sDataFormat:"text","url"
                  （3）setData(sDataFormat, sData) 给剪贴板赋予指定格式的数据。返回 true 表示操作成功。
                    */
                    window.clipboardData.setData('text', content);
                } else {
                    (function (content) {
                        //oncopy 事件在用户拷贝元素上的内容时触发。
                        document.oncopy = function (e) {
                            e.clipboardData.setData('text', content);
                            e.preventDefault(); //取消事件的默认动作
                            document.oncopy = null;
                        }
                    })(content);
                    //execCommand方法是执行一个对当前文档/当前选择/给出范围的命令。
                    //'Copy':将当前选中区复制到剪贴板。
                    document.execCommand('Copy');
                }
                this.$message.success('复制成功！');
            },
        }
    };
</script>
