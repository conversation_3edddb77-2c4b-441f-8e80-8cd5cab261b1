<!-- 用户编辑弹窗 -->
<template>
  <ele-modal
    width="740px"
    :visible="visible"
    :append-to-body="true"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? '修改分销等级' : '添加分销等级'"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="140px">
      <el-form-item label="等级名称:" prop="name">
        <el-input v-model="form.name" placeholder="请输入等级名称不超过4个字" />
      </el-form-item>

      <el-form-item label="级别:" prop="level"  v-if=" form.level != 1" >
        <el-input
          type="number"
      
          v-model="form.level"
          placeholder="请输入等级级别"
        />
      </el-form-item>
      
      <el-form-item label="级别:" prop="level"  v-if=" form.level == 1" >
        <el-input
          type="number"
      disabled
          v-model="form.level"
          placeholder="请输入等级级别"
        />
      </el-form-item>

      <el-form-item label="描述:" prop="desc">
        <el-input v-model="form.desc" placeholder="请输入描述" />
      </el-form-item>

      <el-form-item
        label="消费金额:"
        prop="consumption_amount"
        v-if="distribution_mode == 1 && form.level == 1"
      >
        <el-input v-model="form.consumption_amount"  disabled placeholder="消费金额" />
      </el-form-item>
      <el-form-item
        label="消费金额:"
        prop="consumption_amount"
        v-if="distribution_mode == 1 && form.level != 1"
      >
        <el-input v-model="form.consumption_amount"  placeholder="消费金额" />
      </el-form-item>

      <el-form-item
        label="套餐列表:"
        prop="package_id"
        v-if="distribution_mode == 2"
      >
        <el-select
          v-model="form.package_id"
          placeholder="请选择"
          @change="changeValue"
        >
          <el-option
            v-for="item in packageList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="购买套餐数量:"
        prop="package_count"
        v-if="distribution_mode == 2 && form.level == 1"
      >
        <el-input v-model="form.package_count"  disabled   placeholder="购买套餐数量" />
      </el-form-item>

       <el-form-item
        label="购买套餐数量:"
        prop="package_count"
        v-if="distribution_mode == 2 && form.level != 1"
      >
        <el-input v-model="form.package_count"     placeholder="购买套餐数量" />
      </el-form-item>

      <el-form-item label="直推首购分佣比例:" prop="one_level_income">
        <el-input-number
          type="number"
        
          v-model="form.one_level_income"
          placeholder="直推分佣比例"
        />%
      </el-form-item>

      <el-form-item label="间推首购分佣比例:" prop="two_level_income">
        <el-input-number
          type="number"
       
          v-model="form.two_level_income"
          placeholder="间推分佣比例"
        />%
      </el-form-item>

      <el-form-item
        label="直推复购分佣比例:"
        prop="one_level_repurchase_income"
      >
        <el-input-number
          type="number"
       
          v-model="form.one_level_repurchase_income"
          placeholder="直推分佣比例"
        />%
      </el-form-item>

      <el-form-item
        label="间推复购分佣比例:"
        prop="two_level_repurchase_income"
      >
        <el-input-number
          type="number"
        
          v-model="form.two_level_repurchase_income"
          placeholder="间推分佣比例"
        />%
      </el-form-item>
       <el-form-item  label="是否开启股东分红:" prop="is_shareholder_swich">
                <el-radio-group  v-model="form.is_shareholder_swich" size="small">
                  <el-radio-button :label=1 :value=1>开启</el-radio-button>
                  <el-radio-button :label=2 :value=2>关闭</el-radio-button>
                </el-radio-group>
              </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="updateVisible(false)">取消 </el-button>
      <el-button type="primary" :loading="loading" @click="save"
        >保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script>
import { add, update, queryBrokerageSet } from '@/api/brokerage';

import { list } from '@/api/member/list';
import { getPages } from '@/api/layout';
const DEFAULT_FORM = { 
     id: 0,
    name: '',
    level: '',
    consumption_amount: '',
    one_level_income: '',
    two_level_income: '',
    one_level_repurchase_income: '',
    two_level_repurchase_income: '',
    one_shareholder_income: '',
    two_shareholder_income: '',
    one_shareholder_repurchase_income: '',
    two_shareholder_repurchase_income: '',
    desc:'',
    package_id:'',
    package_count:'',
    is_shareholder_swich:''
   
    };

export default {
  name: 'LevelEdit',
  components: {
   
  },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  computed: {
    balanceName() {
      return this.$store.state.user.balance_name;
    }
  },
  data() {
    return {
      distribution_mode: '',
      packageList: [],

      modalTitle: '选择图片',
      modalPic: false,
      isChoice: '单选',
      gridBtn: {
        xl: 4,
        lg: 8,
        md: 8,
        sm: 8,
        xs: 8
      },
      gridPic: {
        xl: 6,
        lg: 8,
        md: 12,
        sm: 12,
        xs: 12
      },
      // 表单数据
      form: { ...DEFAULT_FORM },
      options: [],
      // 表单验证规则
      rules: {
        name: [
          {
            required: true,
            message: '请输入输入等级名称',
            trigger: 'blur'
          }
        ]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      tableConfig: {
        datasource: ({ page, limit, where, order }) => {
          return getPages({ ...where, ...order, page, limit });
        },
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            align: 'center'
          },
          {
            prop: 'name',
            label: '名称',
            showOverflowTooltip: true,
            minWidth: 110
            //slot: 'nickname'
          },
          {
            prop: 'url',
            label: '路径',
            showOverflowTooltip: true,
            minWidth: 110
          }
        ],
        rowClickChecked: true,
        rowClickCheckedIntelligent: false,
        toolkit: ['reload', 'columns'],
        size: 'small',
        toolStyle: { padding: '0 10px' }
      }
    };
  },
  methods: {
    updateSecond() {
      if (this.form.second_infinite == 1) {
        this.form.second = 0;
      }
    },
    search(where) {
      // debugger
      this.$refs.select.reload({
        where: where,
        page: 1
      });
    },
    changeValue(e) {
      this.form.package_id = e;
    },
    pagelist() {
      list({ page: 1, limit: 10 })
        .then((data) => {
          if (data != null) {
            this.packageList = data.list.map((item) => {
              return {
                value: item.id,
                label: item.name
              };
            });
            console.log(this.packageList)
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },

    /* 保存编辑 */
    queryBrokerageSetFn() {
      queryBrokerageSet()
        .then((data) => {
          if (data != null) {
            this.distribution_mode = data.distribution_mode;
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },
    save() {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          return false;
        }
        this.loading = true;
        const data = {
          ...this.form
        };
        const saveOrUpdata = this.isUpdate ? update : add;
        saveOrUpdata(data)
          .then((msg) => {
            this.loading = false;
            this.$message.success(msg);
            this.updateVisible(false);
            this.$emit('done');
          })
          .catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
          });
      });
    },
    /* 更新visible */
    updateVisible(value) {
      this.$emit('update:visible', value);
    }
  },
  watch: {
    visible(visible) {
      if (visible) {
        if (this.data) {
          console.log(this.data)
          this.$util.assignObject(this.form, {
            ...this.data
          });
          this.form.package_id=Number(this.data.package_id)
          this.isUpdate = true;
           this.queryBrokerageSetFn();
            this.pagelist();
        } else {
           this.queryBrokerageSetFn();
            this.pagelist();
         
          this.isUpdate = false;
        }
      } else {
        this.$refs['form'].clearValidate();
        this.form = { ...DEFAULT_FORM };
      }
    }
  }
};
</script>
