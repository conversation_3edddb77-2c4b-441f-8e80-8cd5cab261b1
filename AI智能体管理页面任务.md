# 上下文
文件名：AI智能体管理页面任务.md
创建于：2025-08-16
创建者：AI Assistant
关联协议：RIPER-5 + Multidimensional + Agent Protocol (Conditional Interactive Step Review Enhanced)

# 任务描述
创建一个AI智能体管理页面，包含以下具体功能和要求：

## 页面功能需求
1. **新增智能体功能**：
   - 空间ID下拉选择器（必填）
   - 智能体ID下拉选择器（必填，依赖于空间ID选择，实现级联选择）
   - 图片上传功能（参考现有的案例管理页面中的图片上传实现）
   - 智能体名称输入框（必填）
   - 智能体描述文本域（可选）
   - 类型单选按钮组：文字、图片、视频（必填）
   - 启用状态开关（默认开启，值为1表示开启，0表示关闭）

2. **列表展示功能**：
   - 以表格形式展示所有智能体信息
   - 包含所有上述字段的显示
   - 提供编辑和删除操作按钮

3. **编辑和删除功能**：
   - 支持弹窗编辑
   - 支持单个删除操作
   - 需要确认提示防止误操作

## 技术要求
- 请先定义接口结构和数据库字段名称
- 实现前后端数据交互
- 确保级联选择的用户体验流畅
- 图片上传功能需要支持预览和删除
- 表单验证和错误提示

# 项目概述
基于Vue.js + Element UI的数字人平台管理系统，需要添加AI智能体管理功能模块。

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)
- 项目使用Vue.js + Element UI技术栈
- 图片上传使用 `/common/uploadFile` 接口
- 数据交互使用axios + 统一的请求处理
- 表单验证使用Element UI的表单验证
- 级联选择可使用el-cascader或自定义组件
- API接口统一使用 `list`、`add`、`update`、`remove` 方法
- 数据字段使用下划线命名（如 `pic_url`、`is_display`）
- 状态值：1表示启用，0/-1表示禁用
- 表单结构使用DEFAULT_FORM常量定义默认值

# 提议的解决方案 (由 INNOVATE 模式填充)
**数据库设计思路**：
使用 `ai_agent` 作为表名，字段采用下划线命名法。需要考虑空间ID和智能体ID的级联关系，以及图片存储的方式。

**接口设计方案**：
参考现有的API模式，创建 `/figure.code/` 系列接口，包含标准的增删改查操作。同时需要额外的接口来支持空间列表和智能体ID的级联查询。

**前端组件架构**：
复用现有的图片上传组件，创建自定义的级联选择器，并遵循项目现有的表单验证和数据处理模式。

**级联选择实现方案**：
使用两个独立的el-select，通过监听第一个选择器的变化来动态加载第二个选择器的数据，提供更好的交互体验和更灵活的数据处理能力。

# 实施计划 (由 PLAN 模式生成)

## 数据库字段设计
**表名**: `ai_agent`
**字段定义**:
```sql
id - 主键ID (int, auto_increment)
space_id - 空间ID (varchar(50), 必填)
bots_id - 智能体ID (varchar(50), 必填) 
name - 智能体名称 (varchar(100), 必填)
des - 智能体描述 (text, 可选)
icon - 图片URL (varchar(255), 可选)
type - 类型 (tinyint, 1=文字, 2=图片, 3=视频, 必填)
open_off - 启用状态 (tinyint, 1=启用, 0=禁用, 默认1)
sort - 排序 (int, 默认0)
create_time - 创建时间 (datetime)
update_time - 更新时间 (datetime)
```

## 接口设计
**基础路径**: `/figure.code/`
**接口列表**:
1. `POST /figure.code/list` - 分页查询智能体列表 (参数：page、size，筛选：name，space_id，type，open_off)
2. `POST /figure.code/add` - 新增智能体 (参数：space_id、bots_id、icon、name、des、type、open_off、sort)
3. `POST /figure.code/edit` - 编辑智能体
4. `POST /figure.code/remove` - 删除智能体
5. `GET /figure.code/work` - 获取空间列表
6. `POST /figure.code/bots` - 根据空间ID获取智能体ID列表 (参数：work_id)

## 实施检查清单：
1. [创建API接口文件 `src/api/aiAgent/index.js`，包含所有CRUD操作和级联查询接口, review:true] ✅
2. [创建智能体管理主页面 `src/views/aiAgent/index.vue`，包含表格展示和工具栏, review:true] ✅
3. [创建智能体编辑组件 `src/views/aiAgent/components/agent-edit.vue`，包含完整的表单和验证, review:true] ✅
4. [创建智能体搜索组件 `src/views/aiAgent/components/agent-search.vue`，支持条件筛选, review:true] ✅
5. [创建空间-智能体级联选择组件，实现级联选择功能, review:true] ✅
6. [集成图片上传功能到编辑组件中，复用现有的uploadPictures组件, review:true] ✅
7. [实现表单验证规则，确保必填字段和数据格式正确性, review:true] ✅
8. [添加路由配置，将新页面集成到系统菜单中, review:true] ✅
9. [根据用户反馈更新接口和字段名称，实现智能体选择后自动填充功能, review:true] ✅
10. [测试完整的增删改查功能，确保数据交互正常, review:false]

# 当前执行步骤 (由 EXECUTE 模式在开始执行某步骤时更新)
> 正在执行: "步骤9：根据用户反馈更新接口和字段名称" (审查需求: review:true, 状态: 已完成)

# 任务进度 (由 EXECUTE 模式在每步完成后，以及在交互式审查迭代中追加)
*   2025-08-16 13:30
    *   步骤：检查清单项目1-4 (审查需求: review:true, 状态：初步完成)
    *   修改：创建了API接口文件、主页面、编辑组件、搜索组件
    *   更改摘要：完成了基础的CRUD页面结构和API接口定义
    *   原因：执行计划步骤1-4的初步实施
    *   阻碍：无
    *   用户确认状态：需要根据实际接口格式进行调整

*   2025-08-16 13:45
    *   步骤：检查清单项目5-8 (审查需求: review:true, 状态：初步完成)
    *   修改：实现级联选择、图片上传、表单验证、菜单集成
    *   更改摘要：完成了核心功能实现和菜单集成
    *   原因：执行计划步骤5-8的初步实施
    *   阻碍：无
    *   用户确认状态：需要根据用户提供的具体接口要求进行调整

*   2025-08-16 14:00
    *   步骤：检查清单项目9 (审查需求: review:true, 状态：已完成)
    *   修改：
        - 更新API接口以匹配实际格式（/figure.code/list POST, /figure.code/add POST等）
        - 更新字段名称（agent_id->bots_id, agent_name->name, agent_desc->des, pic_url->icon, status->open_off）
        - 实现智能体选择后自动填充图片(icon_url)、名称(name)、描述(description)
        - 更新状态值（1=启用, 0=禁用）
        - 更新列表接口参数格式（page, size）
        - 更新所有相关组件的字段引用
    *   更改摘要：根据用户提供的实际接口规范完成了全面的字段和接口更新
    *   原因：用户提供了具体的接口格式和字段要求
    *   阻碍：无
    *   用户确认状态：待确认

# 最终审查 (由 REVIEW 模式填充)
[待完成]
