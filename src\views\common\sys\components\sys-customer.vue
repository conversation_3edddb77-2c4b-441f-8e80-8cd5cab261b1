<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">客服配置</div>
      <div class="ele-page-desc">
      
      </div>
    </div>
    <div class="ele-body">
      <el-card shadow="never">
        <el-form
          ref="form"
          :model="form"

          label-width="120px"
          style="max-width: 700px; margin: 10px auto"
        >

          <el-form-item label="客服类型:">
            <el-radio-group v-model="form.customer_type">
              <el-radio label="phone" value="phone">手机号</el-radio>
              <el-radio label="qr_code" value="qr_code">二维码</el-radio>
              <el-radio label="on_line" value="on_line">在线客服</el-radio>
               <!-- <el-radio label="enterprise_wx" value="enterprise_wx">企业微信</el-radio> -->
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="form.customer_type =='phone'" label="电话号码:" prop="customer_phone">
            <el-input v-model="form.customer_phone" placeholder="请输入电话号码" clearable />
          </el-form-item>

<!-- <el-form-item v-if="form.customer_type =='enterprise_wx'" label="企业微信:" prop="enterprise_wx">
            <el-input v-model="form.enterprise_wx" placeholder="企业微信" clearable />
          </el-form-item> -->

   <el-form-item v-if="form.customer_type == 'qr_code'" label="二维码:" prop="customer_qr_code">
        <span slot="label">
          二维码
          
          <!-- <el-tooltip placement="top">
            <div slot="content" v-if="templateType == 1">
              模板1：建议上传750px * 950px尺寸,或者等比例图片
            </div>
            <div slot="content" v-if="templateType == 2">
              模板2：建议上传750px * 950px尺寸,或者等比例图片
            </div>
            <div slot="content" v-if="templateType == 3">
              模板3：建议上传710px * 340px尺寸,或者等比例图片
            </div>
            <i class="el-icon-question" /> 
          </el-tooltip>-->
        </span>  
        <!--<ele-image-upload v-model="img" :limit="1" :drag="true" :multiple="false"  @upload="onUpload" /> --> 
          <div class="ele-image-upload-list">
          <div class="ele-image-upload-item ele-image-upload-button" @click="modalPicTap('dan','customer_qr_code','图片')">
            <div>
              <div tabindex="0" class="el-upload el-upload--text">
                <div class="el-upload-dragger">
                  <i class="el-icon-plus ele-image-upload-icon"></i>
                </div>
                  <div class="ele-image-upload-item" style="margin:0 0 0 0;" v-if="form.customer_qr_code!=''">
                  <div class="el-image" >
                    <img :src="form.customer_qr_code" width="100%" height="100%"  class="el-image__inner" style="object-fit: cover;">
                  </div>
                  <div class="ele-image-upload-close" @click="handleRemove('customer_qr_code')"><i class="el-icon-close"></i></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-form-item>

          <el-form-item>
            <el-button type="primary" :loading="loading" @click="submit">
              提交
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
      <uploadPictures
      :isChoice="isChoice"
      :visible.sync="modalPic"
      @getPic="getPic"
      :gridBtn="gridBtn"
      :gridPic="gridPic"
      :title="modalTitle"
    ></uploadPictures>
  </div>
</template>

<script>
  import uploadPictures from "@/components/uploadPictures";
  import { save ,query } from '@/api/system/config';
  export default {
    components:{
      uploadPictures
    },
    name: 'SysUpload',
    data() {
      return {
        // 提交状态
        loading: false,
        // 表单数据
        form: {
          customer_type:1,
          customer_phone:'',
          customer_qr_code :''
        },

        modalTitle:'选择图片',
        modalPic: false,
        isChoice: "单选",
        gridBtn: {
          xl: 4,
          lg: 8,
          md: 8,
          sm: 8,
          xs: 8,
        },
        gridPic: {
          xl: 6,
          lg: 8,
          md: 12,
          sm: 12,
          xs: 12,
        },
       
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false,

        // 表单验证规则
    
        
      };
    },
    methods: {
        // 选择图片
      modalPicTap(tit, picTit,openTitle) {
        this.modalTitle = openTitle;
        this.isChoice = tit === "dan" ? "单选" : "多选";
        this.picTit = picTit;
        this.modalPic = true;
      },
      // 选中图片
      getPic(pc) {
        switch (this.picTit) {
          case "customer_qr_code":
            this.form.customer_qr_code = pc.satt_dir;
            break;
          case "background":
            this.form.background = pc.satt_dir;
            break;
        }
        this.modalPic = false;
      },
      //删除图片
      handleRemove(field) {
        this.form[field] = '';
      },
      /* 提交 */
      submit() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            this.loading = true;
          this.loading = true;
          save(this.form)
            .then((msg) => {
              this.loading = false;
              this.$message.success(msg);
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });


          } else {
            return false;
          }
        });
      },
   
    },
    
     mounted() {

      
            query({group:'customer'})
             .then((msg) => {
              if(msg != null){
               this.form = msg;  
               }
              
             })
             .catch((e) => {
               this.$message.error(e.message);
             });
     }
    
  };
</script>

<style scoped>
.el-form-item{
  margin-left: 100px;
}
</style>
