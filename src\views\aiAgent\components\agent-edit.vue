<template>
  <ele-modal
    width="800px"
    :visible="visible"
    :confirm-loading="loading"
    title="智能体信息"
    :body-style="{ paddingBottom: '8px' }"
    @update:visible="updateVisible"
    @ok="save"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-row :gutter="15">
        <el-col :md="12" :sm="24" :xs="24">
          <el-form-item label="空间ID:" prop="space_id">
            <el-select
              v-model="form.space_id"
              placeholder="请选择空间ID"
              clearable
              filterable
              @change="onSpaceChange"
              style="width: 100%"
            >
              <el-option
                v-for="item in spaceList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :md="12" :sm="24" :xs="24">
          <el-form-item label="智能体ID:" prop="bots_id">
            <el-select
              v-model="form.bots_id"
              placeholder="请选择智能体ID"
              clearable
              filterable
              :disabled="!form.space_id"
              @change="onAgentChange"
              style="width: 100%"
            >
              <el-option
                v-for="item in agentList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="15">
        <el-col :md="12" :sm="24" :xs="24">
          <el-form-item label="智能体名称:" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入智能体名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :md="12" :sm="24" :xs="24">
          <el-form-item label="类型:" prop="type">
            <el-radio-group v-model="form.type">
              <el-radio :label="1">文字</el-radio>
              <el-radio :label="2">图片</el-radio>
              <el-radio :label="3">视频</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="15">
        <el-col :md="12" :sm="24" :xs="24">
          <el-form-item label="启用状态:" prop="open_off">
            <el-switch
              v-model="form.open_off"
              :active-value="1"
              :inactive-value="0"
              active-text="启用"
              inactive-text="禁用"
            />
          </el-form-item>
        </el-col>
        <el-col :md="12" :sm="24" :xs="24">
          <el-form-item label="排序:" prop="sort">
            <el-input-number
              v-model="form.sort"
              :min="0"
              :max="9999"
              controls-position="right"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="智能体图片:">
        <div class="upload-container">
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :before-upload="beforeUpload"
            :http-request="handleHttpRequest"
          >
            <img v-if="form.icon" :src="form.icon" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <div class="upload-tips">
            <p>支持jpg、png格式，建议尺寸200x200像素</p>
            <el-button
              v-if="form.icon"
              type="text"
              size="small"
              @click="removePicture"
            >
              删除图片
            </el-button>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="智能体描述:">
        <el-input
          v-model="form.des"
          type="textarea"
          :rows="4"
          placeholder="请输入智能体描述（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>
  </ele-modal>
</template>

<script>
  import { add, update, getSpaceList, getAgentList } from '@/api/aiAgent';
  import request from '@/utils/request';

  // 表单默认值
  const DEFAULT_FORM = {
    id: 0,
    space_id: '',
    bots_id: '',
    name: '',
    des: '',
    icon: '',
    type: 1,
    open_off: 1,
    sort: 0
  };

  export default {
    name: 'AgentEdit',
    props: {
      // 修改回显的数据
      data: Object,
      // 是否显示弹窗
      visible: Boolean
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          space_id: [
            {
              required: true,
              message: '请选择空间ID',
              trigger: 'change'
            }
          ],
          bots_id: [
            {
              required: true,
              message: '请选择智能体ID',
              trigger: 'change'
            }
          ],
          name: [
            {
              required: true,
              message: '请输入智能体名称',
              trigger: 'blur'
            }
          ],
          type: [
            {
              required: true,
              message: '请选择类型',
              trigger: 'change'
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false,
        // 空间列表
        spaceList: [],
        // 智能体列表
        agentList: []
      };
    },
    watch: {
      // 监听弹窗显示
      visible() {
        if (this.visible) {
          if (this.data) {
            this.form = { ...this.data };
            this.isUpdate = true;
            // 如果是编辑模式，需要加载对应的智能体列表
            if (this.form.space_id) {
              this.loadAgentList(this.form.space_id);
            }
          } else {
            this.form = { ...DEFAULT_FORM };
            this.isUpdate = false;
            this.agentList = [];
          }
          this.loadSpaceList();
        }
      }
    },
    methods: {
      /* 保存 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            this.loading = true;
            const saveMethod = this.isUpdate ? update : add;
            saveMethod(this.form)
              .then((msg) => {
                this.loading = false;
                this.$message.success(msg);
                this.updateVisible(false);
                this.$emit('done');
              })
              .catch((e) => {
                this.loading = false;
                this.$message.error(e.message);
              });
          }
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 加载空间列表 */
      loadSpaceList() {
        getSpaceList()
          .then((data) => {
            this.spaceList = data || [];
          })
          .catch((e) => {
            this.$message.error('加载空间列表失败：' + e.message);
          });
      },
      /* 加载智能体列表 */
      loadAgentList(spaceId) {
        if (!spaceId) {
          this.agentList = [];
          return;
        }
        getAgentList({ work_id: spaceId })
          .then((data) => {
            this.agentList = data || [];
          })
          .catch((e) => {
            this.$message.error('加载智能体列表失败：' + e.message);
          });
      },
      /* 空间选择变化 */
      onSpaceChange(spaceId) {
        this.form.bots_id = '';
        this.form.name = '';
        this.form.des = '';
        this.form.icon = '';
        this.loadAgentList(spaceId);
      },
      /* 智能体选择变化 */
      onAgentChange(agentId) {
        if (agentId) {
          const selectedAgent = this.agentList.find(item => item.id === agentId);
          if (selectedAgent) {
            this.form.icon = selectedAgent.icon_url || '';
            this.form.name = selectedAgent.name || '';
            this.form.des = selectedAgent.description || '';
          }
        }
      },
      /* 上传前验证 */
      beforeUpload(file) {
        const isImage = file.type.indexOf('image/') === 0;
        const isLt2M = file.size / 1024 / 1024 < 2;

        if (!isImage) {
          this.$message.error('只能上传图片文件!');
          return false;
        }
        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 2MB!');
          return false;
        }
        return true;
      },
      /* 自定义上传 */
      handleHttpRequest(option) {
        try {
          option.status = 'uploading';
          const formData = new FormData();
          formData.append('file', option.file);
          formData.append('type', 1);

          request({
            url: '/common/uploadFile',
            method: 'post',
            data: formData,
            onUploadProgress: (e) => {
              if (e.lengthComputable) {
                option.progress = e.loaded / e.total * 100;
              }
            }
          }).then((res) => {
            if (res.data.code === 0) {
              this.form.icon = res.data.data.url;
              this.$message.success('图片上传成功');
              option.status = 'success';
            } else {
              this.$message.error(res.data.message || '上传失败');
              option.status = 'exception';
            }
          }).catch((e) => {
            this.$message.error(e.message || '上传失败');
            option.status = 'exception';
          });
        } catch (error) {
          console.error(error);
          this.$message.error('上传失败');
          option.status = 'exception';
        }
      },
      /* 删除图片 */
      removePicture() {
        this.$confirm('确定要删除图片吗？', '提示', {
          type: 'warning'
        }).then(() => {
          this.form.icon = '';
          this.$message.success('图片已删除');
        }).catch(() => {});
      }
    }
  };
</script>

<style scoped>
  .upload-container {
    display: flex;
    align-items: flex-start;
    gap: 15px;
  }

  .avatar-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: border-color 0.3s;
  }

  .avatar-uploader:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
    display: block;
  }

  .avatar {
    width: 100px;
    height: 100px;
    display: block;
    object-fit: cover;
  }

  .upload-tips {
    flex: 1;
    color: #999;
    font-size: 12px;
    line-height: 1.5;
  }

  .upload-tips p {
    margin: 0 0 8px 0;
  }
</style>
