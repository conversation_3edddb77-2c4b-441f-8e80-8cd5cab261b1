<!-- 分类列表组件 -->
<template>
  <div>
    <!-- 操作按钮 -->
    <ele-toolbar class="ele-toolbar-actions">
      <div style="margin: 5px 0">
        <el-button
          size="small"
          type="primary"
          icon="el-icon-plus"
          class="ele-btn-icon"
          @click="openEdit()"
        >
          添加分类
        </el-button>
      </div>
    </ele-toolbar>
    
    <!-- 分类列表 -->
    <div class="ele-border-lighter category-list">
      <el-tree
        ref="tree"
        :data="categoryData"
        highlight-current
        node-key="id"
        :props="{ label: 'name' }"
        :expand-on-click-node="false"
        :default-expand-all="true"
        @node-click="onNodeClick"
      >
        <!-- 自定义节点内容 -->
        <template v-slot="{ node, data }">
          <div class="tree-node-content">
            <span class="node-label">{{ data.name }}</span>
            <div class="node-actions">
              <el-button
                type="text"
                size="mini"
                icon="el-icon-edit"
                @click.stop="openEdit(data)"
                title="修改"
              />
              <el-button
                type="text"
                size="mini"
                icon="el-icon-delete"
                @click.stop="remove(data)"
                title="删除"
                style="color: #f56c6c;"
              />
            </div>
          </div>
        </template>
      </el-tree>

      <!-- 空状态 -->
      <div v-if="!categoryData.length" class="empty-state">
        <el-empty description="暂无分类数据">
          <el-button type="primary" @click="openEdit()">添加分类</el-button>
        </el-empty>
      </div>
    </div>
    
    <!-- 编辑弹窗 -->
    <el-dialog
      :title="isUpdate ? '修改分类' : '添加分类'"
      :visible.sync="showEdit"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="分类名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入分类名称"
            clearable
          />
        </el-form-item>
      </el-form>
      
      <template v-slot:footer>
        <el-button @click="showEdit = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="save">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import {
    getVideoTypeList,
    addVideoType,
    updateVideoType,
    deleteVideoType
  } from '@/api/finishVideo';

  export default {
    name: 'CategoryList',
    data() {
      return {
        // 分类数据
        categoryData: [],
        // 当前选中的分类
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否为更新操作
        isUpdate: false,
        // 表单加载状态
        loading: false,
        // 表单数据
        form: {
          id: null,
          name: ''
        },
        // 表单验证规则
        rules: {
          name: [
            { required: true, message: '请输入分类名称', trigger: 'blur' },
            { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
          ]
        }
      };
    },
    mounted() {
      this.loadCategoryList();
    },
    methods: {
      /* 加载分类列表 */
      async loadCategoryList() {
        try {
          this.categoryData = await getVideoTypeList();
          // 如果有数据且没有选中项，默认选中第一个
          if (this.categoryData.length && !this.current) {
            this.current = this.categoryData[0];
            this.$emit('category-change', this.current);
          }
        } catch (error) {
          this.$message.error(error.message);
        }
      },
      /* 节点点击事件 */
      onNodeClick(data) {
        this.current = data;
        this.$emit('category-change', data);
      },
      /* 打开编辑弹窗 */
      openEdit(data) {
        this.isUpdate = !!data;
        if (data) {
          this.form = { ...data };
        } else {
          this.form = { id: null, name: '' };
        }
        this.showEdit = true;
      },
      /* 保存分类 */
      save() {
        this.$refs.form.validate(async (valid) => {
          if (!valid) return;

          this.loading = true;
          try {
            if (this.isUpdate) {
              // 调用修改分类接口
              await updateVideoType({
                id: this.form.id,
                name: this.form.name
              });
              this.$message.success('修改成功');
            } else {
              // 调用添加分类接口
              await addVideoType(this.form.name);
              this.$message.success('添加成功');
            }

            this.showEdit = false;
            this.loadCategoryList();
            this.$emit('category-updated');

          } catch (error) {
            this.$message.error(error.message);
          } finally {
            this.loading = false;
          }
        });
      },
      /* 删除分类 */
      remove(data) {
        const categoryToDelete = data || this.current;
        if (!categoryToDelete) return;

        this.$confirm(`确定要删除分类"${categoryToDelete.name}"吗？删除后该分类下的所有视频也将被删除！`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          try {
            // 调用删除分类接口
            await deleteVideoType(categoryToDelete.id);
            this.$message.success('删除成功');

            // 如果删除的是当前选中的分类，清空选中状态
            if (this.current && this.current.id === categoryToDelete.id) {
              this.current = null;
              this.$emit('category-change', null);
            }

            this.loadCategoryList();
            this.$emit('category-updated');
          } catch (error) {
            this.$message.error(error.message);
          }
        });
      },
      /* 刷新分类列表 */
      refresh() {
        this.loadCategoryList();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .category-list {
    height: calc(100vh - 320px);
    overflow-y: auto;
    padding: 10px;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    
    .empty-state {
      text-align: center;
      padding: 40px 20px;
    }
  }
  
  .ele-toolbar-actions {
    margin-bottom: 10px;
  }

  .tree-node-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 8px;

    .node-label {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .node-actions {
      display: flex;
      opacity: 0;
      transition: opacity 0.3s;

      .el-button {
        padding: 4px;
        margin-left: 4px;
      }
    }

    &:hover .node-actions {
      opacity: 1;
    }
  }

  // 树形组件样式优化
  :deep(.el-tree-node__content) {
    height: 32px;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  :deep(.el-tree-node__content.is-current) {
    background-color: #ecf5ff;
    color: #409eff;
  }
</style>
