<template>
  <div class="ele-body ele-body-card">
    <profile-card />
    <link-card ref="linkCard" />
    <statistics-card />
    <sale-card />
    
  </div>
</template>

<script>
  import ProfileCard from './components/profile-card.vue';
  import LinkCard from './components/link-card.vue';
  import StatisticsCard from './components/statistics-card.vue';
  import SaleCard from './components/sale-card.vue';


  export default {
    name: 'AgencyDashboardMonitor',
    components: {
      ProfileCard,
      LinkCard,
      StatisticsCard,
      SaleCard,
    },
    computed: {
      // 是否开启响应式布局
      styleResponsive() {
        return this.$store.state.theme.styleResponsive;
      }
    }
  };
</script>
