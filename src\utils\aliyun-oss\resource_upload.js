
accessid= document.getElementById('key_id').getAttribute('value');
accesskey= document.getElementById('key_secret').getAttribute('value');
host = document.getElementById('url').getAttribute('value');

g_dirname = '';
g_object_name = '';
g_object_name_type = '';
now = timestamp = Date.parse(new Date()) / 1000; 

var policyText = {
    "expiration": "3020-01-01T12:00:00.000Z", // 设置该Policy的失效时间，超过这个失效时间之后，就没有办法通过这个policy上传文件了
    "conditions": [
    ["content-length-range", 0, 104857600] // 设置上传文件的大小限制
    ]
};

var policyBase64 = Base64.encode(JSON.stringify(policyText))
message = policyBase64
var bytes = Crypto.HMAC(Crypto.SHA1, message, accesskey, { asBytes: true }) ;
var signature = Crypto.util.bytesToBase64(bytes);

function check_object_radio() {
    var tt = document.getElementsByName('myradio');
    for (var i = 0; i < tt.length ; i++ )
    {
        if(tt[i].checked)
        {
            g_object_name_type = tt[i].value;
            break;
        }
    }
}

function get_dirname()
{
    dir = document.getElementById("dirname").value;
    if (dir != '' && dir.indexOf('/') != dir.length - 1)
    {
        dir = dir + '/'
    }
    // alert(dir)
    g_dirname = dir
}

function random_string(len) {
　　len = len || 32;
　　var chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';   
　　var maxPos = chars.length;
　　var pwd = '';
　　for (i = 0; i < len; i++) {
    　　pwd += chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
}

function get_suffix(filename) {
    pos = filename.lastIndexOf('.')
    suffix = ''
    if (pos != -1) {
        suffix = filename.substring(pos)
    }
    return suffix;
}

function calculate_object_name(filename)
{
    if (g_object_name_type == 'local_name')
    {
        g_object_name += "${filename}"
    }
    else if (g_object_name_type == 'random_name')
    {
        suffix = get_suffix(filename)
        g_object_name = g_dirname + random_string(10) + suffix
    }
    return ''
}

function get_uploaded_object_name(filename)
{
    if (g_object_name_type == 'local_name')
    {
        tmp_name = g_object_name
        tmp_name = tmp_name.replace("${filename}", filename);
        return tmp_name
    }
    else if(g_object_name_type == 'random_name')
    {
        return g_object_name
    }
}

function set_upload_param(up, filename, ret)
{
    g_object_name = g_dirname;
    if (filename != '') {
        suffix = get_suffix(filename)
        calculate_object_name(filename)
    }
    new_multipart_params = {
        'key' : g_object_name,
        'policy': policyBase64,
        'OSSAccessKeyId': accessid, 
        'success_action_status' : '200', // 让服务端返回200,不然，默认会返回204
        'signature': signature,
    };

    up.setOption({
        'url': host,
        'multipart_params': new_multipart_params
    });

    up.start();
}

window.delete_div =function (div_id){
	div_id.remove();
}

var uploader = new plupload.Uploader({
	runtimes : 'html5,flash,silverlight,html4',
	browse_button : 'selectfiles', 
    multi_selection: true,
	container: document.getElementById('container'),
	flash_swf_url : '/static/plugs/aliyun-oss/plupload-2.1.2/Moxie.swf',
	silverlight_xap_url : '/static/plugs/aliyun-oss/plupload-2.1.2/Moxie.xap',
    url : 'http://oss.aliyuncs.com',
    loadIndex:null,
    filters: {
        mime_types : [ // 只允许上传图片和zip文件
        { title :document.getElementById('file_type').value , extensions : document.getElementById('extensions').value }
        ],
        max_file_size : '100mb', // 最大只能上传100mb的文件
        prevent_duplicates : false // 不允许选取重复文件
    },

	init: {
		PostInit: function() {
			document.getElementById('ossfile').innerHTML = '';
			document.getElementById('postfiles').onclick = function() {
	            set_upload_param(uploader, '', false);
	            if(uploader.total.queued>0){
	            	loadIndex = layer.msg('上传中，请稍候...', {icon: 16,time:-1});
	            }else if(uploader.total.queued==0){
	            	layer.msg("未选择需要上传的文件",{icon: 7,time:2000});
	            }
	            return false;
			};
		},
		FilesAdded: function(up, files) {
			plupload.each(files, function(file) {
				var file_type = document.getElementById('file_type').value;
				var data ={"file_name":file.name,"file_id":file.id,"resource_type":file_type,"resource_group_id":$("#resource_group_id").val(),"resource_status":2}
            	$.ajax({
	                url: "/bpdr.much_resource/add_resource",
	                type: 'POST',
	                dataType: 'json',
	                data:data,
	                success: function(datas) {
	                    var code = datas.code;
	                    if(code==0){
	                    	layui.table.reload("currentTableRenderId");
	                    }
	                }
	            });
				document.getElementById('ossfile').innerHTML += '<div style="margin-left: 55px;margin-top:15px;display: inline-block" id="' + file.id + '">'
				+'<b>文件原名：' + file.name +'</b><b></b><div class="progress" style="width:130px;"><div class="progress-bar" style="width: 0%"></div></div>'
				+'</div>';
			});
			layui.table.reload("currentTableRenderId");
		},

		BeforeUpload: function(up, file) {
            check_object_radio();
            set_upload_param(up, file.name, true);
        },

		UploadProgress: function(up, file) {
			var d = document.getElementById(file.id);
			if(d!=null){
				d.getElementsByTagName('b')[1].innerHTML = '<span>，上传进度' + file.percent + "%</span>";
	            var prog = d.getElementsByTagName('div')[0];
				var progBar = prog.getElementsByTagName('div')[0]
				progBar.style.width= file.percent+'%';
				progBar.setAttribute('aria-valuenow', file.percent+"%");
			}
		},

		FileUploaded: function(up, file, info) {
            if (info.status == 200)
            {
            	var url =host+'/'+get_uploaded_object_name(file.name);
            	var audio = document.createElement("audio");
       		    audio.src =  url
       		    audio.addEventListener("loadedmetadata", function() {
       			    var text_duration = parseInt(this.duration);     // 获取总时长
       			    var file_type = document.getElementById('file_type').value;
           			var data ={"file_id":file.id,"resource_url":url,"resource_status":1,"resource_duration":text_duration,"resource_type":file_type,"resource_group_id":$("#resource_group_id").val(),"file_name":file.name}
                	$.ajax({
    	                url: "/bpdr.much_resource/add_resource",
    	                type: 'POST',
    	                dataType: 'json',
    	                data:data,
    	                
    	                success: function(datas) {
    	                	var code = datas.code;
    	                	if(code == 0){
    	                		layui.table.reload("currentTableRenderId");
    	                	}
    	                }
    	            });
           		});
            }
            else if (info.status == 203)
            {
            	var d = document.getElementById(file.id);
                if(d!=null){
                	d.getElementsByTagName('b')[1].innerHTML = '上传到OSS成功，但是oss访问用户设置的上传回调服务器失败，失败原因是:' + info.response;
                }
                var url =host+'/'+get_uploaded_object_name(file.name);
            	d.getElementsByTagName('b')[1].innerHTML = "";
            	var audio = document.createElement("audio");
       		    audio.src =  url
       		    audio.addEventListener("loadedmetadata", function() {
       			    var text_duration = parseInt(this.duration);     // 获取总时长
       			    var file_type = document.getElementById('file_type').value;
           			var data ={"file_id":file.id,"resource_url":url,"resource_status":1,"resource_duration":text_duration,"resource_type":file_type,"resource_group_id":$("#resource_group_id").val(),"file_name":file.name}
                	$.ajax({
    	                url: "/bpdr.much_resource/add_resource",
    	                type: 'POST',
    	                dataType: 'json',
    	                data:data,
    	                success: function(datas) {
    	                	var code = datas.code;
    	                	if(code == 0){
    	                		layui.table.reload("currentTableRenderId");
    	                	}
    	                }
    	            });
           		});
            }
            else
            {
            	var d = document.getElementById(file.id);
                if(d!=null){
                	document.getElementById(file.id).getElementsByTagName('b')[1].innerHTML = info.response;
                }
            } 
            layui.table.reload("currentTableRenderId");
            if(up.total.queued==0){
            	layer.close(loadIndex);
            }
		},

		Error: function(up, err) {
            if (err.code == -600) {
                document.getElementById('console').appendChild(document.createTextNode("\n选择的文件太大了,请上传100M以内的视频"));
            }
            else if (err.code == -601) {
                document.getElementById('console').appendChild(document.createTextNode("\n选择的文件后缀不对"));
            }
            else if (err.code == -602) {
                document.getElementById('console').appendChild(document.createTextNode("\n这个文件已经上传过一遍了"));
            }
            else 
            {
                document.getElementById('console').appendChild(document.createTextNode("\nError xml:" + err.response));
            }
            var file_type = document.getElementById('file_type').value;
            var data ={"file_id":file.id,"resource_url":url,"resource_status":3,"resource_type":file_type,"resource_group_id":$("#resource_group_id").val()}
        	$.ajax({
                url: "/bpdr.much_resource/add_resource",
                type: 'POST',
                dataType: 'json',
                data:data,
                success: function(datas) {
                	var code = datas.code;
                }
            });
            if(up.total.queued==0){
            	layer.close(loadIndex);
            }
		}
	}
});

uploader.init();
