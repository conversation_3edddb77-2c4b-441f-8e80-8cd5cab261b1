import axios from '@/utils/request';


/**
 * 查询回收分类排行
 * @param params 查询条件
 */
 export async function getReserveCategoryRanking() {
  const res = await axios.get('/recycle.datamonitor/getReserveCategoryRanking');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 查询平台统计数据
 * @param params 查询条件
 */
 export async function getStatisInfo() {
  const res = await axios.get('/recycle.datamonitor/getStatisInfo');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 查询近七天回收订单
 * @param params 查询条件
 */
 export async function getReserveOrderByDay() {
  const res = await axios.get('/recycle.datamonitor/getReserveOrderByDay');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 查询近一年回收订单
 * @param params 查询条件
 */
 export async function getReserveOrderByMonth() {
  const res = await axios.get('/recycle.datamonitor/getReserveOrderByMonth');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 查询回收员订单数据
 * @param params 查询条件
 */
 export async function getCollectorOrderRanking() {
  const res = await axios.get('/recycle.datamonitor/getCollectorOrderRanking');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询商城订单交易
 * @param params 查询条件
 */
 export async function getOrderByDay() {
  const res = await axios.get('/recycle.datamonitor/getOrderByDay');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询回收站出库入库记录
 * @param params 查询条件
 */
 export async function getStockLog() {
  const res = await axios.get('/recycle.datamonitor/getStockLog');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询打包站汇总数据
 * @param params 查询条件
 */
 export async function getBalanceData() {
  const res = await axios.get('/recycle.datamonitor/getBalanceData');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}





