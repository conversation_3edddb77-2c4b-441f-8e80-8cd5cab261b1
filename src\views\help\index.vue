<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            新增
          </el-button> 
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            class="ele-btn-icon"
            @click="removeBatch"
          >
            删除
          </el-button>
         
        </template>


      
 <template v-slot:introduce_url="{ row }">
        
          <video v-if="row.introduce_url && !isImageFile(row.introduce_url)" width="300" height="240" controls :src="row.introduce_url"  > </video>
          <el-avatar v-else-if="row.introduce_url && isImageFile(row.introduce_url)" :src="row.introduce_url" shape="square" :size="80" />
     
          
        </template>


       

        <!-- 状态列 -->
        <template v-slot:is_display="{ row }">
          <el-switch
            :active-value="1"
            :inactive-value="2"
            v-model="row.is_display"
            @change="editStatus(row)"
          />
        </template>


        <template v-slot:sort>
          <i class="sort-handle el-icon-rank ele-text-secondary" style="cursor: move;"></i>
        </template>

        
        <!-- 操作列 -->
        <template v-slot:action="{ row }">
          <el-link
            type="primary"
            :underline="false"
            icon="el-icon-edit"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-popconfirm
            class="ele-action"
            title="确定要删除此数据吗？"
            @confirm="remove(row)"
          >
            <template v-slot:reference>
              <el-link type="danger" :underline="false" icon="el-icon-delete">
                删除
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
    <!-- 编辑弹窗 -->
       <help-edit :visible.sync="showEdit" :data="current"  @done="reload"/>

  </div>
</template>

<script>
  import HelpSearch from './components/help-search';
  import HelpEdit from './components/help-edit';
  import { list, remove, modify,sortChange  } from '@/api/help';
  import Sortable from 'sortablejs';

  export default {
    name: 'Help',
    components: {
      HelpSearch,
     HelpEdit
    },
    data() {
      return {
      
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },

           {
            prop: 'title',
            label: '标题',
            sortable: 'custom',
            showOverflowTooltip: true,
            minWidth: 60,
           
          },
             {
            prop: 'desc',
            label: '描述',
            sortable: 'custom',
            showOverflowTooltip: true,
            minWidth: 60,
           
          }, {
            prop: 'introduce_url',
            label: '视频介绍',
            showOverflowTooltip: true,
            minWidth: 220,
            slot: 'introduce_url'
          },
          {
            prop: 'sort',
            label: '排序',
            sortable: 'custom',
            showOverflowTooltip: true,
            minWidth: 60,
            slot: 'sort'
          },
         
       
        
          {
            prop: 'create_time',
            label: '创建时间',
            showOverflowTooltip: true,
            minWidth: 100,
            formatter: (row, column, cellValue) => {
              return this.$util.toDateString(cellValue);
            }
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 350,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            showOverflowTooltip: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示分配回收员
        showCollector: false,
        //弹框名称
        id:0,
      };
    },
    methods: {
         isImageFile(url){
        // 获取文件扩展名
        const extension = url.substring(url.lastIndexOf('.') + 1).toLowerCase();
        // 支持的图片扩展名列表
        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];
        // 判断当前文件扩展名是否为图片扩展名
        if (imageExtensions.includes(extension)) {
          return true;
        }
        return false;
      },
      helpList() {
        const tbody = document.querySelector('.el-table__body-wrapper tbody')
        const _this = this
        Sortable.create(tbody, {
          forceFallback: true,
          dragClass: 'drag',
          delay:100,
          animation: 1000,
          onEnd({ newIndex, oldIndex }) {
        
            //更换表格数据 后续拖动坐标才会正确
            let data = _this.$refs.table.getData();
             
            let nid = data[newIndex].id;
            let oid = data[oldIndex].id;

           
            
            const currRow = data.splice(oldIndex, 1)[0];
            data.splice(newIndex, 0, currRow);

            sortChange(data).then((msg) => {
              
            }).catch((e) => {
              _this.$refs.table.reload();
            });
            //去数据库更换排序
    
          }
        })
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        where.b_type = this.b_type;
        return list({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        where = where ? where : {};
        where.b_type = this.b_type;
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id).then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
      },

     
      
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的数据吗?', '提示', {
          type: 'warning'
        }).then(() => {
          const loading = this.$loading({ lock: true });
          remove(this.selection.map((d) => d.id))
            .then((msg) => {
              loading.close();
              this.$message.success(msg);
              this.reload();
            })
            .catch((e) => {
              loading.close();
              this.$message.error(e.message);
            });
        }).catch(() => {});
      },
      
      /* 更改状态 */
      editStatus(row) {
        const loading = this.$loading({ lock: true });
        modify({id:row.id,field:'is_display',value: row.is_display})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
          })
          .catch((e) => {
            loading.close();
            row.status = !row.status ? 1 : 0;
            this.$message.error(e.message);
          });
      }
    },mounted() {
      this.helpList()
      //this.columnDrop()
    },
    watch: {
      $route: {
        handler(route) {
          this.b_type = route.meta.type;
        },
        immediate: true
      },
      b_type(){
        this.reload();
      }
    },
  };
</script>

<style>
 .drag {
    background: #000 !important;
    background-image: linear-gradient(#333, #999) !important;
  }
</style>
