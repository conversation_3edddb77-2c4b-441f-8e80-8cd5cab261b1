<!-- 视频上传组件 -->
<template>
  <ele-modal
    width="460px"
    :visible="visible"
    :append-to-body="true"
    :close-on-click-modal="true"
    custom-class="ele-dialog-form"
    title="上传视频"
    @update:visible="updateVisible"
  >
    <!-- 视频信息表单 -->
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="视频标题" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入视频标题"
          clearable
          maxlength="20"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="选择分类" prop="typeId">
        <el-select
          v-model="form.typeId"
          placeholder="请选择现有分类或输入新分类名称"
          class="ele-fluid"
          filterable
          allow-create
          default-first-option
          :filter-method="filterMethod"
          @change="onCategoryChange"
        >
          <el-option
            v-for="item in categoryList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
        <!-- 新建分类提示 -->
        <div v-if="isNewCategory && newCategoryName" class="category-tip">
          <el-alert
            :title="`将创建新分类: ${newCategoryName}`"
            type="info"
            :closable="false"
            show-icon
            style="margin-top: 8px;"
          />
        </div>
      </el-form-item>

      <!-- 视频封面上传 -->
      <el-form-item label="视频封面">
        <div class="cover-upload-container">
          <!-- 有封面时显示封面，点击可替换 -->
          <div v-if="coverFileList.length > 0" class="cover-preview">
            <div class="cover-image-wrapper" @click="replaceCover">
              <el-image
                :src="coverFileList[0].url"
                fit="cover"
                class="cover-image"
              />
              <div class="cover-overlay">
                <i class="el-icon-edit"></i>
                <span>点击替换</span>
              </div>
            </div>
            <div class="cover-actions">
              <el-button size="mini" type="text" @click="replaceCover">
                <i class="el-icon-edit"></i> 替换封面
              </el-button>
              <el-button size="mini" type="text" @click="removeCover">
                <i class="el-icon-delete"></i> 移除封面
              </el-button>
            </div>
          </div>

          <!-- 没有封面时显示上传区域 -->
          <el-upload
            v-else
            ref="coverUpload"
            :disabled="loading"
            :file-list="[]"
            :on-change="onCoverUpload"
            :auto-upload="false"
            :before-upload="beforeCoverUpload"
            :on-exceed="handleCoverExceed"
            :limit="1"
            accept=".jpg,.jpeg,.png,.gif"
            class="cover-upload"
            list-type="picture-card"
          >
            <i class="el-icon-plus"></i>
          </el-upload>

          <!-- 隐藏的文件输入，用于替换封面 -->
          <input
            ref="hiddenFileInput"
            type="file"
            accept=".jpg,.jpeg,.png,.gif"
            style="display: none"
            @change="onFileInputChange"
          />

          <div class="upload-tip">
            可选上传自定义封面，支持 jpg、png、gif 格式，文件大小不超过 5MB<br>
            <span style="color: #909399; font-size: 11px;">如不上传，将自动截取视频第一帧并上传作为封面</span>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <el-upload
      ref="upload"
      :disabled="loading"
      :file-list="fileList"
      :on-change="onUpload"
      :auto-upload="false"
      :before-upload="beforeVideoUpload"
      :on-exceed="handleExceed"
      :limit="1"
      :on-remove="onRemove"
      accept=".mp4,.3gp,.mov"
      class="upload-demo"
      drag
      multiple
    >
      <i class="el-icon-upload" v-show="!loading"></i>
      <el-image
        v-show="loading"
        style="width: 67px; margin: 40px 0 16px; line-height: 50px;"
        :src="require('@/assets/loading.gif')"
      ></el-image>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <div class="el-upload__tip" slot="tip">
        只能上传.mp4,.3gp,.mov文件，且文件大小不超过100MB
      </div>
    </el-upload>
    
    <template v-slot:footer>
      <el-button size="small" @click="updateVisible(false)">
        取消上传
      </el-button>
      <el-button
        :loading="loading"
        size="small"
        type="primary"
        @click="startUpload"
      >
        开始上传
      </el-button>
    </template>
  </ele-modal>
</template>

<script>
  import { uploadVideoFile, uploadImageFile, addFinishVideo, getVideoTypeList, addVideoType } from '@/api/finishVideo';

  export default {
    name: 'VideoUpload',
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 当前选中的分类ID
      typeId: {
        type: Number,
        default: null
      }
    },
    data() {
      return {
        loading: false,
        fileList: [],
        coverFileList: [],
        categoryList: [],
        form: {
          name: '',
          typeId: null,
          cover: ''
        },
        // 是否为新建分类
        isNewCategory: false,
        // 新分类名称
        newCategoryName: '',
        rules: {
          name: [
            { required: true, message: '请输入视频标题', trigger: 'blur' },
            { min: 1, max: 20, message: '标题长度在 1 到 20 个字符', trigger: 'blur' }
          ],
          typeId: [
            { required: true, message: '请选择视频分类', trigger: 'change' }
          ]
        }
      };
    },
    watch: {
      visible(val) {
        if (val) {
          this.loadCategoryList();
          // 如果传入了typeId，自动选中
          if (this.typeId) {
            this.form.typeId = this.typeId;
          }
        }
      }
    },
    methods: {
      /* 加载分类列表 */
      async loadCategoryList() {
        try {
          this.categoryList = await getVideoTypeList();
        } catch (error) {
          this.$message.error(error.message);
        }
      },
      /* 更新弹窗显示状态 */
      updateVisible(value) {
        this.$emit('update:visible', value);
        if (!value) {
          this.reset();
        }
      },
      /* 分类选择变化处理 */
      onCategoryChange(value) {
        // 检查是否为新建分类（如果value是字符串且不在现有分类列表中）
        if (typeof value === 'string') {
          const existingCategory = this.categoryList.find(item => item.name === value);
          if (!existingCategory) {
            this.isNewCategory = true;
            this.newCategoryName = value;
          } else {
            this.isNewCategory = false;
            this.newCategoryName = '';
            this.form.typeId = existingCategory.id;
          }
        } else {
          // 选择的是现有分类ID
          this.isNewCategory = false;
          this.newCategoryName = '';
        }
      },
      /* 分类过滤方法 */
      filterMethod(query) {
        // 允许所有输入，el-select会自动处理过滤
        return true;
      },
      /* 文件选择变化 */
      onUpload(file, fileList) {
        this.fileList = fileList;
      },
      /* 文件移除 */
      onRemove(file, fileList) {
        this.fileList = fileList;
      },
      /* 封面文件选择变化 */
      onCoverUpload(file, fileList) {
        this.coverFileList = fileList;
      },
      /* 封面文件移除 */
      onCoverRemove(file, fileList) {
        this.coverFileList = fileList;
      },
      /* 替换封面 */
      replaceCover() {
        this.$refs.hiddenFileInput.click();
      },
      /* 移除封面 */
      removeCover() {
        this.coverFileList = [];
        this.$refs.coverUpload && this.$refs.coverUpload.clearFiles();
      },
      /* 隐藏文件输入变化 */
      onFileInputChange(event) {
        const file = event.target.files[0];
        if (file) {
          // 验证文件
          if (!this.beforeCoverUpload(file)) {
            return;
          }

          // 创建文件对象
          const fileObj = {
            name: file.name,
            size: file.size,
            type: file.type,
            uid: Date.now(),
            raw: file,
            url: URL.createObjectURL(file)
          };

          // 更新文件列表
          this.coverFileList = [fileObj];
        }

        // 清空input值，允许重复选择同一文件
        event.target.value = '';
      },
      /* 封面文件超出限制 */
      handleCoverExceed(files, fileList) {
        this.$message.warning('只能上传一张封面图片');
      },
      /* 封面上传前验证 */
      beforeCoverUpload(file) {
        // 验证文件类型
        const isImage = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'].includes(file.type);
        if (!isImage) {
          this.$message.error('请上传正确的图片格式（jpg、png、gif）');
          return false;
        }

        // 验证文件大小（5MB）
        const isLt5M = file.size / 1024 / 1024 < 5;
        if (!isLt5M) {
          this.$message.error('上传图片大小不能超过 5MB');
          return false;
        }

        return true;
      },
      /* 文件超出限制 */
      handleExceed(files, fileList) {
        this.$message.warning('只能上传一个视频文件');
      },
      /* 上传前验证 */
      beforeVideoUpload(file) {
        // 验证文件类型
        const isVideo = ['video/mp4', 'video/3gp', 'video/quicktime'].includes(file.type);
        if (!isVideo) {
          this.$message.error('请上传正确的视频格式（mp4、3gp、mov）');
          return false;
        }
        
        // 验证文件大小（100MB）
        const isLt100M = file.size / 1024 / 1024 < 100;
        if (!isLt100M) {
          this.$message.error('上传视频大小不能超过 100MB');
          return false;
        }
        
        return true;
      },
      /* 开始上传 */
      async startUpload() {
        // 验证表单
        const valid = await this.$refs.form.validate().catch(() => false);
        if (!valid) return;

        if (!this.fileList.length) {
          this.$message.warning('请选择要上传的视频文件');
          return;
        }

        this.loading = true;

        try {
          let categoryId = this.form.typeId;

          // 1. 如果是新建分类，先创建分类
          if (this.isNewCategory && this.newCategoryName) {
            this.$message.info('正在创建新分类...');
            const createResult = await addVideoType(this.newCategoryName);

            // 重新加载分类列表获取新分类的ID
            await this.loadCategoryList();
            const newCategory = this.categoryList.find(item => item.name === this.newCategoryName);
            if (newCategory) {
              categoryId = newCategory.id;
              this.$message.success('分类创建成功');
            } else {
              throw new Error('创建分类成功但无法获取分类ID');
            }
          }

          // 2. 上传视频文件获取URL
          this.$message.info('正在上传视频文件...');
          const file = this.fileList[0].raw;
          const url = await uploadVideoFile(file);

          // 3. 处理封面图片
          let coverUrl = '';
          if (this.coverFileList.length > 0) {
            // 用户上传了自定义封面
            this.$message.info('正在上传封面图片...');
            const coverFile = this.coverFileList[0].raw;
            coverUrl = await uploadImageFile(coverFile);
          } else {
            // 没有自定义封面，截取视频第一帧
            this.$message.info('正在生成视频封面...');
            coverUrl = await this.captureVideoFrame(url);
          }

          // 4. 添加视频记录
          this.$message.info('正在保存视频信息...');
          await addFinishVideo({
            url: url,
            type_id: categoryId,
            name: this.form.name,
            cover: coverUrl
          });

          this.$message.success('视频上传成功');
          this.$emit('done');
          this.updateVisible(false);

        } catch (error) {
          this.$message.error(error.message);
        } finally {
          this.loading = false;
        }
      },
      /* 截取视频第一帧 */
      async captureVideoFrame(videoUrl) {
        return new Promise((resolve, reject) => {
          const video = document.createElement('video');
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          video.crossOrigin = 'anonymous';
          video.currentTime = 1; // 截取第1秒的画面

          video.onloadedmetadata = () => {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
          };

          video.onseeked = async () => {
            try {
              // 绘制视频帧到canvas
              ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

              // 将canvas转换为blob
              canvas.toBlob(async (blob) => {
                if (blob) {
                  try {
                    // 上传截取的图片
                    const coverUrl = await uploadImageFile(blob);
                    resolve(coverUrl);
                  } catch (error) {
                    reject(error);
                  }
                } else {
                  reject(new Error('截取视频帧失败'));
                }
              }, 'image/jpeg', 0.8);
            } catch (error) {
              reject(error);
            }
          };

          video.onerror = () => {
            reject(new Error('视频加载失败'));
          };

          video.src = videoUrl;
          video.load();
        });
      },
      /* 重置表单 */
      reset() {
        this.fileList = [];
        this.coverFileList = [];
        this.loading = false;
        this.isNewCategory = false;
        this.newCategoryName = '';
        this.form = {
          name: '',
          typeId: this.typeId || null,
          cover: ''
        };
        this.$refs.upload && this.$refs.upload.clearFiles();
        this.$refs.coverUpload && this.$refs.coverUpload.clearFiles();
        this.$refs.form && this.$refs.form.resetFields();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .upload-demo {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .el-upload:hover {
      border-color: #409EFF;
    }
  }

  .category-tip {
    :deep(.el-alert) {
      padding: 8px 12px;

      .el-alert__icon {
        font-size: 14px;
      }

      .el-alert__title {
        font-size: 12px;
        line-height: 1.4;
      }
    }
  }

  .cover-upload-container {
    .cover-upload {
      :deep(.el-upload) {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        width: 135px;
        height: 240px;

        &:hover {
          border-color: #409EFF;
        }

        .el-icon-plus {
          font-size: 28px;
          color: #8c939d;
          width: 135px;
          height: 240px;
          line-height: 240px;
          text-align: center;
        }
      }

      :deep(.el-upload-list__item) {
        width: 135px;
        height: 240px;
      }
    }

    .cover-preview {
      display: inline-block;

      .cover-image-wrapper {
        position: relative;
        width: 135px;
        height: 240px;
        border-radius: 6px;
        overflow: hidden;
        cursor: pointer;
        border: 1px solid #dcdfe6;

        &:hover {
          .cover-overlay {
            opacity: 1;
          }
        }

        .cover-image {
          width: 100%;
          height: 100%;
        }

        .cover-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.6);
          color: white;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s;

          i {
            font-size: 20px;
            margin-bottom: 4px;
          }

          span {
            font-size: 12px;
          }
        }
      }

      .cover-actions {
        margin-top: 8px;
        text-align: center;

        .el-button {
          margin: 0 4px;
          padding: 4px 8px;
        }
      }
    }

    .upload-tip {
      font-size: 12px;
      color: #999;
      margin-top: 8px;
      line-height: 1.4;
    }
  }
</style>
