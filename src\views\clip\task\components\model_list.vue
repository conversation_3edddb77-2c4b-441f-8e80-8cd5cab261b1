<template>
  <div class="ele-body">
    <el-card shadow="never">
  
      <!-- 搜索表单 -->
      
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
    
      

        <template v-slot:video="{ row }">

          <img
                  style="width: 200px; height: 150px"
                  v-if="row.url"
                  :src="row.url"
                  alt=""
          />
        </template>

      
       


        <!-- 操作列 -->

      </ele-pro-table>
    </el-card>
  </div>
</template>

<script>
  

  import { model } from '@/api/clip';

  export default {
    name: 'model',
    components: {
    
    },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      videoId: {
        type: Number,
        default: 0
      },
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },

          {
            prop: 'url',
            label: '模版',
             align: 'center',
            showOverflowTooltip: true,
         //   minWidth: 110,
            slot: 'video'
            
          },
           
          
          
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示分配回收员
        showCollector: false,
        //弹框名称
        id:0,
        dyPlay:0,
        dyLike:0,
        dyComment:0,
        ksPlay:0,
        ksLike:0,
        ksComment:0,
      };
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        where.id = this.videoId;
       
        return model({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id).then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
      },
    
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的数据吗?', '提示', {
          type: 'warning'
        }).then(() => {
          const loading = this.$loading({ lock: true });
          remove(this.selection.map((d) => d.id))
            .then((msg) => {
              loading.close();
              this.$message.success(msg);
              this.reload();
            })
            .catch((e) => {
              loading.close();
              this.$message.error(e.message);
            });
        }).catch(() => {});
      },
      
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
    },
    mounted() {
 
    },
    watch: {
      videoId(){
    
        this.reload();
      
      }
    },
  };
</script>
