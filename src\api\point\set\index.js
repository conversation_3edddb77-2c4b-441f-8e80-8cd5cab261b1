import axios from '@/utils/request';


/**
 * 查询代理系统首页配置
 * @param params 查询条件
 */
 export async function query(params) {
    const res = await axios.get('/figure.pointSet/query', {
      params
    });
    if (res.data.code === 0) {
      return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
  }
  
  
  /**
   * 保存代理系统首页配置信息
   * @param data 配置数据
   */
  export async function save(data) {
      const res = await axios.post('/figure.pointSet/save', data);
      if (res.data.code === 0) {
          return res.data.message;
      }
      return Promise.reject(new Error(res.data.message));
  }