<template>
  <div class="ele-body" >
    <el-card shadow="never">


      <el-tabs v-model="active" class="user-info-tabs"   @tab-click="reload()">
        <el-tab-pane label="一级团队" name="first">
        </el-tab-pane>
        <el-tab-pane label="二级团队" name="second">
        </el-tab-pane>
      </el-tabs>
      <!-- 数据表格 -->

      <team-search @search="reload" />

     
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >

       

        <template v-slot:nickname="{ row }">
          <div class="ele-cell">
            <el-avatar v-if="row.avatar" :src="row.avatar" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.nickname }} </div>
              <div class="ele-cell-desc">{{ row.telphone }} </div>
            </div>
          </div>
        </template>

        <template v-slot:qrcode="{ row }">
              <el-image
            style="width: 70px; height: 70px"
            :src="row.qrcode"
            fit="fit">
            
            </el-image>
            <el-link
            type="primary"
            style="margin-left: 5px;"
            :underline="false"
            @click="reloadQrcode(row)"
          >
            重新生成
          </el-link>
        </template>

        
        <!-- 操作列 -->
        <template v-slot:action="{ row }">
          <el-link
            type="success"
            :underline="false"
            icon="el-icon-time"
            @click="openLog(row)"
          >
            佣金记录
          </el-link>
        </template>
      </ele-pro-table>
    </el-card>

    <ele-modal 
      width="80%"
      :close-on-click-modal="false"
      custom-class="ele-dialog-form"
      :title=" userName + ' 佣金记录'"
      :append-to-body="true"
      :visible.sync="showLog">
      <user-brokerage-log :data="current" :pid="pid" :uid="id"  @done="reload"/>
    </ele-modal>

  </div>
</template>

<script>
  import TeamSearch from './team-search';
  import UserBrokerageLog from '@/views/brokerage/log/index';
  import { teamList} from '@/api/user/list';

  export default {
    name: 'UserTeam',
    components: { TeamSearch,UserBrokerageLog },
    props:{
      pid: Number,
    },
    data() {
      return {
        active: 'first',
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            align: 'center',
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'nickname',
            label: '昵称',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'nickname',
          },
          
          {
            prop: 'money',
            label: '佣金',
            showOverflowTooltip: true,
            minWidth: 110
          },
          
          {
            prop: 'create_time',
            label: '入驻时间',
            showOverflowTooltip: true,
            minWidth: 100,
            formatter: (row, column, cellValue) => {
              return this.$util.toDateString(cellValue);
            }
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 350,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            showOverflowTooltip: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示记录弹窗
        showLog: false,
        //弹框名称
        id:0,
        userName:''
      };
    },
    methods: {

      /* 表格数据源 */
      datasource({ page, limit, where, order }) {

        let type = this.active == 'first' ? 1:2;

        where.type = type;
        where.pid = this.pid;
        return teamList({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        where = !where ? {} : where;
        let type = this.active == 'first' ? 1:2;
        where.type = type;
        where.pid = this.pid;
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      /* 打开记录弹窗 */
      openLog(row) {
        this.id = row.id;
        this.current = row;
        this.showLog = true;
        this.userName = row.nickname;
      },
     
    },
    watch:{
      pid(){
        this.reload();
      }
    }
  };
</script>

<style lang="scss" scoped>

  .ele-cell-content {
   
   overflow: hidden;
   display: -webkit-box;
   -webkit-line-clamp: 2;
   -webkit-box-orient: vertical;
 }
 
  .ele-body {
    padding-bottom: 0;
  }

  .el-card {
    margin-bottom: 15px;
  }

  /* 用户资料卡片 */
  .user-info-card {
    padding: 8px 0;
    text-align: center;

    .user-info-avatar-group {
      position: relative;
      cursor: pointer;
      margin: 0 auto;
      width: 110px;
      height: 110px;
      border-radius: 50%;
      overflow: hidden;

      & > i {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #fff;
        font-size: 30px;
        display: none;
        z-index: 2;
      }

      &:hover {
        & > i {
          display: block;
        }

        &:after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.3);
        }
      }
    }

    .user-info-avatar {
      width: 110px;
      height: 110px;
      border-radius: 50%;
      object-fit: cover;
    }

    .user-info-name {
      font-size: 24px;
      margin-top: 20px;
    }

    .user-info-desc {
      margin-top: 8px;
    }
  }

  /* 用户信息列表 */
  .user-info-list {
    margin-top: 30px;

    .user-info-item {
      margin-bottom: 16px;
      display: flex;
      align-items: baseline;

      & > i {
        margin-right: 10px;
        font-size: 16px;
      }

      & > span {
        flex: 1;
        display: block;
      }
    }
  }

  /* 用户标签 */
  .user-info-tags .el-tag {
    margin: 10px 10px 0 0;
  }

  /* 用户账号绑定列表 */
  .user-account-list {
    padding: 16px 20px;

    .user-account-item {
      padding: 15px;

      .ele-text-secondary {
        margin-top: 6px;
      }

      .user-account-icon {
        width: 42px;
        height: 42px;
        line-height: 42px;
        text-align: center;
        color: #fff;
        font-size: 26px;
        border-radius: 50%;
        background-color: #3492ed;
        box-sizing: border-box;

        &.el-icon-_wechat {
          background-color: #4daf29;
          font-size: 28px;
        }

        &.el-icon-_alipay {
          background-color: #1476fe;
          padding-left: 5px;
          font-size: 32px;
        }
      }
    }
  }

  /* tab 页签 */
  .user-info-tabs {
    margin-bottom: 25px;

    :deep(.el-tabs__nav-wrap) {
      padding-left: 24px;
    }

    :deep(.el-tabs__item) {
      height: 50px;
      line-height: 50px;
      font-size: 15px;
    }
  }
</style>
