import axios from '@/utils/request';

/**
 * 一级分类
 * @param params 查询条件
 */
export async function list(params) {
 
  const res = await axios.get('/figure.videoRepost/index', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 删除
 * @param id id
 */
export async function remove(id) {
  const res = await axios.get('/figure.videoRepost/delete?id=' + id);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询统计首页数据
 * @param params 查询条件
 */
 export async function statis(params) {
  const res = await axios.get('/figure.videoRepost/statis',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询统计首页数据
 * @param params 查询条件
 */
 export async function getRepostData(params) {
  const res = await axios.get('/figure.videoRepost/repostData',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询统计首页数据
 * @param params 查询条件
 */
 export async function getWorkStatis(params) {
  const res = await axios.get('/figure.videoRepost/workStatis',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

