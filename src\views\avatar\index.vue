<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <log-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            class="ele-btn-icon"
            @click="removeBatch"
          >
            删除
          </el-button>
        </template>

        <template v-slot:user="{ row }">
          <div class="ele-cell" v-if="row.figureUser">
            <el-avatar
              v-if="row.figureUser.avatar"
              :src="row.figureUser.avatar"
              shape="square"
              :size="40"
            />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.figureUser.nickname }} </div>
              <div class="ele-cell-desc">{{ row.figureUser.telphone }} </div>
            </div>
          </div>
          <span v-else>无</span>
        </template>

        <template v-slot:a_type="{ row }">
          <el-tag v-if="row.a_type == 1" type="success" effect="plain"
            >形象克隆</el-tag
          >
          <el-tag v-if="row.a_type == 2" type="success" effect="plain"
            >照片克隆</el-tag
          >
        </template>

        <template v-slot:type="{ row }">
          <el-tag v-if="row.type == 1" type="success" effect="plain"></el-tag>
          <el-tag v-if="row.type == 2" type="primary" effect="plain"
            >高级模式</el-tag
          >
        </template>

        <template v-slot:video_url="{ row }">
          <video
            v-if="row.video_url && !isImageFile(row.video_url)"
            width="300"
            height="240"
            controls
            :src="row.video_url"
          >
          </video>
          <el-avatar
            v-else-if="row.video_url && isImageFile(row.video_url)"
            :src="row.video_url"
            shape="square"
            :size="80"
          />
          <el-avatar
            v-else-if="row.image_url && isImageFile(row.image_url)"
            :src="row.image_url"
            shape="square"
            :size="80"
          />
        </template>

        <template v-slot:current_status="{ row }">
          <el-tag v-if="row.current_status == 'completed'" type="success"
            >线路一成功</el-tag
          >
          <el-tag
            v-else-if="
              row.current_status == 'sent' ||
              row.current_status == 'pending' ||
              row.current_status == 'initialized' ||
              row.current_status == 'processing'
            "
            >线路一生成中</el-tag
          >
          <el-tag v-else-if="row.current_status == 'failed'" type="danger"
            >线路一失败</el-tag
          >
          <el-tag v-else-if="row.current_status == 'nothing'" type="danger"
            >线路一未训练</el-tag
          >

          <el-tag v-if="row.new_current_status == 'completed'" type="success"
            >线路二成功</el-tag
          >
          <el-tag
            v-else-if="
              row.new_current_status == 'sent' ||
              row.new_current_status == 'pending' ||
              row.new_current_status == 'initialized' ||
              row.new_current_status == 'processing'
            "
            >线路二生成中</el-tag
          >
          <el-tag v-else-if="row.new_current_status == 'failed'" type="danger"
            >线路二失败</el-tag
          >
          <el-tag v-else-if="row.new_current_status == 'nothing'" type="danger"
            >线路二未训练</el-tag
          >

          <el-tag
            v-if="row.composite_current_status == 'completed'"
            type="success"
            >线路三成功</el-tag
          >
          <el-tag
            v-else-if="
              row.composite_current_status == 'sent' ||
              row.composite_current_status == 'pending' ||
              row.composite_current_status == 'initialized' ||
              row.composite_current_status == 'processing'
            "
            >线路三生成中</el-tag
          >
          <el-tag
            v-else-if="row.composite_current_status == 'failed'"
            type="danger"
            >线路三失败</el-tag
          >
          <el-tag
            v-else-if="row.composite_current_status == 'nothing'"
            type="danger"
            >线路三未训练</el-tag
          >
  <el-tag v-if="row.four_current_status == 'completed'" type="success"
            >线路四成功</el-tag
          >
          <el-tag
            v-else-if="
              row.four_current_status == 'sent' ||
              row.four_current_status == 'pending' ||
              row.four_current_status == 'initialized' ||
              row.four_current_status == 'processing'
            "
            >线路四生成中</el-tag
          >
          <el-tag v-else-if="row.four_current_status == 'failed'" type="danger"
            >线路四失败</el-tag
          >
          <el-tag v-else-if="row.four_current_status == 'nothing'" type="danger"
            >线路四未训练</el-tag
          >
          
        </template>

        <template v-slot:is_status="{ row }">
          <ele-dot
            v-if="row.is_status == 1"
            type="warning"
            effect="plain"
            text="待审核"
            :ripple="true"
          />
          <ele-dot
            v-if="row.is_status == 2"
            type="success"
            effect="plain"
            text="审核通过"
            :ripple="true"
          />
          <ele-dot
            v-if="row.is_status == 3"
            type="danger"
            effect="plain"
            text="审核拒绝"
            :ripple="true"
          />
          <div
            v-if="row.is_status == 3 && row.refuse"
            style="margin-top: 5px; font-size: 2px"
          >
            拒绝原因：<span style="font-weight: bold; color: red">{{
              row.refuse
            }}</span>
          </div>
        </template>

        <!-- 操作列 -->
        <template v-slot:action="{ row }">
          <el-link
            v-if="row.is_status == 1"
            type="success"
            :underline="false"
            icon="el-icon-circle-check"
            @click="openCheck(row)"
          >
            审核
          </el-link>

          <el-popconfirm
            class="ele-action"
            title="确定要删除此数据吗？"
            @confirm="remove(row)"
          >
            <template v-slot:reference>
              <el-link type="danger" :underline="false" icon="el-icon-delete">
                删除
              </el-link>
            </template>
          </el-popconfirm>

          <el-popconfirm
            v-if="row.is_refund_one != 1  &&  row.current_status =='fail'"
            class="ele-action"
            title="线路一退款"
            @confirm="refund_one(row)"
          >
            <template v-slot:reference>
              <el-link type="refund" :underline="false" icon="el-icon-key">
                线路一退款
              </el-link>
            </template>
          </el-popconfirm>

          <el-popconfirm
            v-if="row.is_refund_two != 1 &&  row.new_current_status =='fail'"
            class="ele-action"
            title="线路二退款"
            @confirm="refund_two(row)"
          >
            <template v-slot:reference>
              <el-link type="refund" :underline="false" icon="el-icon-key">
                线路二退款
              </el-link>
            </template>
          </el-popconfirm>

          <el-popconfirm
            v-if="
           row.is_refund_three != 1 &&  row.composite_current_status =='fail'
            "
            class="ele-action"
            title="线路三退款"
            @confirm="refund_three(row)"
          >
            <template v-slot:reference>
              <el-link type="refund" :underline="false" icon="el-icon-key">
                线路三退款
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
    <check :visible.sync="showCheck" :data="current" @done="reload" />
  </div>
</template>

<script>
import LogSearch from './components/log-search';
import Check from './components/check';
import { list, remove, modify, sortChange,refund_one,refund_two,refund_three } from '@/api/avatar';
import Sortable from 'sortablejs';

export default {
  name: 'Avatar',
  components: {
    LogSearch,
    Check
  },
  data() {
    return {
      // 表格列配置
      columns: [
        {
          columnKey: 'selection',
          type: 'selection',
          width: 45,
          align: 'center',
          fixed: 'left'
        },
        {
          prop: 'user',
          label: '用户',
          showOverflowTooltip: true,
          minWidth: 110,
          slot: 'user'
        },
        {
          prop: 'name',
          label: '名称',
          showOverflowTooltip: true,
          minWidth: 100
        },
        // {
        //   prop: 'a_type',
        //   label: '类型',
        //   align: 'center',
        //   showOverflowTooltip: true,
        //   minWidth: 90,
        //   slot: 'a_type'
        // },
        // {
        //   prop: 'type',
        //   label: '模式',
        //   align: 'center',
        //   showOverflowTooltip: true,
        //   minWidth: 90,
        //   slot: 'type'
        // },

        {
          prop: 'video_url',
          label: '链接',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 220,
          slot: 'video_url'
        },
        {
          prop: 'current_status',
          label: '状态',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 350,
          slot: 'current_status'
        },

        {
          prop: 'is_status',
          label: '审核状态',
          align: 'center',
          width: 110,
          resizable: false,
          slot: 'is_status',
          showOverflowTooltip: true
        },

        {
          prop: 'create_time',
          label: '创建时间',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 90
        },
        {
          columnKey: 'action',
          label: '操作',
          width: 480,
          align: 'center',
          resizable: false,
          slot: 'action',
          hideInSetting: true,
          showOverflowTooltip: true
        }
      ],
      // 表格选中数据
      selection: [],
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showEdit: false,
      // 是否显示审核弹窗
      showCheck: false,
      //弹框名称
      id: 0
    };
  },
  methods: {
    isImageFile(url) {
      // 获取文件扩展名
      const extension = url.substring(url.lastIndexOf('.') + 1).toLowerCase();
      // 支持的图片扩展名列表
      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];
      // 判断当前文件扩展名是否为图片扩展名
      if (imageExtensions.includes(extension)) {
        return true;
      }
      return false;
    },
    rowDrop() {
      const tbody = document.querySelector('.el-table__body-wrapper tbody');
      const _this = this;
      Sortable.create(tbody, {
        forceFallback: true,
        dragClass: 'drag',
        delay: 100,
        animation: 1000,
        onEnd({ newIndex, oldIndex }) {
          console.log(newIndex);
          console.log(oldIndex);
          //更换表格数据 后续拖动坐标才会正确
          let data = _this.$refs.table.getData();

          let nid = data[newIndex].id;
          let oid = data[oldIndex].id;

          console.log(nid);
          console.log(oid);

          const currRow = data.splice(oldIndex, 1)[0];
          data.splice(newIndex, 0, currRow);

          sortChange(data)
            .then((msg) => {})
            .catch((e) => {
              _this.$refs.table.reload();
            });
          //去数据库更换排序
          console.log(_this.$refs.table.getData());
        }
      });
    },
    /* 表格数据源 */
    datasource({ page, limit, where, order }) {
      return list({ ...where, ...order, page, limit });
    },
    /* 刷新表格 */
    reload(where) {
      this.$refs.table.reload({ page: 1, where: where });
    },
    /* 打开编辑弹窗 */
    openEdit(row) {
      this.current = row;
      this.showEdit = true;
    },
    /* 打开编辑弹窗 */
    openCheck(row) {
      this.id = row.id;
      this.current = row;
      this.showCheck = true;
    },
    /* 删除 */
    remove(row) {
      const loading = this.$loading({ lock: true });
      remove(row.id)
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
    },

    refund_one(row) {
      const loading = this.$loading({ lock: true });
      refund_one(row.id)
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
    },
    refund_two(row) {
      const loading = this.$loading({ lock: true });
      refund_two(row.id)
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
    },
    refund_three(row) {
      const loading = this.$loading({ lock: true });
      refund_three(row.id)
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
    },
    /* 批量删除 */
    removeBatch() {
      if (!this.selection.length) {
        this.$message.error('请至少选择一条数据');
        return;
      }
      this.$confirm('确定要删除选中的数据吗?', '提示', {
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading({ lock: true });
          remove(this.selection.map((d) => d.id))
            .then((msg) => {
              loading.close();
              this.$message.success(msg);
              this.reload();
            })
            .catch((e) => {
              loading.close();
              this.$message.error(e.message);
            });
        })
        .catch(() => {});
    },

    /* 更改状态 */
    editStatus(row) {
      const loading = this.$loading({ lock: true });
      modify({ id: row.id, field: 'is_display', value: row.is_display })
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
        })
        .catch((e) => {
          loading.close();
          row.status = !row.status ? 1 : 0;
          this.$message.error(e.message);
        });
    }
  },
  mounted() {
    this.rowDrop();
    //this.columnDrop()
  }
};
</script>

<style lang="scss" scoped>
.drag {
  background: #000 !important;
  background-image: linear-gradient(#333, #999) !important;
}

.ele-cell-content {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
