<!-- 用户编辑弹窗 -->
<template>
  <ele-modal
    width="50%"
    :visible="visible"
    :append-to-body="true"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="'用户详情'"
    @update:visible="updateVisible"
    v-if="data"
  >
    <el-row :gutter="15">
      <el-col v-bind="{ md: 6, sm: 8 }">
        <el-card shadow="never" body-style="padding: 25px;">
          <div class="user-info-card">
            <div class="user-info-avatar-group">
              <img class="user-info-avatar" :src="data.avatar" alt="" />
            </div>
            <h2 class="user-info-name">{{ data.nickname }}</h2>
          </div>
          <div class="user-info-list">
            <div class="user-info-item">
              <i class="el-icon-time"></i>
              <span>加入{{ data.day }}天</span>
            </div>

            <div class="user-info-item">
              <i class="el-icon-coin"></i>
              <span>余额 ：{{ data.balance }}</span>
            </div>

            <div class="user-info-item">
              <i class="el-icon-_salary"></i>
              <span>佣金：{{ data.brokerage }}</span>
            </div>

            <div class="user-info-item">
              <i class="el-icon-_school"></i>
              <span
                >推荐人：{{ data.p_nickname ? data.p_nickname : '无' }}</span
              >
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col v-bind="{ md: 18, sm: 16 }">
        <el-card shadow="never" body-style="padding: 0;">
          <el-tabs class="user-info-tabs">
            <div class="user-account-list">
              <div class="user-account-item ele-cell">
                <i class="user-account-icon el-icon-message-solid"></i>
                <div class="ele-cell-content">
                  <div>绑定手机</div>
                  <div v-if="data.telphone" class="ele-text-secondary"
                    >已绑定手机: {{ data.telphone }}</div
                  >
                  <div v-else class="ele-text-secondary"
                    >当前未绑定手机号码</div
                  >
                </div>
              </div>

              <div class="user-account-item ele-cell">
                <i class="user-account-icon el-icon-_prerogative-solid"></i>
                <div class="ele-cell-content">
                  <div>会员身份</div>
                  <div v-if="data.is_member" class="ele-text-secondary"
                    >到期时间: {{ data.maturity_time }}</div
                  >
                  <div v-else class="ele-text-secondary">当前未开通会员</div>
                </div>
                <el-link
                  type="primary"
                  :underline="false"
                  @click="openMemeber()"
                  >去修改</el-link
                >
              </div>

              <div class="user-account-item ele-cell">
                <i class="user-account-icon el-icon-s-promotion"></i>
                <div class="ele-cell-content">
                  <div>分销二维码</div>
                  <div v-if="images[0]" class="ele-text-secondary">
                    {{ images[0] }}</div
                  >
                </div>
              </div>

              <div class="user-account-item ele-cell code-class">
                <el-image
                  v-if="data.qrcode"
                  style="width: 100px; height: 100px"
                  :src="data.qrcode"
                  :preview-src-list="images"
                  :zIndex="9999"
                />

                <el-button
                  type="primary"
                  :loading="loading"
                  @click="reloadQrcode"
                >
                  重新生成
                </el-button>
              </div>

              <div class="user-account-item ele-cell">
                <div style="margin-right: 10px">分销规则</div>
                <el-radio-group v-model="form.distribution_level_logic">
                  <el-radio :label="1" value="1">按系统逻辑</el-radio>
                  <el-radio :label="2" value="2">可上升不可下调</el-radio>
                  <el-radio :label="3" value="3">可下调不可上升 </el-radio>
                </el-radio-group>
              </div>
              <div class="user-account-item ele-cell">
                <div style="margin-right: 10px">分销等级</div>
                <el-select
                  v-model="form1"
                  placeholder="请选择"
                  @change="changeValue"
                  value-key="label"
                >
                  <el-option
                    v-for="item in packageList"
                    :key="item.value.id"
                    :label="item.label"
                    :value="item"
                  >
                  </el-option>
                </el-select>
              </div>
            </div>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>

    <member-edit :visible.sync="showMember" :data="data" @done="reload" />

    <template v-slot:footer>
      <el-button @click="updateVisible(false)">取消 </el-button>
      <el-button type="primary" :loading="loading" @click="save"
        >保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script>
import MemberEdit from './member-edit';
import EleImageUpload from 'ele-admin/es/ele-image-upload';
import { update, reloadQrcode } from '@/api/user/list';
import { levelList } from '@/api/brokerage';
const DEFAULT_FORM = {
  id: 0,

 
  id_no: '',
  password: '',
  area: [],
  commission: 0,
  
  distribution_level_logic: 1
};

export default {
  name: 'UserEdit',
  components: { EleImageUpload, MemberEdit },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  computed: {
    balanceName() {
      return this.$store.state.user.balance_name;
    },
    brokerageName() {
      return this.$store.state.user.brokerage_name;
    }
  },
  data() {
    return {
      images: [],
      // tab页选中
      active: 'account',
      // 表单数据
      form: { ...DEFAULT_FORM },
      form1: { distribution_level: 0, distribution_level_id: 0 },

      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false,
      // 是否显示会员弹窗
      showMember: false,
      packageList: []
    };
  },
  methods: {
    reload() {
      this.$emit('update:visible', false);
      this.$emit('done');
    },
    /* 保存编辑 */
    save() {
      this.loading = true;
      const data = {
        distribution_level: this.form1.distribution_level,
        distribution_level_id: this.form1.distribution_level_id,
        ...this.form
      };

      update(data)
        .then((msg) => {
          this.loading = false;
          this.$message.success(msg);
          this.updateVisible(false);
          this.$emit('done');
        })
        .catch((e) => {
          this.loading = false;
          this.$message.error(e.message);
        });
    },
    /* 打开会员弹窗 */
    openMemeber() {
      this.showMember = true;
    },
    /* 更新visible */
    updateVisible(value) {
      this.$emit('update:visible', value);
    },
    /* 更新visible */
    reloadQrcode(index) {
      const loading = this.$loading({ lock: true });
      reloadQrcode({ id: this.data.id, type: index })
        .then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.updateVisible(false);
          this.$emit('done');
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
    },
    levelListFn() {
      levelList({ page: 1, limit: 10 })
        .then((data) => {
          if (data != null) {
            this.packageList = data.list.map((item) => {
              return {
                value: {
                  id: item.id,
                  level: item.level
                },
                // value: item.id,
                label: item.name
              };
            });
            console.log(this.packageList);
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },
    changeValue(e) {
      // this.form.package_id = e;
      console.log(e);
      this.form1.distribution_level_id = e.value.id;
      this.form1.distribution_level = e.value.level;
    }
  },

  watch: {
    visible(visible) {
      if (visible) {
        if (this.data) {
          this.$util.assignObject(this.form, {
            ...this.data
          });

          let qrcode = this.data.qrcode;
          if (qrcode) {
            this.images.push(qrcode);
          }
          console.log(this.data.id, '=======================');
          this.form.id = this.data.id;
          this.isUpdate = true;

          


          this.form1.distribution_level= this.data.distribution_level
          this.form1.distribution_level_id= this.data.distribution_level_id
          this.form1.label= this.data.name
           this.form1.value= {
            id:this.data.distribution_level_id,
            level:this.data.distribution_level

           }
          console.log(this.form1)
          this.levelListFn();
        } else {
          this.isUpdate = false;
        }
      } else {
        this.images = [];
        this.form = { ...DEFAULT_FORM };
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.ele-body {
  padding-bottom: 0;
}

.el-card {
  margin-bottom: 15px;
}

/* 用户资料卡片 */
.user-info-card {
  padding: 8px 0;
  text-align: center;

  .user-info-avatar-group {
    position: relative;
    cursor: pointer;
    margin: 0 auto;
    width: 110px;
    height: 110px;
    border-radius: 50%;
    overflow: hidden;

    & > i {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      font-size: 30px;
      display: none;
      z-index: 2;
    }

    &:hover {
      & > i {
        display: block;
      }

      &:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.3);
      }
    }
  }

  .user-info-avatar {
    width: 110px;
    height: 110px;
    border-radius: 50%;
    object-fit: cover;
  }

  .user-info-name {
    font-size: 24px;
    margin-top: 20px;
  }

  .user-info-desc {
    margin-top: 8px;
  }
}

/* 用户信息列表 */
.user-info-list {
  margin-top: 30px;

  .user-info-item {
    margin-bottom: 16px;
    display: flex;
    align-items: baseline;

    & > i {
      margin-right: 10px;
      font-size: 16px;
    }

    & > span {
      flex: 1;
      display: block;
    }
  }
}

/* 用户标签 */
.user-info-tags .el-tag {
  margin: 10px 10px 0 0;
}

/* 用户账号绑定列表 */
.user-account-list {
  padding: 16px 20px;
  .code-class {
    width: 200px;
    margin-left: 50px;
    display: flex;
    flex-direction: column;
  }
  .user-account-item {
    padding: 15px;
    .ele-text-secondary {
      margin-top: 6px;
    }

    .user-account-icon {
      width: 42px;
      height: 42px;
      line-height: 42px;
      text-align: center;
      color: #fff;
      font-size: 26px;
      border-radius: 50%;
      background-color: #1476fe;
      box-sizing: border-box;

      &.el-icon-_wechat {
        background-color: #4daf29;
        font-size: 28px;
      }

      &.el-icon-_alipay {
        background-color: #1476fe;
        padding-left: 5px;
        font-size: 32px;
      }
    }
  }
}

/* tab 页签 */
.user-info-tabs {
  :deep(.el-tabs__nav-wrap) {
    padding-left: 24px;
  }

  :deep(.el-tabs__item) {
    height: 50px;
    line-height: 50px;
    font-size: 15px;
  }
}
</style>
