<!-- 用户编辑弹窗 -->
<template>
  <ele-modal
    width="70%"
    :visible="visible"
    :append-to-body="true"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? '修改商品' : '添加商品'"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
     
      <el-row :gutter="15">
        <el-col :sm="12">

          <el-form-item label="商品名称:" prop="name">
            <el-input
              clearable
              v-model="form.name"
              placeholder="请输入商品名称"
            />
           
          </el-form-item>
             <el-form-item label="类型:" prop="card_type">
            <el-radio-group v-model="form.card_type">
              <el-radio :label=1 value=1 >合伙人购买</el-radio>
              <el-radio :label="2" value="2">用户使用</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="商品价格:" prop="money">
            <el-input type="number" step="0.01" v-model="form.money" placeholder="请输入商品价格" clearable >
              <template slot="append">元</template>
            </el-input>
          </el-form-item>

          <el-form-item label="卡密数量:" prop="quantity">
            <el-input type="number" v-model="form.quantity" placeholder="请输入卡密数量" clearable >
              <template slot="append">张</template>
            </el-input>
          </el-form-item>
        
          <el-form-item label="图片:" prop="pic_url">
            <span slot="label">
              图片
              <el-tooltip placement="top">
                <div slot="content">
                  建议上传320px * 320px尺寸,或者等比例图片
                </div>
                <i class="el-icon-question" />
              </el-tooltip>
            </span>  
            <!--<ele-image-upload v-model="img" :limit="1" :drag="true" :multiple="false"  @upload="onUpload" /> --> 
              <div class="ele-image-upload-list">
              <div class="ele-image-upload-item ele-image-upload-button" @click="modalPicTap('dan','pic_url','图片')">
                <div>
                  <div tabindex="0" class="el-upload el-upload--text">
                    <div class="el-upload-dragger">
                      <i class="el-icon-plus ele-image-upload-icon"></i>
                    </div>
                      <div class="ele-image-upload-item" style="margin:0 0 0 0;" v-if="form.pic_url!=''">
                      <div class="el-image" >
                        <img :src="form.pic_url" width="100%" height="100%"  class="el-image__inner" style="object-fit: cover;">
                      </div>
                      <div class="ele-image-upload-close" @click="handleRemove('pic_url')"><i class="el-icon-close"></i></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :sm="12">

          <el-form-item label="类型:" prop="type">
            <el-radio-group v-model="form.type">
              <el-radio :label=1 value=1 >会员</el-radio>
              <el-radio :label="2" value="2">点数</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="选择会员" prop="member_id" v-if="form.type == 1">
            <member-select
              placeholder="请选择会员"
              v-model="form.member_id"
              @done='updateCount'
            />
          </el-form-item>

          <el-form-item label="会员天数" prop="day" v-if="form.type == 1">
            <el-input disabled  type="number" v-model="form.day" placeholder="请输入会员天数" clearable >
              <template slot="append">天</template>
            </el-input>
          </el-form-item>

  

          <el-form-item  label="视频合成无限:" prop="second_infinite" v-if="form.type == 1">
            <el-radio-group  v-model="form.second_infinite" size="small" disabled>
              <el-radio-button :label=0 :value=0>否</el-radio-button>
              <el-radio-button :label=1 :value=1>是</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="视频合成秒数" prop="second" v-if="form.type == 1 && form.second_infinite == 0">
            <el-input disabled  type="number" v-model="form.second" placeholder="请输入视频合成秒数" clearable >
              <template slot="append">秒</template>
            </el-input>
          </el-form-item>

          <el-form-item label="ai文案次数" prop="voice_twin_count" v-if="form.type == 1">
            <el-input disabled  type="number" v-model="form.ai_copywriting_times" placeholder="ai文案次数" clearable >
              <template slot="append">次</template>
            </el-input>
          </el-form-item>

          <el-form-item label="高保真次数" prop="voice_twin_count" v-if="form.type == 1">
            <el-input disabled  type="number" v-model="form.voice_twin_count" placeholder="请输入声音高保真次数" clearable >
              <template slot="append">次</template>
            </el-input>
          </el-form-item>

          <el-form-item label="高保真合成字数" prop="voice_twin_count" v-if="form.type == 1">
            <el-input disabled  type="number" v-model="form.high_fidelity_words_number" placeholder="高保真合成字数" clearable >
              <template slot="append">字数</template>
            </el-input>
          </el-form-item>
          <el-form-item label="专业版克隆次数" prop="voice_twin_count" v-if="form.type == 1">
            <el-input disabled  type="number" v-model="form.xunfei_sound_clone_words_number" placeholder="专业版克隆次数" clearable >
              <template slot="append">次</template>
            </el-input>
          </el-form-item>
          <el-form-item label="专业版合成字数" prop="voice_twin_count" v-if="form.type == 1">
            <el-input disabled  type="number" v-model="form.xunfei_sound_generate_words_number" placeholder="专业版合成字数" clearable >
              <template slot="append">字数</template>
            </el-input>
          </el-form-item>
          <el-form-item label="ai标题" prop="ai_title_times" v-if="form.type == 1">
            <el-input disabled  type="number" v-model="form.ai_title_times" placeholder="ai标题次数" clearable >
              <template slot="append">次数</template>
            </el-input>
          </el-form-item>


          
          <el-form-item label="充值点数" prop="point" v-if="form.type == 2">
            <el-input  type="number" v-model="form.point" placeholder="请输入充值点数" clearable >
              <template slot="append">点</template>
            </el-input>
          </el-form-item>

       
          
          <el-form-item label="商品库存:" prop="inventory">
            <el-input-number
              :min="0"
              clearable
              v-model="form.inventory"
              placeholder="请输入商品库存"
            />
          </el-form-item>
          <el-form-item label="商品已售:" prop="sold">
            <el-input-number
              :min="0"
              clearable
              v-model="form.sold"
              placeholder="请输入商品已售"
            />
          </el-form-item>
          <el-form-item label="排序号:" prop="sort">
            <el-input-number
              :min="0"
              v-model="form.sort"
              placeholder="请输入排序号"
              controls-position="right"
              class="ele-fluid ele-text-left"
            />
          </el-form-item>

          <el-form-item label="展示状态:">
            <el-switch
              :active-value="1"
              :inactive-value="2"
              v-model="form.is_display"
            />
            <el-tooltip
              placement="top"
              content="选择不可见则前端页面不显示该数据"
            >
              <i
                class="el-icon-_question"
                style="vertical-align: middle; margin-left: 8px"
              ></i>
            </el-tooltip>
          </el-form-item>

        </el-col>
       
      </el-row>
      <el-form-item label="商品详情:" >
        <tinymce-editor v-model="form.detail" :init="editorConfig"  v-if="visible" />
      </el-form-item>  
    </el-form>
    <template v-slot:footer>
      <el-button @click="updateVisible(false)">取消 </el-button>
      <el-button type="primary" :loading="loading" @click="save"
        >保存
      </el-button>
    </template>
    <uploadPictures
      :isChoice="isChoice"
      :visible.sync="modalPic"
      @getPic="getPic"
      :gridBtn="gridBtn"
      :gridPic="gridPic"
      :title="modalTitle"
    ></uploadPictures>
  </ele-modal>
</template>

<script>
  import MemberSelect from "@/views/member/list/components/member-select";
  import uploadPictures from "@/components/uploadPictures";
  import EleImageUpload from 'ele-admin/es/ele-image-upload';
  import TinymceEditor from '@/components/TinymceEditor';
  import request from '@/utils/request';
  import { add,update } from '@/api/card/goods';
  const DEFAULT_FORM = {
    id: 0,
    name: '',
    money: '',
    pic_url:'',
    quantity: 0,
    member_id: 0,
    member_name: '',
    type: 1,
    day: 0,
    point: 0,
    second: 0,
    second_infinite: 0,
    voice_twin_count: 0,
    inventory: 0,
    sold: 0,
    detail: '',
    sort:0,
    is_display:1,
    card_type : 1,
      ai_copywriting_times:0,
      high_fidelity_words_number:0,
      xunfei_sound_generate_words_number:0,
      xunfei_sound_clone_words_number:0,
      ai_title_times:0

  };

  export default {
    name: 'GoodsEdit',
    components: { MemberSelect,EleImageUpload,uploadPictures,TinymceEditor },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object,
      b_type:0
    },
    computed: {
      balanceName() {
        return this.$store.state.user.balance_name;
      }
    },
    data() {
      return {
        modalTitle:'选择图片',
        modalPic: false,
        isChoice: "单选",
        gridBtn: {
          xl: 4,
          lg: 8,
          md: 8,
          sm: 8,
          xs: 8,
        },
        gridPic: {
          xl: 6,
          lg: 8,
          md: 12,
          sm: 12,
          xs: 12,
        },
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          name: [
            {
              required: true,
              message: '请输入商品名称',
              trigger: 'blur'
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false,
        editorConfig:{
          height: 525,
          relative_urls : false,
          convert_urls: false,
          images_upload_handler: (blobInfo, success, error) => {
            const file = blobInfo.blob();
            // 使用 axios 上传，实际开发这段建议写在 api 中再调用 api
            const formData = new FormData();
            formData.append('file', file, file.name);
            request({
              url: '/common/uploadFile',
              method: 'post',
              data: formData,
              onUploadProgress: (e) => {  // 文件上传进度回调
                     
              }
            }).then((res) => {
                if(res.data.code === 0) {
                    success(res.data.data.url);
                }else{
                  error(res.data.message);
                }
            }).catch((e) => {
                error('exception');
            });
          }
        },
      };
    },
    methods: {
      search(where) {
        // debugger
        this.$refs.select.reload({
          where: where,
          page: 1
        });
      },
      // 选择图片
      modalPicTap(tit, picTit,openTitle) {
        this.modalTitle = openTitle;
        this.isChoice = tit === "dan" ? "单选" : "多选";
        this.picTit = picTit;
        this.modalPic = true;
      },
      // 选中图片
      getPic(pc) {
        switch (this.picTit) {
          case "pic_url":
            this.form.pic_url = pc.satt_dir;
            break;
          case "background":
            this.form.background = pc.satt_dir;
            break;
        }
        this.modalPic = false;
      },
      //删除图片
      handleRemove(field) {
        this.form[field] = '';
      },

      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }

          this.loading = true;
          const data = {
            ...this.form,
          };

          const saveOrUpdata = this.isUpdate ? update : add;

          saveOrUpdata(data).then((msg) => {
            this.loading = false;
            this.$message.success(msg);
            this.updateVisible(false);
            this.$emit('done',{ b_type: this.b_type });
          }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
          });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 更新visible */
      updateCount(object) {
        this.form.member_id = object.id;
        this.form.member_name = object.name;
        this.form.day = object.day;
      
        this.form.second_infinite = object.second_infinite;
        this.form.second = object.second;
        this.form.voice_twin_count = object.voice_twin_count;
        this.form.ai_copywriting_times = object.ai_copywriting_times;
         this.form.high_fidelity_words_number = object.high_fidelity_words_number;
          this.form.xunfei_sound_generate_words_number = object.xunfei_fidelity_words_number;
          this.form.xunfei_sound_clone_words_number = object.xunfei_sound_clone_words_number;
          this.form.ai_title_times = object.ai_title_times;
          

      }
    },
    watch: {
      visible(visible) {
        if (visible) {
          if (this.data) {

            this.$util.assignObject(this.form, {
              ...this.data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
