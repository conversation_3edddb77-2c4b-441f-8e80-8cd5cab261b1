<template>
  <div class="ele-body">
       <!-- 搜索表单 -->
      <MemberSearch @search="reload" />
    <el-card shadow="never">
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            新增
          </el-button> 
          
        </template>

      

        <template v-slot:day="{ row }">
          <div style="max-height: 200px; overflow-y: auto; line-height: 1.4;">
            <div>天数：{{row.day}}</div>
            <div>视频合成秒数：{{row.second_infinite == 0 ? row.second : '无限'}}</div>
            <div>声音高保真克隆次数：{{row.voice_twin_count}}</div>
            <div>声音高保真合成字数：{{row.high_fidelity_words_number}}</div>
            <div>ai文案仿写|提取次数：{{row.ai_copywriting_times}}</div>
            <div>专业版声音克隆次数：{{row.xunfei_sound_clone_words_number}}</div>
            <div>专业版声音合成字数：{{row.xunfei_fidelity_words_number}}</div>
            <div>ai标题：{{row.ai_title_times}}</div>
            <div>AI立项：{{row.ai_project_deduct || 0}}</div>
            <div>AI诊断分析：{{row.ai_diagnosis_deduct || 0}}</div>
            <div>AI商业定位：{{row.ai_business_position_deduct || 0}}</div>
            <div>AI账号包装：{{row.ai_account_package_deduct || 0}}</div>
            <div>深度思考：{{row.deep_thinking_deduct || 0}}</div>
            <div>爆款选题：{{row.hot_topic_deduct || 0}}</div>
            <div>文案编导：{{row.copywriting_director_deduct || 0}}</div>
            <div>热点跟拍：{{row.hotspot_follow_deduct || 0}}</div>
            <div>小红书笔记：{{row.xiaohongshu_note_deduct || 0}}</div>
            <div>AI翻译官：{{row.ai_translator_deduct || 0}}</div>
            <div>口播精剪：{{row.voiceover_edit_deduct || 0}}</div>
            <div>批量混剪：{{row.batch_mix_edit_deduct || 0}}</div>
            <div>图生视频720P：{{row.image_to_video_720p_deduct || 0}}</div>
            <div>图生视频1080P：{{row.image_to_video_1080p_deduct || 0}}</div>
            <div>文生视频720P：{{row.text_to_video_720p_deduct || 0}}</div>
            <div>文生视频1080P：{{row.text_to_video_1080p_deduct || 0}}</div>
            <div>AI设计：{{row.ai_design_deduct || 0}}</div>
            <div>图片精修：{{row.image_retouch_deduct || 0}}</div>
            <div>表情包：{{row.emoji_package_deduct || 0}}</div>
            <div>商品抠图：{{row.product_cutout_deduct || 0}}</div>
          </div>
        </template>

        <!-- 状态列 -->
        <template v-slot:is_display="{ row }">
          <el-switch
            :active-value="1"
            :inactive-value="2"
            v-model="row.is_display"
            @change="editStatus(row)"
          />
        </template>


        <!-- 状态列 -->
        <template v-slot:is_recommend="{ row }">
          <el-switch
            :active-value="1"
            :inactive-value="2"
            v-model="row.is_recommend"
            @change="editRecommend(row)"
          />
        </template>

        <!-- 权益描述列 -->
        <template v-slot:member_info="{ row }">
          <el-button
            type="text"
            size="small"
            icon="el-icon-view"
            @click="viewBenefits(row)"
          >
            查看权益
            <span v-if="getBenefitsCount(row.member_info) > 0" class="benefits-count">
              ({{ getBenefitsCount(row.member_info) }}项)
            </span>
          </el-button>
        </template>


        <template v-slot:sort>
          <i class="sort-handle el-icon-rank ele-text-secondary" style="cursor: move;"></i>
        </template>

        
        <!-- 操作列 -->
        <template v-slot:action="{ row }">
          <el-link
            type="primary"
            :underline="false"
            icon="el-icon-edit"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          
        </template>
      </ele-pro-table>
    </el-card>
    <!-- 编辑弹窗 -->
    <member-edit :visible.sync="showEdit" :data="current"  @done="reload"/>

    <!-- 权益查看弹窗 -->
    <member-benefits-view
      :visible.sync="showBenefitsView"
      :data="currentBenefits"
      @done="reload"
    />
  </div>
</template>

<script>
  import MemberSearch from './components/member-search';
  import MemberEdit from './components/member-edit';
  import MemberBenefitsView from './components/member-benefits-view';
  import { list, remove, modify,sortChange,doDefault  } from '@/api/member/list';
  import Sortable from 'sortablejs';

  export default {
    name: 'Member',
    components: {
      MemberSearch,
      MemberEdit,
      MemberBenefitsView
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },
          {
            prop: 'sort',
            label: '排序',
            showOverflowTooltip: true,
            minWidth: 60,
            slot: 'sort'
          },
          {
            prop: 'name',
            label: '名称',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'money',
            label: '价格',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 90,
          },
          {
            prop: 'day',
            label: '套餐',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 280,
            slot: 'day'
          },
          // {
          //   prop: 'is_recommend',
          //   label: '推荐状态',
          //   align: 'center',
          //   showOverflowTooltip: true,
          //   minWidth: 110,
          //   slot: 'is_recommend'
          // },
          {
            prop: 'is_display',
            label: '展示状态',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'is_display'
          },
          {
            prop: 'member_info',
            label: '权益描述',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'member_info'
          },
          
          {
            prop: 'create_time',
            label: '创建时间',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 100,
            formatter: (row, column, cellValue) => {
              return this.$util.toDateString(cellValue);
            }
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 250,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            showOverflowTooltip: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示分配回收员
        showCollector: false,
        // 是否显示权益查看弹窗
        showBenefitsView: false,
        // 当前查看的权益数据
        currentBenefits: null,
        //弹框名称
        id:0,
      };
    },
    methods: {
      rowDrop() {
        const tbody = document.querySelector('.el-table__body-wrapper tbody')
        const _this = this
        Sortable.create(tbody, {
          forceFallback: true,
          dragClass: 'drag',
          delay:100,
          animation: 1000,
          onEnd({ newIndex, oldIndex }) {
            console.log(newIndex);
            console.log(oldIndex);
            //更换表格数据 后续拖动坐标才会正确
            let data = _this.$refs.table.getData();
             
            let nid = data[newIndex].id;
            let oid = data[oldIndex].id;

            console.log(nid);
            console.log(oid);
            
            const currRow = data.splice(oldIndex, 1)[0];
            data.splice(newIndex, 0, currRow);

            sortChange(data).then((msg) => {
              
            }).catch((e) => {
              _this.$refs.table.reload();
            });
            //去数据库更换排序
            console.log(_this.$refs.table.getData());
          }
        })
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return list({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id).then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
      },

      /* 一键默认 */
      doDefault() {
        
        this.$confirm('请谨慎使用该功能，一旦使用则会变成系统默认内容。确定一键默认？', '提示', {
          type: 'warning'
        }).then(() => {
          const loading = this.$loading({ lock: true });
          doDefault().then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
        }).catch(() => {});
      },
      
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的数据吗?', '提示', {
          type: 'warning'
        }).then(() => {
          const loading = this.$loading({ lock: true });
          remove(this.selection.map((d) => d.id))
            .then((msg) => {
              loading.close();
              this.$message.success(msg);
              this.reload();
            })
            .catch((e) => {
              loading.close();
              this.$message.error(e.message);
            });
        }).catch(() => {});
      },
      
      /* 更改状态 */
      editStatus(row) {
        const loading = this.$loading({ lock: true });
        modify({id:row.id,field:'is_display',value: row.is_display})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            row.is_display = row.is_display == 1 ? 2 : 1;
            this.$message.error(e.message);
          });
      },
       /* 更改状态 */
       editRecommend(row) {
        const loading = this.$loading({ lock: true });
        modify({id:row.id,field:'is_recommend',value: row.is_recommend})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            row.is_recommend = row.is_recommend  == 1 ? 2 : 1;
            this.$message.error(e.message);
          });
      },

      /* 查看权益描述 */
      viewBenefits(row) {
        this.currentBenefits = row;
        this.showBenefitsView = true;
      },

      /* 获取权益描述数量 */
      getBenefitsCount(memberInfo) {
        if (!memberInfo) return 0;
        try {
          const benefits = typeof memberInfo === 'string' ? JSON.parse(memberInfo) : memberInfo;
          return Array.isArray(benefits) ? benefits.length : 0;
        } catch (e) {
          return 0;
        }
      }
    },mounted() {
      this.rowDrop()
      //this.columnDrop()
    }
  };
</script>

<style>
 .drag {
    background: #000 !important;
    background-image: linear-gradient(#333, #999) !important;
  }

.benefits-count {
  color: #67c23a;
  font-weight: bold;
  margin-left: 5px;
}
</style>
