<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <partner-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            class="ele-btn-icon"
            @click="removeBatch"
          >
            删除
          </el-button>
        </template>

        <template v-slot:user="{ row }">
          <div class="ele-cell" v-if="row.figureUser">
            <el-avatar v-if="row.figureUser.avatar" :src="row.figureUser.avatar" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.figureUser.nickname }} </div>
              <div class="ele-cell-desc">{{ row.figureUser.telphone }} </div>
            </div>
          </div>
          <span v-else>无</span>
          
        </template>


        <template v-slot:is_status="{ row }">
          <el-tag v-if="row.is_status==1" type="warning" effect="plain">待审核</el-tag>
          <el-tag v-if="row.is_status==2" type="success" effect="plain">审核通过</el-tag>
          <el-tag v-if="row.is_status==3" type="danger" effect="plain">审核拒绝</el-tag>
          <div v-if="row.is_status==3 && row.refuse " style="margin-top: 5px;font-size: 2px;">
            拒绝原因：<span style="font-weight: bold;color: red;">{{row.refuse}}</span>
          </div>
        </template>

        <!-- 状态列 -->
        <template v-slot:is_freeze="{ row }">
          <el-switch
            :active-value="2"
            :inactive-value="1"
            v-model="row.is_freeze"
            @change="editStatus(row)"
          />
        </template>


        <template v-slot:sort>
          <i class="sort-handle el-icon-rank ele-text-secondary" style="cursor: move;"></i>
        </template>

        
        <!-- 操作列 -->
        <template v-slot:action="{ row }">
          <el-link
            v-if="row.is_status==1"
            type="success"
            :underline="false"
            icon="el-icon-circle-check"
            @click="openCheck(row)"
          >
            审核
          </el-link>
          <el-link
            v-if="row.is_status==2"
            type="primary"
            :underline="false"
            icon="el-icon-circle-check"
            @click="openTransfer(row)"
          >
            划拨
          </el-link>
          <el-link
            type="info"
            :underline="false"
            icon="el-icon-edit"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-popconfirm
            class="ele-action"
            title="确定要删除此数据吗？"
            @confirm="remove(row)"
          >
            <template v-slot:reference>
              <el-link type="danger" :underline="false" icon="el-icon-delete">
                删除
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
    <!-- 编辑弹窗 -->
    <partner-edit :visible.sync="showEdit" :data="current"  @done="reload"/>
    <check :visible.sync="showCheck" :data="current" @done="reload"/>
    <transfer :visible.sync="showTransfer" :data="current" @done="reload"/>
  </div>
</template>

<script>
  import PartnerSearch from './components/partner-search';
  import PartnerEdit from './components/partner-edit';
  import Check from './components/check';
  import Transfer from './components/transfer';
  import { list, remove, modify,sortChange,doDefault  } from '@/api/partner/list';
  import Sortable from 'sortablejs';

  export default {
    name: 'Partner',
    components: {
      PartnerSearch,
      PartnerEdit,
      Check,
      Transfer
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            align: 'center',
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'user',
            label: '用户',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'user'
          },
          {
            prop: 'name',
            label: '名称',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'telphone',
            label: '电话',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110,
          },
          {
            prop: 'is_status',
            label: '审核状态',
            align: 'center',
            width: 140,
            resizable: false,
            slot: 'is_status',
            showOverflowTooltip: true
          },
          {
            prop: 'is_freeze',
            label: '冻结状态',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'is_freeze'
          },
          
          {
            prop: 'create_time',
            label: '申请时间',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 100,
            
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 250,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            showOverflowTooltip: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示审核弹窗
        showCheck: false,
        showTransfer: false,
        //弹框名称
        id:0,
      };
    },
    methods: {
      rowDrop() {
        const tbody = document.querySelector('.el-table__body-wrapper tbody')
        const _this = this
        Sortable.create(tbody, {
          forceFallback: true,
          dragClass: 'drag',
          delay:100,
          animation: 1000,
          onEnd({ newIndex, oldIndex }) {
            console.log(newIndex);
            console.log(oldIndex);
            //更换表格数据 后续拖动坐标才会正确
            let data = _this.$refs.table.getData();
             
            let nid = data[newIndex].id;
            let oid = data[oldIndex].id;

            console.log(nid);
            console.log(oid);
            
            const currRow = data.splice(oldIndex, 1)[0];
            data.splice(newIndex, 0, currRow);

            sortChange(data).then((msg) => {
              
            }).catch((e) => {
              _this.$refs.table.reload();
            });
            //去数据库更换排序
            console.log(_this.$refs.table.getData());
          }
        })
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return list({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      /* 打开编辑弹窗 */
      openCheck(row) {
        this.id = row.id;
        this.current = row;
        this.showCheck = true;
      },
      /* 打开编辑弹窗 */
      openTransfer(row) {
        this.id = row.id;
        this.current = row;
        this.showTransfer = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id).then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
      },

      /* 一键默认 */
      doDefault() {
        
        this.$confirm('请谨慎使用该功能，一旦使用则会变成系统默认内容。确定一键默认？', '提示', {
          type: 'warning'
        }).then(() => {
          const loading = this.$loading({ lock: true });
          doDefault().then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
        }).catch(() => {});
      },
      
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的数据吗?', '提示', {
          type: 'warning'
        }).then(() => {
          const loading = this.$loading({ lock: true });
          remove(this.selection.map((d) => d.id))
            .then((msg) => {
              loading.close();
              this.$message.success(msg);
              this.reload();
            })
            .catch((e) => {
              loading.close();
              this.$message.error(e.message);
            });
        }).catch(() => {});
      },
      
      /* 更改状态 */
      editStatus(row) {
        const loading = this.$loading({ lock: true });
        modify({id:row.id,field:'is_freeze',value: row.is_freeze})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            row.is_freeze = row.is_freeze == 2 ? 1 : 2;
            this.$message.error(e.message);
          });
      },
       /* 更改状态 */
       editRecommend(row) {
        const loading = this.$loading({ lock: true });
        modify({id:row.id,field:'is_recommend',value: row.is_recommend})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            row.is_recommend = !row.is_recommend ? 1 : 0;
            this.$message.error(e.message);
          });
      }
    },mounted() {
      this.rowDrop()
      //this.columnDrop()
    }
  };
</script>

<style>
 .drag {
    background: #000 !important;
    background-image: linear-gradient(#333, #999) !important;
  }
</style>
