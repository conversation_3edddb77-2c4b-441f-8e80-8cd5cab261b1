<template>
  <ele-table-select
    ref="select"
    :clearable="true"
    size="medium"
    :value="value"
    placeholder="请选择"
    value-key="id"
    label-key="name"
    prefix-icon="el-icon-search"
    :table-config="tableConfig"
    @select="updateValue"
    :popper-width="900"
  >
    <template v-slot:toolbar>
      <div style="max-width: 200px">
        <el-input
          clearable
          size="small"
          v-model="keywords"
          placeholder="输入关键字搜索"
          prefix-icon="el-icon-search"
          @blur="search"
        />
      </div>
    </template>

    <template v-slot:user="{ row }">
      <div class="ele-cell" v-if="row.figureUser">
        <el-avatar v-if="row.figureUser.avatar" :src="row.figureUser.avatar" shape="square" :size="40" />
        <div class="ele-cell-content">
          <div class="ele-cell-title">{{ row.figureUser.nickname }} </div>
          <div class="ele-cell-desc">{{ row.figureUser.telphone }} </div>
        </div>
      </div>
      <span v-else>无</span>
      
    </template>

    <template v-slot:train_mode="{ row }">
      <el-tag v-if="row.train_mode==1" type="success" effect="plain">入门版</el-tag>
      <el-tag v-if="row.train_mode==2" type="primary" effect="plain">高保真</el-tag>
       <el-tag v-if="row.train_mode==3" type="primary" effect="plain">专业版本</el-tag>
    </template>

    <template v-slot:voice_urls="{ row }">
      <div v-for="item in row.voice_urls" :key="item">
        <audio   :src="item"   controls="controls"></audio>
      </div>
      
    </template>
  
  </ele-table-select>
  
</template>

<script>
  import { list } from '@/api/voiceTwin';
  export default {
    name: 'VoiceSelect',
    props: {
      // 修改回显的数据
      value: undefined
    },
    data() {
      return {
        keywords:'',
        // 搜索表单
        where: {
          keywords: ''
        },
        tableConfig: {
          datasource: ({ page, limit, where, order }) => {
            where.current_status = 'completed';
            where.keywords = this.keywords;
            return list({ ...where, ...order, page, limit });
          },
          columns: [
            {
              prop: 'user',
              label: '用户',
              showOverflowTooltip: true,
              minWidth: 110,
              slot: 'user'
            },
            {
              prop: 'name',
              label: '名称',
              showOverflowTooltip: true,
              minWidth: 110,
            },
          
            {
              prop: 'train_mode',
              label: '训练模式',
              align: 'center',
              showOverflowTooltip: true,
              minWidth: 90,
              slot: 'train_mode'
            },

            {
              prop: 'voice_urls',
              label: '语音链接',
              align: 'center',
              showOverflowTooltip: true,
              minWidth: 180,
              slot: 'voice_urls'
            },
          ],
          pageSize:5,
          pageSizes:[5,10, 20, 30, 40, 50, 100],
          rowClickChecked: true,
          rowClickCheckedIntelligent: false,
          toolkit: ['reload', 'columns'],
          size: 'large',
          toolStyle: { padding: '0 10px' }
        }
      };
    },
    methods: {
      reload(){
        this.$refs.select.reload();
      },
      updateValue(data) {
        this.$emit('done', data);
      },
      // 搜索
      search(where) {
        where.current_status = 'completed';
        where.keywords = this.keywords;
        this.$refs.select.reload({
          where: where,
          page: 1
        });
      }
    }
  };
</script>
<style lang="less" scoped>
 
 .drag {
    background: #000 !important;
    background-image: linear-gradient(#333, #999) !important;
  }

  .ele-cell-content {
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

</style>

