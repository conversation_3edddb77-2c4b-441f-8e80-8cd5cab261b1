<template>
  <div class="ele-body">
    <el-card shadow="never">
      <statistics-card />
      <!-- 搜索表单 -->
      <brokerage-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            class="ele-btn-icon"
            @click="removeBatch"
          >
            删除
          </el-button>
         
        </template>
        
        <template v-slot:user="{ row }">
          <div class="ele-cell" v-if="row.figureUser">
            <el-avatar v-if="row.figureUser.avatar" :src="row.figureUser.avatar" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.figureUser.nickname }} </div>
              <div class="ele-cell-desc">{{ row.figureUser.telphone }} </div>
            </div>
          </div>
        </template>

        <template v-slot:cash_type="{ row }">
          <ele-dot v-if="row.cash_type==1" :ripple="true" text="支付宝"/>
          <ele-dot v-if="row.cash_type==2" type="success" :ripple="true" text="微信"/>
          <ele-dot v-if="row.cash_type==3" type="warning" :ripple="true" text="余额"/>
        </template>

        <template v-slot:is_status="{ row }">
          <el-tag v-if="row.is_status==1 &&  row.wx_transfer_version == 1  "   >待审核</el-tag>
             <el-tag v-if="row.is_status==1 &&  row.wx_transfer_version == 2 &&  row.cash_type == 2"  >等待用户确认</el-tag>
          <el-tag v-if="row.is_status==2" type="success">已打款</el-tag>
          <el-tag v-if="row.is_status==3" type="danger">已驳回</el-tag>
          <div v-if="row.refuse " style="margin-top: 5px;font-size: 2px;">
            拒绝原因：<span style="font-weight: bold;color: red;">{{row.refuse}}</span>
          </div>
        </template>
        
        <!-- 操作列 -->
        <template v-slot:action="{ row }">
          <el-link
            type="primary"
            :underline="false"
            icon="el-icon-view"
            @click="openEdit(row)"
          >
            详情
          </el-link>
          <el-popconfirm
            class="ele-action"
            title="确定要删除此数据吗？"
            @confirm="remove(row)"
          >
            <template v-slot:reference>
              <el-link type="danger" :underline="false" icon="el-icon-delete">
                删除
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
    <!-- 编辑弹窗 -->
    <brokerage-edit :visible.sync="showEdit" :data="current"  @done="reload"/>
  </div>
</template>

<script>
  import StatisticsCard from './components/statistics-card.vue';
  import BrokerageSearch from './components/brokerage-search';
  import BrokerageEdit from './components/brokerage-edit';
  import { brokerageList, remove } from '@/api/brokerage';

  export default {
    name: 'Brokerage',
    components: {
      StatisticsCard,
      BrokerageSearch,
      BrokerageEdit,
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            align: 'center',
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'log_no',
            label: '订单编号',
            showOverflowTooltip: true,
            minWidth: 110,
            //slot: 'nickname'
          },
          {
            prop: 'user',
            label: '申请人',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'user',
          },
          {
            prop: 'cash_type',
            label: '提现方式',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 100,
            slot: 'cash_type',
          },
          {
            prop: 'commission_wait',
            label: '申请金额',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 80
          },
          {
            prop: 'commission_actual',
            label: '实际金额',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 80
          },
          {
            prop: 'service_charge',
            label: '服务费',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 80
          },
          {
            prop: 'is_status',
            label: '审核状态',
            align: 'center',
            width: 150,
            resizable: false,
            slot: 'is_status',
            showOverflowTooltip: true
          },
          {
            prop: 'create_time',
            label: '时间',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 100,
           
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 250,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            showOverflowTooltip: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示审核弹窗
        showCheck: false,
        // 是否显示充值弹窗
        showRecharge: false,
        //弹框名称
        id:0,
      };
    },
    methods: {
      beforeHandleCommand(index, row,command){
          
          return {
              'index': index,
              'row': row,
              'command':command
          }
    },
      onDropClick(command) {
        
        switch (command.command) {
            case "a"://编辑
                this.openAddNum(command.row,1);
                break;
            case "b"://充值密码
                this.openAddNum(command.row,2);
                break;
            case "c"://删除
                this.openAddNum(command.row,3);
                break;
            case "d"://删除
                this.openAddNum(command.row,4);
                break;
        }
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return brokerageList({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },

      /* 打开编辑弹窗 */
      openCheck(row) {
        this.id = row.id;
        this.current = row;
        this.showCheck = true;
      },

      /* 打开编辑弹窗 */
      openRecharge(row) {
        this.id = row.id;
        this.current = row;
        this.showRecharge = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      },
      /* 更新二维码 */
      reloadQrcode(row) {
        const loading = this.$loading({ lock: true });
        reloadQrcode({user_id:row.userId})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      },

      
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的合伙人吗?', '提示', {
          type: 'warning'
        })
          .then(() => {
            const loading = this.$loading({ lock: true });
            remove(this.selection.map((d) => d.id))
              .then((msg) => {
                loading.close();
                this.$message.success(msg);
                this.reload();
              })
              .catch((e) => {
                loading.close();
                this.$message.error(e.message);
              });
          })
          .catch(() => {});
      },
      /* 重置合伙人密码 */
      resetPsw(row) {
        this.$confirm('确定要重置此合伙人的密码为"123456"吗?', '提示', {
          type: 'warning'
        })
          .then(() => {
            const loading = this.$loading({ lock: true });
            resetPassword(row.id)
              .then((msg) => {
                loading.close();
                this.$message.success(msg);
              })
              .catch((e) => {
                loading.close();
                this.$message.error(e.message);
              });
          })
          .catch(() => {});
      },
      /* 更改状态 */
      editStatus(row) {
        const loading = this.$loading({ lock: true });
        modify({id:row.id,field:'is_freeze',value: row.is_freeze})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
          })
          .catch((e) => {
            loading.close();
            row.status = !row.status ? 1 : 0;
            this.$message.error(e.message);
          });
      }
    }
  };
</script>
