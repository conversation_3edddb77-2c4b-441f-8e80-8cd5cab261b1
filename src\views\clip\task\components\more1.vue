<template>
  <ele-modal
    width="680px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="'详情'"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" label-width="100px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="原视频:">
            <video width="320" height="240" controls :src="currentData.result">
            </video>
          </el-form-item>
        
        </el-col>
      </el-row>
    </el-form>
  </ele-modal>
</template>

<script>
  import { taskInfo } from '@/api/clip/index.js';

const DEFAULT_FORM = {};

export default {
  name: 'askInfo',
  components: {},
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  data() {
    return {
      // 表单提交状态
      loading: false,
      // 表单数据
      form: {
        ...DEFAULT_FORM
      },
      // 表单验证规则
      rules: {},
      // 表单验证信息
      validMsg: '',
      id: '',
      currentData: ''
    };
  },
  mounted() {},
  methods: {
    /* 更新visible */
    updateVisible(value) {
      this.$emit('update:visible', value);
    },
    moreApi(id) {
      taskInfo(id)
        .then((msg) => {
          if (msg != null) {
            console.log(msg);
            this.currentData = msg;
          }
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
    },

    /* 表单提交 */
    submit() {
      this.$refs['form'].validate((valid, obj) => {
        if (valid) {
          this.validMsg = '';
          this.loading = true;
          const data = {
            ...this.form
          };
          save(data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(msg);
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });
        } else {
          this.validMsg = ` 共有校验 ${Object.keys(obj).length} 项不通过`;
          return false;
        }
      });
    }
  },
  watch: {
    visible(visible) {
      this.$nextTick(() => {
        if (visible) {
          if (this.data) {
            // this.$util.assignObject(this.form, {
            //   ...this.data
            // });

            this.id = this.data.id;

            this.moreApi(this.id);

            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          // this.$nextTick(() => {
          //   this.$refs['form'].resetFields();
          //   console.log(this.form);
          // });
        }
      });
    }
  }
};
</script>
