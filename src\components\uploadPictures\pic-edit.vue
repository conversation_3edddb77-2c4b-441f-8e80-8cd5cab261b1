<!-- 机构编辑弹窗 -->
<template>
  <ele-modal
    width="680px"
    append-to-body
    :visible="visible"
    :close-on-click-modal="true"
    custom-class="ele-dialog-form"
    :title="isUpdate ? '修改分类' : '添加分类'"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="82px">
     
         
          <el-form-item label="上级:" >
             <el-select
                v-model="parentId"
                
                filterable
                reserve-keyword
                placeholder="选择上级"
                :loading="loading">
                <el-option
                  v-for="item in organizationlist"
                  :key="item.id"
                  :label="item.title"
                  :value="item.id">
                  
                </el-option>
              </el-select>
          </el-form-item>
          <el-form-item label="分类名称:" prop="title">
              <el-input
              clearable
              :maxlength="20"
              v-model="form.title"
              placeholder="请输入分类名称"
            />
          </el-form-item>
          
      
    </el-form>
    <template v-slot:footer>
      <el-button @click="updateVisible(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script>


  import EleImageUpload from 'ele-admin/es/ele-image-upload';
  import request from '@/utils/request';
 import {
    addCate
  } from '@/api/file';
  const DEFAULT_FORM = {
    title: '',
    id:null,
    pid:null
  };

  export default {
    name: 'PicEdit',
    components: {  EleImageUpload},
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object,
      // 上级id
      parentId: String,
      // 机构数据
      organizationlist: Array
    },
    data() {
      return {
        images: [],
        img:[],
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          title: [
            {
              required: true,
              message: '请输入分类名称',
              trigger: 'blur'
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
     
          const data = {
            ...this.form,
            pid:this.parentId
          };
          
           addCate(data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(msg);
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      onUpload(item){
         console.log('item:', item);
          item.status = 'uploading';
          const formData = new FormData();
          formData.append('file', item.file);
          request({
              url: '/common/upload',
              method: 'post',
              data: formData,
              onUploadProgress: (e) => {  // 文件上传进度回调
                  if (e.lengthComputable) {
                      item.progress = e.loaded / e.total * 100;
                  }
              }
          }).then((res) => {
              if(res.data.code === 0) {
                  item.status = 'done';
                  item.url = res.data.data.url;
                  // 如果你上传的不是图片格式, 建议将 url 字段作为缩略图, 再添加其它字段作为最后提交数据
                  //item.url = res.data.data.thumbnail;  // 也可以不赋值 url 字段, 默认会显示为一个文件图标
                  item.fileUrl = res.data.data.url;
              }
          }).catch((e) => {
              item.status = 'exception';
          });
      }
    },
    watch: {
      visible(visible) {
        if (visible) {
     
          if (this.data) {
            this.$util.assignObject(this.form, this.data);
            
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
             this.img = [];
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
         
        }
      }
    }
  };
</script>
