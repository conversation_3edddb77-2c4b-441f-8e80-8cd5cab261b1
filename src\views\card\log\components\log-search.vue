<!-- 搜索表单 -->
<template>
  <el-form
    label-width="77px"
    class="ele-form-search"
    @keyup.enter.native="search"
    @submit.native.prevent
  >
    <el-row :gutter="15">
      <el-col :lg="4" :md="12">
        <el-form-item label="订单编号:">
          <el-input clearable v-model="where.log_no" placeholder="请输入订单编号" />
        </el-form-item>
      </el-col>

      <el-col :lg="4" :md="12">
        <el-form-item label="用户:">
          <el-input clearable v-model="where.nickname" placeholder="请输入用户" />
        </el-form-item>
      </el-col>

      <el-col :lg="4" :md="12">
        <el-form-item label="合伙人:">
          <el-input clearable v-model="where.partner" placeholder="请输入合伙人" />
        </el-form-item>
      </el-col>

      <el-col :lg="4" :md="12">
        <el-form-item label="商品:">
          <el-input clearable v-model="where.goods_name" placeholder="请输入商品" />
        </el-form-item>
      </el-col>
      <el-col :lg="4" :md="12">
        <div class="ele-form-actions">
          <el-button
            type="primary"
            icon="el-icon-search"
            class="ele-btn-icon"
            @click="search"
          >
            查询
          </el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </el-col>
     
    </el-row>
   
  </el-form>
</template>

<script>
  const DEFAULT_WHERE = {
    log_no: '',
    nickname:'',
    partner:'',
    goods_name:'',
  };

  export default {
    name: 'BrokerageSearch',
    data() {
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE },
        dateRange: [],
        // 日期时间选择器快捷项
        pickerOptions: {
          shortcuts: [
            {
              text: '最近一周',
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '最近一个月',
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '最近三个月',
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                picker.$emit('pick', [start, end]);
              }
            }
          ]
        }
      };
    },
    methods: {
      handleChange(){
        this.search();
      },
      /* 搜索 */
      search() {
        const [d1, d2] = this.dateRange ?? [];
        this.$emit('search', {
          ...this.where,
          start_time: d1 ? d1  : '',
          end_time: d2 ? d2  : ''
        });
      },
      /*  重置 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.dateRange = [];
        this.search();
      }
    }
  };
</script>
