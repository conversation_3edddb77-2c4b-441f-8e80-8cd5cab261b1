import axios from '@/utils/request';


/**
 * 查询代理佣金提现统计
 * @param params 查询条件
 */
 export async function brokerageStatis(params) {
  const res = await axios.get('/figure.brokerage/statis', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 查询代理佣金提现记录
 * @param params 查询条件
 */
 export async function brokerageList(params) {
  const res = await axios.get('/figure.brokerage/index', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


export async function shareholderLog(params) {
  const res = await axios.get('/figure.brokerageLog/shareholderLog', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 通过提现
 * @param data 数据
 */
 export async function editSubmit(data) {
  const res = await axios.post('/figure.brokerage/edit_submit', data);
  if (res.data.code === 0) {
      return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 驳回提现
 * @param data 数据
 */
export async function rejectSubmit(data) {
    const res = await axios.post('/figure.brokerage/reject_submit', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 查询代理佣金记录
 * @param params 查询条件
 */
export async function brokerageLog(params) {
  const res = await axios.get('/figure.brokerageLog/index', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 删除
 * @param id id
 */
 export async function remove(id) {
  const res = await axios.get('/figure.brokerage/delete?id=' + id);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 查询代理用户分销设置
 * @param params 查询条件
 */
 export async function queryBrokerageSet(params) {
  const res = await axios.get('/figure.brokerage/queryBrokerageSet', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 保存代理用户分销设置
 * @param data 配置数据
 */
export async function saveBrokerageSet(data) {
    const res = await axios.post('/figure.brokerage/saveBrokerageSet', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}



/**
 * 查询代理佣金提现记录
 * @param params 查询条件
 */
export async function levelList(params) {
  const res = await axios.get('/figure.distribution/index', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 添加
 * @param data 机构信息
 */
export async function add(data) {
  const res = await axios.post('/figure.distribution/addDistribution', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 修改
 * @param data 机构信息
 */
export async function update(data) {
  const res = await axios.post('/figure.distribution/editDistribution', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function modify(data) {
  const res = await axios.post('/figure.distribution/modify', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}



