<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <brokerage-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            class="ele-btn-icon"
            @click="removeBatch"
          >
            删除
          </el-button>
        </template>

        <!-- 订单编号 -->
        <template v-slot:log_no="{ row }">
          <span v-if="row.log_no"> {{ row.log_no }}</span>
          <span v-else> -- </span>
        </template>

        <template v-slot:pUser="{ row }">
          <div class="ele-cell" v-if="row.figurePuser">
            <el-avatar  v-if="row.figurePuser.avatar" :src="row.figurePuser.avatar" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.figurePuser.nickname }} </div>
              <div class="ele-cell-desc">{{ row.figurePuser.telphone }} </div>
            </div>
          </div>
        </template>

        <template v-slot:user="{ row }">
          <div class="ele-cell" v-if="row.figureUser">
            <el-avatar v-if="row.figureUser.avatar" :src="row.figureUser.avatar" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.figureUser.nickname }} </div>
              <div class="ele-cell-desc">{{ row.figureUser.telphone }} </div>
            </div>
          </div>
        </template>

        <template v-slot:type="{ row }">
          <ele-dot v-if="row.type==1"  text="邀请好友" :ripple="true" />
          <ele-dot v-else-if="row.type==2"  text="会员分销" :ripple="true" />
          <ele-dot v-else-if="row.type==3"  text="点数分销" :ripple="true" />
        </template>
        <template v-slot:is_repurchase="{ row }">
             <ele-dot v-if="row.type==1"  text="邀请好友" :ripple="true" />
          <ele-dot  v-else-if=" (row.type==2 ||row.type==3 )&& row.is_repurchase==0"  text="首购" :ripple="true" />
          <ele-dot v-else-if="  (row.type==2 ||row.type==3 ) && row.is_repurchase==1"  text="复购" :ripple="true" />

        </template>


        <template v-slot:name="{ row }">
          <span v-if="row.way == 1">{{ row.film_name }}</span>
          <span v-if="row.way == 2">{{ row.name }}</span>
        </template>
        

        
        <!-- 操作列 -->
        <template v-slot:action="{ row }">
          <el-popconfirm
            class="ele-action"
            title="确定要删除此数据吗？"
            @confirm="remove(row)"
          >
            <template v-slot:reference>
              <el-link type="danger" :underline="false" icon="el-icon-delete">
                删除
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
  </div>
</template>

<script>
  import BrokerageSearch from './components/brokerage-search';
  import { brokerageLog, remove } from '@/api/brokerage';

  export default {
    name: 'BrokerageLog',
    props:{
      pid: Number,
      uid: Number,
    },
    components: {
      BrokerageSearch,
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            align: 'center',
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'log_no',
            label: '订单编号',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'log_no'
          },
          {
            prop: 'pUser',
            label: '用户',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'pUser'
          },
         
          {
            prop: 'user',
            label: '来源',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'user'
          },
          {
            prop: 'type',
            label: '类型',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'type',
          },
            {
                prop: 'level_name',
                label: '分销等级',
                showOverflowTooltip: true,
                minWidth: 110,

            },
            {
                prop: 'is_repurchase',
                label: '是否为复购',
                showOverflowTooltip: true,
                minWidth: 110,
                slot: 'is_repurchase',

            },

          
          {
            prop: 'money',
            label: '佣金',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'create_time',
            label: '创建时间',
            showOverflowTooltip: true,
            minWidth: 100,
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 150,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            showOverflowTooltip: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示审核弹窗
        showCheck: false,
        // 是否显示充值弹窗
        showRecharge: false,
        //弹框名称
        id:0,
      };
    },
    methods: {
      beforeHandleCommand(index, row,command){
          
          return {
              'index': index,
              'row': row,
              'command':command
          }
    },
      onDropClick(command) {
        
        switch (command.command) {
            case "a"://编辑
                this.openAddNum(command.row,1);
                break;
            case "b"://充值密码
                this.openAddNum(command.row,2);
                break;
            case "c"://删除
                this.openAddNum(command.row,3);
                break;
            case "d"://删除
                this.openAddNum(command.row,4);
                break;
        }
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        where.pid = this.pid;
        where.uid = this.uid;
        return brokerageLog({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        where = !where ? {} : where;
        where.pid = this.pid;
        where.uid = this.uid;
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },

      /* 打开编辑弹窗 */
      openCheck(row) {
        this.id = row.id;
        this.current = row;
        this.showCheck = true;
      },

      /* 打开编辑弹窗 */
      openRecharge(row) {
        this.id = row.id;
        this.current = row;
        this.showRecharge = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      },
      /* 更新二维码 */
      reloadQrcode(row) {
        const loading = this.$loading({ lock: true });
        reloadQrcode({user_id:row.userId})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      },

      
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的合伙人吗?', '提示', {
          type: 'warning'
        })
          .then(() => {
            const loading = this.$loading({ lock: true });
            remove(this.selection.map((d) => d.id))
              .then((msg) => {
                loading.close();
                this.$message.success(msg);
                this.reload();
              })
              .catch((e) => {
                loading.close();
                this.$message.error(e.message);
              });
          })
          .catch(() => {});
      },
      /* 重置合伙人密码 */
      resetPsw(row) {
        this.$confirm('确定要重置此合伙人的密码为"123456"吗?', '提示', {
          type: 'warning'
        })
          .then(() => {
            const loading = this.$loading({ lock: true });
            resetPassword(row.id)
              .then((msg) => {
                loading.close();
                this.$message.success(msg);
              })
              .catch((e) => {
                loading.close();
                this.$message.error(e.message);
              });
          })
          .catch(() => {});
      },
      /* 更改状态 */
      editStatus(row) {
        const loading = this.$loading({ lock: true });
        modify({id:row.id,field:'is_freeze',value: row.is_freeze})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
          })
          .catch((e) => {
            loading.close();
            row.status = !row.status ? 1 : 0;
            this.$message.error(e.message);
          });
      }
    },
    watch:{
      pid(){
        this.reload();
      },
      uid(){
        this.reload();
      }
    }
  };
</script>
<style lang="scss" scoped>

  .ele-cell-content {
   
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

</style>
