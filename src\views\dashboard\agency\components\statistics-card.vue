<!-- 统计卡片 -->
<template>
  <el-row :gutter="15">
    <el-col :lg="6" :md="12">
      <el-card class="analysis-chart-card" shadow="never">
        <template v-slot:header>
          <div class="ele-cell">
            <div class="ele-cell-content">总销售额</div>
            <el-tooltip content="兑换商城金额 + 积分商城金额" placement="top">
              <i
                class="el-icon-_question ele-text-placeholder"
                style="cursor: pointer"
              >
              </i>
            </el-tooltip>
          </div>
        </template>
        <div class="analysis-chart-card-num ele-text-heading" style="padding-top: 0px">￥ {{saleData.sum_price}}</div>
        <div class="analysis-chart-card-content">
          
        </div>
        <el-divider />
        <div class="analysis-chart-card-text">总销售数量 {{saleData.sum_count}}</div>
      </el-card>
    </el-col>
    <el-col :lg="6" :md="12">
      <el-card class="analysis-chart-card" shadow="never">
        <template v-slot:header>
          <div class="ele-cell">
            <div class="ele-cell-content">回收订单数量</div>
            <el-tag size="mini" type="danger">日</el-tag>
          </div>
        </template>
        <div class="analysis-chart-card-num ele-text-heading">{{saleData.reserve_count}}单</div>
        <div class="analysis-chart-card-content">
          <v-chart
            ref="visitChart"
            style="height: 40px"
            :option="visitChartOption"
          />
        </div>
        <el-divider />
        <div class="analysis-chart-card-text">日回收订单量 {{saleData.day_reserve_count}}</div>
      </el-card>
    </el-col>
    <el-col :lg="6" :md="12">
      <el-card class="analysis-chart-card" shadow="never">
        <template v-slot:header>
          <div class="ele-cell">
            <div class="ele-cell-content">充值金额</div>
            <el-tag size="mini">月</el-tag>
          </div>
        </template>
        <div class="analysis-chart-card-num ele-text-heading">￥{{saleData.cz_price}}</div>
        <div class="analysis-chart-card-content">
          <v-chart
            ref="payNumChart"
            style="height: 40px"
            :option="payNumChartOption"
          />
        </div>
        <el-divider />
        <div class="analysis-chart-card-text">日充值金额 ￥{{saleData.day_cz_price}}</div>
      </el-card>
    </el-col>
    <el-col :lg="6" :md="12">
      <el-card class="analysis-chart-card" shadow="never">
        <template v-slot:header>
          <div class="ele-cell">
            <div class="ele-cell-content">用户数量</div>
           
          </div>
        </template>
        <div class="analysis-chart-card-num ele-text-heading" style="padding-top: 0px"> {{saleData.u_count}}人</div>
        <div class="analysis-chart-card-content" >
         
        </div>
        <el-divider />
        <div class="analysis-chart-card-text">日增长量 {{saleData.day_u_count}}位</div>
     
      
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { LineChart, BarChart } from 'echarts/charts';
  import { GridComponent, TooltipComponent } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { getAgencySaleData} from '@/api/dashboard/analysis';
  import { echartsMixin } from '@/utils/echarts-mixin';

  
 

  use([CanvasRenderer, LineChart, BarChart, GridComponent, TooltipComponent]);

  export default {
    name: 'StatisticsCard',
    components: { VChart },
    mixins: [echartsMixin(['visitChart', 'payNumChart'])],
    data() {
      return {
        // 访问量折线图配置
        visitChartOption: {},
        // 支付笔数柱状图配置
        payNumChartOption: {},
        saleData:{
          sum_price:0,
          t_sum_price:0,
          tg_count:0,
          day_tg_count:0,
          cz_price:0,
          day_cz_price:0,
          u_count:0,
          day_u_count:0
        },
        payCount:0,
        videoData:{
          count:0,
          d_count:0
        },
        mealData:{
          sum_price:0,
          cur_count:0,
          sum_count:0
        }

      };
    },
    created() {
      this.getPayNumData();
    },
    methods: {
      /* 获取数据 */
      getPayNumData() {
        
        getAgencySaleData().then((data) => {

          this.saleData = data;

          this.visitChartOption = {
              color: '#975fe5',
              tooltip: {
                trigger: 'axis',
                formatter:
                  '<i class="ele-chart-dot" style="background: #975fe5;"></i>{b0}: {c0}'
              },
              grid: {
                top: 10,
                bottom: 0,
                left: 0,
                right: 0
              },
              xAxis: [
                {
                  show: false,
                  type: 'category',
                  boundaryGap: false,
                  data: data.date
                }
              ],
              yAxis: [
                {
                  show: false,
                  type: 'value',
                  splitLine: {
                    show: false
                  }
                }
              ],
              series: [
                {
                  type: 'line',
                  smooth: true,
                  symbol: 'none',
                  areaStyle: {
                    opacity: 0.5
                  },
                  data: data.reserve_value
                }
              ]
            };

            this.payNumChartOption = {
              tooltip: {
                trigger: 'axis',
                formatter:
                  '<i class="ele-chart-dot" style="background: #5b8ff9;"></i>{b0}: {c0}'
              },
              grid: {
                top: 10,
                bottom: 0,
                left: 0,
                right: 0
              },
              xAxis: [
                {
                  show: false,
                  type: 'category',
                  data: data.month
                }
              ],
              yAxis: [
                {
                  show: false,
                  type: 'value',
                  splitLine: {
                    show: false
                  }
                }
              ],
              series: [
                {
                  type: 'bar',
                  data:data.recharge_value
                }
              ]
            };
        })
        .catch((e) => {
          this.$message.error(e.message);
        });
      }
    }
  };
</script>

<style scoped>
  .analysis-chart-card-num {
    font-size: 30px;
  }

  .analysis-chart-card-content {
    height: 40px;
    box-sizing: border-box;
    margin-bottom: 12px;
  }

  .analysis-chart-card-text {
    padding-top: 12px;
  }
</style>
