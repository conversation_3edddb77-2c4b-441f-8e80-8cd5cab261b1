<template>
  <el-card shadow="never" header="商户增长记录">
    <el-row>

        <v-chart
          ref="saleChart"
          style="height: 444px"
          :option="userCountMapOption"
        />
      
   
      
    </el-row>
  </el-card>
</template>

<script>
  import {  getProxyTbSaleData,getProxyRankSaleData} from '@/api/dashboard/analysis';
  import { use, registerMap } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import {  BarChart } from 'echarts/charts';
  import {
    VisualMapComponent,
    GeoComponent,
    TooltipComponent,
    GridComponent
  } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { getChinaMapData, getUserCountList } from '@/api/dashboard/monitor';
  import { echartsMixin } from '@/utils/echarts-mixin';

  use([
    CanvasRenderer,
    BarChart,
    VisualMapComponent,
    GeoComponent,
    TooltipComponent,
    GridComponent
  ]);
  export default {
    name: 'MapCard',
    components: { VChart },
    mixins: [echartsMixin(['saleChart'])],
    data() {
      return {
        // 用户分布地图配置
        userCountMapOption: {},
        // 用户分布前10名
        userCountDataRank: [],
        saleroomData: [],
      };
    },
    created() {
      this.getSaleroomData();
    },
    methods: {
      /* 获取销售量数据 */
      getSaleroomData() {
        getProxyTbSaleData()
              .then((data) => {
                debugger;
                this.saleroomData = data.car_data;
                this.onSaleTypeChange();
                console.log(data);
              })
              .catch((e) => {
                this.$message.error(e.message);
              });
          
      },
      /* 销售量tab选择改变事件 */
      onSaleTypeChange() {
        const data =  this.saleroomData;
        this.userCountMapOption = {
          tooltip: {
            trigger: 'axis'
          },
          xAxis: [
            {
              type: 'category',
              data: data.map((d) => d.month)
            }
          ],
          yAxis: [
            {
              type: 'value'
            }
          ],
          series: [
            {
              type: 'bar',
              data: data.map((d) => d.value)
            }
          ]
        };
      },

      /* 获取用户分布数据 */
      getUserCountData() {
        getUserCountList()
          .then((data) => {
            const temp = data.sort((a, b) => b.value - a.value);
            const min = temp[temp.length - 1].value || 0;
            const max = temp[0].value || 1;
            //
            const list = temp.length > 10 ? temp.slice(0, 15) : temp;
            this.userCountDataRank = list.map((d) => {
              return {
                name: d.name,
                value: d.value,
                percent: (d.value / max) * 100
              };
            });
            //
            this.userCountMapOption = {
              tooltip: {
                trigger: 'item'
              },
              visualMap: {
                min: min,
                max: max,
                text: ['高', '低'],
                calculable: true,
                color: ['#1890FF', '#EBF3FF']
              },
              series: [
                {
                  name: '用户数',
                  label: {
                    show: true
                  },
                  type: 'map',
                  map: 'china',
                  data: data
                }
              ]
            };
          })
          .catch((e) => {
            this.$message.error(e.message);
          });
      }
    }
  };
</script>

<style scoped>
  /* 人数分布排名 */
  .monitor-user-count-item {
    margin-bottom: 8px;
  }

  .monitor-user-count-item ::v-deep .el-progress-bar__outer {
    background: none;
  }

  .monitor-user-count-item .ele-cell-content {
    padding-right: 10px;
  }
</style>
