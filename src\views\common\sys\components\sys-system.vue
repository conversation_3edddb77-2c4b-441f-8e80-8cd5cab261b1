<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">平台配置</div>
      <div class="ele-page-desc">
        用于前端平台相关信息配置。
      </div>
    </div>
    <div class="ele-body" style="padding-bottom: 71px">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="130px"
        @keyup.enter.native="submit"
        @submit.native.prevent
      >
        <el-card shadow="never" header="系统配置" body-style="padding: 22px 22px 0 22px;">
          <el-row :gutter="15">
            <el-col :lg="8" :md="8">
              <el-form-item label="系统logo:" prop="logo">
                <span slot="label">
                  系统logo
                  <el-tooltip placement="top">
                    <div slot="content">
                        用于授权时展示<br/>
                        大小尺寸为100px * 100px
                    </div>
                    <i class="el-icon-question" />
                  </el-tooltip>
                </span>
               
                 <div class="ele-image-upload-list">
                  <div class="ele-image-upload-item ele-image-upload-button" @click="modalPicTap('dan','logo','系统logo')">
                    <div>
                      <div tabindex="0" class="el-upload el-upload--text">

                        <div class="el-upload-dragger">
                          <i class="el-icon-plus ele-image-upload-icon"></i>
                        </div>

                        <div class="ele-image-upload-item" style="margin:0 0 0 0;" v-if="form.logo!=''">
                          <div class="el-image" >
                            <img :src="form.logo" width="100%" height="100%"  class="el-image__inner" style="object-fit: cover;">
                          </div>
                          <div class="ele-image-upload-close" @click="handleRemove('logo')"><i class="el-icon-close"></i></div>
                        </div>

                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </el-col>

            <el-col :lg="8" :md="12">
              <el-form-item label="系统名称:" label-width="130px" prop="name">
                <el-input v-model="form.name" placeholder="请输入系统名称" clearable/>
              </el-form-item>

            </el-col>
            
          </el-row>
          
        </el-card>


        <el-card shadow="never" header="小程序配置" body-style="padding: 22px 22px 0 22px;">
          <el-row :gutter="15">
            <el-col :lg="8" :md="12">
              <el-form-item label="appid:" prop="appid">
                 <el-input v-model="form.appid" placeholder="请输入小程序appid" clearable/>
              </el-form-item>
              <el-form-item label="appsecret:" prop="appsecret">
                <el-input show-password v-model="form.appsecret" type="password" placeholder="请输入小程序appsecret" clearable/>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12">
              <el-form-item label="流量主广告id:" prop="banner_id">
                 <el-input v-model="form.banner_id" placeholder="请输入流量主广告" clearable/>
              </el-form-item>
            </el-col>
          </el-row>
          
        </el-card>

        <el-card shadow="never" header="分享配置" body-style="padding: 22px 22px 0 22px;">
          <el-row :gutter="15">
            <el-col :lg="8" :md="10">
              <el-form-item label="分享图片:" prop="share_pic">
                <span slot="label">
                  分享图片
                  <el-tooltip placement="top">
                    <div slot="content">
                        用于分享时的图片选择<br/>
                        大小尺寸跟分辨率同比例
                    </div>
                    <i class="el-icon-question" />
                  </el-tooltip>
                </span>
                <!--<ele-image-upload  v-model="form.site_login_background" :limit="1" :drag="true" :multiple="false"  @upload="onUpload" />-->
                 <div class="ele-image-upload-list">
                  <div class="ele-image-upload-item ele-image-upload-button" @click="modalPicTap('dan','share_pic','分享图片')">
                    <div>
                      <div tabindex="0" class="el-upload el-upload--text">

                        <div class="el-upload-dragger">
                          <i class="el-icon-plus ele-image-upload-icon"></i>
                        </div>

                        <div class="ele-image-upload-item" style="margin:0 0 0 0;" v-if="form.share_pic!=''">
                          <div class="el-image" >
                            <img :src="form.share_pic" width="100%" height="100%"  class="el-image__inner" style="object-fit: cover;">
                          </div>
                          <div class="ele-image-upload-close" @click="handleRemove('share_pic')"><i class="el-icon-close"></i></div>
                        </div>

                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            
            <el-col :lg="8" :md="10">
              <el-form-item label="分享标题:" prop="share_title">
                <el-input v-model="form.share_title" placeholder="请输入分享标题" clearable/>
              </el-form-item>
              <el-form-item label="分享描述:" prop="share_desc">
                <el-input v-model="form.share_desc" placeholder="请输入分享描述" clearable/>
              </el-form-item>
            </el-col>


          </el-row>
          <el-row :gutter="15">
            <el-col :lg="10" :md="8">
              <el-form-item>
                <el-button type="primary" :loading="loading" @click="submit">
                  提交
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        
       
        <uploadPictures
          :isChoice="isChoice"
          :visible.sync="modalPic"
           @getPic="getPic"
          :gridBtn="gridBtn"
          :gridPic="gridPic"
          :title="modalTitle"
        ></uploadPictures>

        <!-- 底部工具栏 -->
        <!-- <div class="ele-bottom-tool">
          <div v-if="validMsg" class="ele-text-danger">
            <i class="el-icon-circle-close"></i>
            <span>{{ validMsg }}</span>
          </div>
          <div class="ele-bottom-tool-actions">
            <el-button type="primary" :loading="loading" @click="submit">
              提交
            </el-button>
          </div>
        </div> -->
      </el-form>
    </div>
  </div>
  
</template>

<script>
import uploadPictures from "@/components/uploadPictures";
import { saveSystem ,querySystem } from '@/api/system/config';
  const DEFAULT_FORM = {
    logo: '',
    name:'',
    appid:'',
    appsecret:'',
    balance_name:'',
    brokerage_name:'',
    banner_id:'',
    share_pic:'',
    share_title:'',
    admin_phone:'',
  };
import EleImageUpload from 'ele-admin/es/ele-image-upload';
import request from '@/utils/request';
  export default {
    name: 'FormAdvanced',
    components: { EleImageUpload,uploadPictures },
    data() {
      return {
        modalTitle:'',
        modalPic: false,
        isChoice: "单选",
        gridBtn: {
          xl: 4,
          lg: 8,
          md: 8,
          sm: 8,
          xs: 8,
        },
        gridPic: {
          xl: 6,
          lg: 8,
          md: 12,
          sm: 12,
          xs: 12,
        },
        // 表单提交状态
        loading: false,
        // 表单数据
        form: {
          ...DEFAULT_FORM
        },
        // 表单验证规则
        rules: {
          name: [
            {
              required: true,
              message: '请输入仓库名',
              trigger: 'blur'
            }
          ]
        },
        // 表单验证信息
        validMsg: ''
      };
    },
    mounted() {
      querySystem().then((data) => {
        if(data != null){
          this.form = data;
        }
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    methods: {
      // 选择图片
      modalPicTap(tit, picTit,openTitle) {
        this.modalTitle = openTitle;
        this.isChoice = tit === "dan" ? "单选" : "多选";
        this.picTit = picTit;
        this.modalPic = true;
      },
      // 选中图片
      getPic(pc) {
        switch (this.picTit) {
          case "logo":
            this.form.logo = pc.satt_dir;
            break;
          case "qrcode":
            this.form.qrcode = pc.satt_dir;
            break;
          case "share_pic":
            this.form.share_pic = pc.satt_dir;
            break;
        }
        this.modalPic = false;
      },
      //删除图片
      handleRemove(field) {
        this.form[field] = '';
      },
      /* 表单提交 */
      submit() {
        this.$refs['form'].validate((valid, obj) => {
          if (valid) {
            this.validMsg = '';
            this.loading = true;
            const data = {
            ...this.form
            };
            saveSystem(data).then((msg) => {
              this.loading = false;
              this.$message.success(msg);
            }).catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });
          } else {
            this.validMsg = ` 共有校验 ${Object.keys(obj).length} 项不通过`;
            return false;
          }
        });
      },
      onUpload(item){
         console.log('item:', item);
          item.status = 'uploading';
          const formData = new FormData();
          formData.append('file', item.file);
          request({
              url: '/common/uploadFile',
              method: 'post',
              data: formData,
              onUploadProgress: (e) => {  // 文件上传进度回调
                  if (e.lengthComputable) {
                      item.progress = e.loaded / e.total * 100;
                  }
              }
          }).then((res) => {
              if(res.data.code === 0) {
                  item.status = 'done';
                  item.url = res.data.data.url;
                  // 如果你上传的不是图片格式, 建议将 url 字段作为缩略图, 再添加其它字段作为最后提交数据
                  //item.url = res.data.data.thumbnail;  // 也可以不赋值 url 字段, 默认会显示为一个文件图标
                  item.fileUrl = res.data.data.url;
              }
          }).catch((e) => {
              item.status = 'exception';
          });
      }
    }
  };
</script>
