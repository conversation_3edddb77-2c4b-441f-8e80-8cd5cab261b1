<!-- 机构编辑弹窗 -->
<template>
  <ele-modal
    width="920px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? '修改形象' : '添加形象'"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form"  label-width="100px">
      <el-row :gutter="15">
        <el-col :sm="12">

          <el-form-item label="选择形象" prop="avatar_id">
            <avatar-select
              placeholder="请选择形象"
              v-model="form.avatar_id"
              @done='updateAvatar'
            />
          </el-form-item>
         
          <el-form-item label="选择声音" prop="voice_id">
            <voice-select
              placeholder="请选择声音"
              v-model="form.voice_id"
              @done='updateVoice'
            />
          </el-form-item>

          <el-form-item label="排序号:" prop="sort">
            <el-input-number
              :min="0"
              v-model="form.sort"
              placeholder="请输入排序号"
              controls-position="right"
              class="ele-fluid ele-text-left"
            />
          </el-form-item>

          <el-form-item label="展示状态:">
            <el-switch
              :active-value="1"
              :inactive-value="2"
              v-model="form.is_display"
            />
            <el-tooltip
              placement="top"
              content="选择不可见则前端页面不显示该数据"
            >
              <i
                class="el-icon-_question"
                style="vertical-align: middle; margin-left: 8px"
              ></i>
            </el-tooltip>
          </el-form-item>

        </el-col>
        <el-col :sm="12">
          <el-form-item label="视频:" prop="avatar_video">
            <div class="ele-image-upload-list">
              <div class="ele-image-upload-item ele-image-upload-button" @click="modalPicTap('dan','avatar_video','图片')">
                <div>
                  <div tabindex="0" class="el-upload el-upload--text">
                    <div class="el-upload-dragger">
                      <i class="el-icon-plus ele-image-upload-icon"></i>
                    </div>
                      <div class="ele-image-upload-item" style="margin:0 0 0 0;" v-if="form.avatar_video!=''">
                      <div class="el-image" >
                        <video width="320" height="200" controls :src="form.avatar_video"  v-if="form.avatar_video"> </video>
                      </div>
                      <div class="ele-image-upload-close" @click="handleRemove()"><i class="el-icon-close"></i></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      
    </el-form>
    <template v-slot:footer>
      <el-button @click="updateVisible(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>

    <uploadPictures
      fileType="video"
      :isChoice="isChoice"
      :visible.sync="modalPic"
      @getPic="getPic"
      :gridBtn="gridBtn"
      :gridPic="gridPic"
      :title="modalTitle"
    ></uploadPictures>
    
  </ele-modal>
 
</template>

<script>
  import AvatarSelect from "@/views/avatar/components/avatar-select";
  import VoiceSelect from "@/views/voiceTwin/components/voice-select";
  import uploadPictures from "@/components/uploadPictures";
  import { add,update } from '@/api/screen/avatar';
  const DEFAULT_FORM = {
    id:0,
    avatar_id:0,
    avatar_img:'',
    avatar_video:'',
    voice_id:0,
    voice_mode:0,
    is_display :1,
    sort :0
  };

  export default {
    name: 'AvatarEdit',
    components: { AvatarSelect,VoiceSelect,uploadPictures },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data:[]
    },
    data() {
      return {
        modalTitle:'选择视频',
        modalPic: false,
        isChoice: "单选",
        gridBtn: {
          xl: 4,
          lg: 8,
          md: 8,
          sm: 8,
          xs: 8,
        },
        gridPic: {
          xl: 6,
          lg: 8,
          md: 12,
          sm: 12,
          xs: 12,
        },
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          name: [
            {
              required: true,
              message: '请输入服务名称',
              trigger: 'blur'
            }
          ]
        },
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          avatar_id: [
            {
              required: true,
              message: '请选择形象',
              trigger: 'blur'
            }
          ],
          voice_id: [
            {
              required: true,
              message: '请选择声音',
              trigger: 'blur'
            }
          ],
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      // 选择图片
      modalPicTap(tit, picTit,openTitle) {
        this.modalTitle = openTitle;
        this.isChoice = tit === "dan" ? "单选" : "多选";
        this.picTit = picTit;
        this.modalPic = true;
      },
      // 选中图片
      getPic(pc) {
        switch (this.picTit) {
          case "avatar_video":
            this.form.avatar_video = pc.satt_dir;
            break;
        }
        this.modalPic = false;
      },
      //删除图片
      handleRemove() {
        this.form.avatar_video = '';
      },
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;

  
          const data = {
            ...this.form
          };

          const saveOrUpdata = this.isUpdate ? update : add;
          
          saveOrUpdata(data).then((msg) => {
            this.loading = false;
            this.$message.success(msg);
            this.updateVisible(false);
            this.$emit('done');
          }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
          });
        });
      },
      /* 更新visible */
      updateAvatar(object) {
        this.form.avatar_id = object.id;
        this.form.avatar_img = object.decode_img;
      },
      /* 更新visible */
      updateVoice(object) {
        this.form.voice_id = object.id;
        this.form.voice_mode = object.train_mode;
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      }
    },
    watch: {
      visible(visible) {
        if (visible) {
          if (this.data) {
            this.form = { ...DEFAULT_FORM };
            this.$util.assignObject(this.form, this.data);
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>


<style lang="scss" scoped>

  .ele-image-upload-list .ele-image-upload-item {
    width: 340px;
    height: 220px;
  }
</style>
