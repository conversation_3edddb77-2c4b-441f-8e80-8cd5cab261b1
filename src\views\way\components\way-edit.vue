<!-- 用户编辑弹窗 -->
<template>
    <ele-modal
            width="740px"
            :visible="visible"
            :append-to-body="true"
            :close-on-click-modal="false"
            custom-class="ele-dialog-form"
            title="修改"
            @update:visible="updateVisible"
    >
        <el-form ref="form" :model="form"  label-width="140px">

            <el-form-item label="小程序展示名称:" prop="name">
                <el-input
                        v-model="form.name"
                        placeholder="小程序展示名称"
                />
            </el-form-item>

           






            <el-form-item label="排序号:" prop="sort">
                <el-input-number
                        :min="0"
                        v-model="form.sort"
                        placeholder="请输入排序号"
                        controls-position="right"
                        class="ele-fluid ele-text-left"
                />
            </el-form-item>

         

            <el-form-item label="是否启用:">
                <el-switch
                        :active-value="1"
                        :inactive-value="2"
                        v-model="form.status"
                />
                <el-tooltip
                        placement="top"
                        content="选择不可见则前端页面不使用此线路"
                >
                    <i
                            class="el-icon-_question"
                            style="vertical-align: middle; margin-left: 8px"
                    ></i>
                </el-tooltip>
            </el-form-item>


        </el-form>
        <template v-slot:footer>
            <el-button @click="updateVisible(false)">取消</el-button>
            <el-button type="primary" :loading="loading" @click="save"
            >保存
            </el-button>
        </template>
     
    </ele-modal>
</template>

<script>
    
   

    import {add, update} from '@/api/way/list';
    import {getPages} from '@/api/layout';

    const DEFAULT_FORM = {
        id: 0,
        reception: '',
       
        status: 1,
 
        sort: 1,
     
    };

    export default {
        name: 'MemberEdit',
        components: {},
        props: {
            // 弹窗是否打开
            visible: Boolean,
            // 修改回显的数据
            data: Object
        },
        computed: {
            balanceName() {
                return this.$store.state.user.balance_name;
            }
        },
        data() {
            return {
                modalTitle: '选择图片',
                modalPic: false,
                isChoice: "单选",
                gridBtn: {
                    xl: 4,
                    lg: 8,
                    md: 8,
                    sm: 8,
                    xs: 8,
                },
                gridPic: {
                    xl: 6,
                    lg: 8,
                    md: 12,
                    sm: 12,
                    xs: 12,
                },
                // 表单数据
                form: {...DEFAULT_FORM},
                // 表单验证规则
                rules: {
                   
                },
                // 提交状态
                loading: false,
                // 是否是修改
                isUpdate: false,
                tableConfig: {
                    datasource: ({page, limit, where, order}) => {
                        return getPages({...where, ...order, page, limit});
                    },
                    columns: [
                        {
                            columnKey: 'index',
                            type: 'index',
                            width: 45,
                            align: 'center'
                        },
                        {
                            prop: 'name',
                            label: '名称',
                            showOverflowTooltip: true,
                            minWidth: 110,
                            //slot: 'nickname'
                        },
                        {
                            prop: 'url',
                            label: '路径',
                            showOverflowTooltip: true,
                            minWidth: 110
                        }
                    ],
                    rowClickChecked: true,
                    rowClickCheckedIntelligent: false,
                    toolkit: ['reload', 'columns'],
                    size: 'small',
                    toolStyle: {padding: '0 10px'}
                }
            };
        },
        methods: {
            updateSecond() {
                if (this.form.second_infinite == 1) {
                    this.form.second = 0;
                }

            },
            search(where) {
                // debugger
                this.$refs.select.reload({
                    where: where,
                    page: 1
                });
            },
            // 选择图片
            modalPicTap(tit, picTit, openTitle) {
                this.modalTitle = openTitle;
                this.isChoice = tit === "dan" ? "单选" : "多选";
                this.picTit = picTit;
                this.modalPic = true;
            },
            // 选中图片
            getPic(pc) {
                switch (this.picTit) {
                    case "pic_url":
                        this.form.pic_url = pc.satt_dir;
                        break;
                }
                this.modalPic = false;
            },
            //删除图片
            handleRemove() {
                this.form.pic_url = '';
            },

            /* 保存编辑 */
            save() {
                this.$refs['form'].validate((valid) => {
                    if (!valid) {
                        return false;
                    }

                    this.loading = true;
                    const data = {
                        ...this.form,
                    };

                    const saveOrUpdata = this.isUpdate ? update : add;

                    saveOrUpdata(data).then((msg) => {
                        this.loading = false;
                        this.$message.success(msg);
                        this.updateVisible(false);
                        this.$emit('done');
                    }).catch((e) => {
                        this.loading = false;
                        this.$message.error(e.message);
                    });
                });
            },
            /* 更新visible */
            updateVisible(value) {
                this.$emit('update:visible', value);
            }
        },
        watch: {
            visible(visible) {
                if (visible) {
                    if (this.data) {

                        this.$util.assignObject(this.form, {
                            ...this.data
                        });
                        this.isUpdate = true;
                    } else {
                        this.isUpdate = false;
                    }
                } else {
                    this.$refs['form'].clearValidate();
                    this.form = {...DEFAULT_FORM};
                }
            }
        }
    };
</script>
