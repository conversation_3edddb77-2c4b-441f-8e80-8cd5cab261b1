<template>
  <div class="ele-body">
    <el-card shadow="never">

      <!-- 搜索表单 -->
      <video-search @search="reload" />
      <!-- 数据表格 -->
     <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
       
        <template v-slot:user="{row}">
            <el-avatar v-if="row.avatar" :src="row.avatar" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.nickname }} </div>
          </div>
        </template>

       
       
        
        <template v-slot:status="{ row }">
          <ele-dot v-if="row.status==1" type="warning" effect="plain" text="等待合成" :ripple="true" />
             <ele-dot v-if="row.status==2" type="warning" effect="plain" text="合成中" :ripple="true" />
          <ele-dot v-if="row.status==3" type="warning" effect="plain" text="合成中" :ripple="true" />
            <ele-dot v-if="row.status==4" type="danger" effect="plain" text="失败" :ripple="true" />
            <ele-dot v-if="row.status==5" type="success" effect="plain" text="成功" :ripple="true" />

        </template>

          <template v-slot:complete_cover="{ row }">
             <el> <img  width="160" height="240" controls :src="row.complete_cover"  > </el>
   
        </template>


        <template v-slot:complete_video="{ row }">
            
            <el> <video  width="200" height="240" controls :src="row.complete_video" > </video></el>
    
        </template>
         <template v-slot:action="{ row }">

          <el-link
            type="success"
            :underline="false"
            icon="el-icon-position"
            @click="openRepost(row)"
          >
            转发
          </el-link>
        </template>
      
      
      </ele-pro-table>
    </el-card>
      <ele-modal 
      width="80%"
      :close-on-click-modal="false"
      custom-class="ele-dialog-form"
      :title="videoName + '转发数据'"
      :visible.sync="showRepost">

      
      <el-row>
        <el-col>
          <el-form
            label-width="130px"
            style="margin-bottom: -15px;"
          >
            <el-row :gutter="20" >
              <el-col :md="12" :sm="12" :xs="12"  v-if="current && current.dy_qrcode">
                <el-form-item label="D音二维码" >
                  <el-image
                    style="width: 80px; height: 80px"
                    :src="current.dy_qrcode" 
                    :preview-src-list="dyPic"
                    :zIndex="9999"
                  />
                </el-form-item>
              </el-col>

              <el-col :md="12" :sm="12" :xs="12"  v-if="current && current.ks_qrcode">
                <el-form-item label="K手二维码" >
                  <el-image
                    style="width: 80px; height: 80px"
                    :src="current.ks_qrcode" 
                    :preview-src-list="ksPic"
                    :zIndex="9999"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <video-repost :data="current" :videoId="id" @done="reload"/>
        </el-col>
      </el-row>
    </ele-modal> 

  </div>
</template>

<script>


  import VideoSearch from './components/video-search';
    import VideoRepost from "@/views/clip/repost";
  import { video } from '@/api/clip/index.js';

  export default {
    name: 'Video',
    components: {
      VideoSearch,
      VideoRepost
     
    },
   data() {
      return {
        // 表格列配置
        columns: [
        
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            align: 'center',
            showOverflowTooltip: true,
            fixed: 'left'
          },

            {
                prop: 'mission_id',
                label: '剪辑任务id',
                showOverflowTooltip: true,
                minWidth: 110,
            },
           {
            prop: 'title',
            label: '标题',
            showOverflowTooltip: true,
            minWidth: 110,
           },
          {
            prop: 'content',
            label: '字幕内容',
            showOverflowTooltip: true,
            minWidth: 110,
          
          },
          
            {
            prop: 'nickname',
            label: '剪辑用户',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'user',
          },
         
           {
            prop: 'complete_cover',
            label: '封面图',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 210,
             slot: 'complete_cover',
            
          },
        
          {
            prop: 'complete_video',
            label: '视频',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 280,
             slot: 'complete_video',
            
          },
            
          
          {
            prop: 'status',
            label: '状态',
            align: 'center',
            width: 150,
            resizable: false,
            slot: 'status',
            showOverflowTooltip: true
          },
          {
            prop: 'create_time',
            label: '时间',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 100,
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 150,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            showOverflowTooltip: true
          }
          
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示审核弹窗
        showCheck: false,
        // 是否显示充值弹窗
        showRecharge: false,
         showRepost: false,
        //弹框名称
        id:0,
      };
    },
    methods: {
      beforeHandleCommand(index, row,command){

          return {
              'index': index,
              'row': row,
              'command':command
          }
    },
    
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return video({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
       openRepost(row) {
        this.id = row.id;
        this.current = row;
        this.showRepost = true;
        this.videoName = row.title;
        this.dyPic = [
          row.dy_qrcode
        ];
        this.ksPic = [
          row.ks_qrcode
        ];
      },

      /* 打开编辑弹窗 */
      openCheck(row) {
        this.id = row.id;
        this.current = row;
        this.showCheck = true;
      },

      /* 打开编辑弹窗 */
      openRecharge(row) {
        this.id = row.id;
        this.current = row;
        this.showRecharge = true;
      },
     
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      },
      /* 更新二维码 */
    

     
      
      /* 更改状态 */
      editStatus(row,statusValue) {
        const loading = this.$loading({ lock: true });
        updateGoodsStatus({id:row.id,field:'status',value: statusValue})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
              this.reload();
          })
            .catch((e) => {
              loading.close();
              this.$message.error(e.message);
            });
         
      }
    }
  };
</script>
