<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">克隆设置</div>
    </div>
    <div class="ele-body" style="padding-bottom: 71px;background: var(--color-white);">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="160px"
        @keyup.enter.native="submit"
        @submit.native.prevent
      >
        <el-card shadow="never" body-style="padding: 22px 22px 0 22px;">

          <el-row>

            <el-col :lg="8" :md="12">
           

              <el-form-item  label="声音克隆高保真开关:" prop="voice_high_open">
                <el-radio-group  v-model="form.voice_high_open" size="small">
                  <el-radio-button :label=1 :value=1>开启</el-radio-button>
                  <el-radio-button :label=2 :value=2>关闭</el-radio-button>
                </el-radio-group>
              </el-form-item>
                  <el-form-item  label="专业版声音接口:" prop="xunfei_sound_clone_swich">
                <el-radio-group  v-model="form.xunfei_sound_clone_swich" size="small">
                  <el-radio-button :label=1 :value=1>开启</el-radio-button>
                  <el-radio-button :label=2 :value=2>关闭</el-radio-button>
                </el-radio-group>
              </el-form-item> 

               <el-form-item  label="专业版声音审核:" prop="xunfei_sound_clone_check">
                <el-radio-group  v-model="form.xunfei_sound_clone_check" size="small">
                  <el-radio-button :label=1 :value=1>开启</el-radio-button>
                  <el-radio-button :label=2 :value=2>关闭</el-radio-button>
                </el-radio-group>
              </el-form-item> 
              
              <el-form-item  label="声音克隆入门版审核:" prop="voice_check">
                <el-radio-group  v-model="form.voice_check" size="small">
                  <el-radio-button :label=1 :value=1>开启</el-radio-button>
                  <el-radio-button :label=2 :value=2>关闭</el-radio-button>
                </el-radio-group>
              </el-form-item>

              <el-form-item  label="形象克隆协议名称:" prop="protocol_name">
                <el-input v-model="form.protocol_name" placeholder="请输入克隆协议名称" clearable  style="width: 320px;"/>
              </el-form-item>

            </el-col>

            <el-col :lg="8" :md="12">


          <el-form-item  label="是否开启一键分身:" prop="separation_swich">
                <el-radio-group  v-model="form.separation_swich" size="small">
                  <el-radio-button :label=1 :value=1>开启</el-radio-button>
                  <el-radio-button :label=2 :value=2>关闭</el-radio-button>
                </el-radio-group>
              </el-form-item> 

              <el-form-item  label="视频下载后删除:" prop="video_expire" >
                <el-input v-model="form.video_expire" clearable  style="width: 150px;" >
                  <template slot="append">天</template>
                </el-input>
              </el-form-item>
              
              

            </el-col>

            <el-col :lg="8" :md="12">
                 <el-form-item  label="形象克隆审核:"  prop="avatar_check">
            <el-radio-group v-model="form.avatar_check">
              <el-radio :label="1" :value="1">人工审核</el-radio>
              <el-radio :label="2" :value="2">关闭</el-radio>
                <el-radio :label="3" :value="3">AI审核</el-radio>
            </el-radio-group>
          </el-form-item>

<el-form-item label="智能媒体管理项目名称:" prop="alioss_manage_name">
            <el-input v-model="form.alioss_manage_name" placeholder="智能媒体管理项目名称" clearable />
            <div class="text-info">示例：test</div>
          </el-form-item>

           <el-form-item  label="智能媒体管理数据存储节点:" prop="alioss_manage_endpoint">
            <el-input v-model="form.alioss_manage_endpoint" placeholder="请输入阿里云智能媒体管理数据存储节点" clearable />
            <div class="text-info">示例：imm.cn-hangzhou.aliyuncs.com</div>
          </el-form-item>

       


              <!-- <el-form-item  label="形象克隆审核:" prop="avatar_check">
                <el-radio-group  v-model="form.avatar_check" size="small">
                  <el-radio-button :label=1 :value=1>开启</el-radio-button>
                  <el-radio-button :label=2 :value=2>关闭</el-radio-button>
                </el-radio-group>
              </el-form-item> -->
              <!-- <el-form-item  label="形象克隆高级版审核:" prop="avatar_high_check">
                <el-radio-group  v-model="form.avatar_high_check" size="small">
                  <el-radio-button :label=1 :value=1>开启</el-radio-button>
                  <el-radio-button :label=2 :value=2>关闭</el-radio-button>
                </el-radio-group>
              </el-form-item> -->


              <!-- <el-form-item  label="视频换脸审核:" prop="face_check">
                <el-radio-group  v-model="form.face_check" size="small">
                  <el-radio-button :label=1 :value=1>开启</el-radio-button>
                  <el-radio-button :label=2 :value=2>关闭</el-radio-button>
                </el-radio-group>
              </el-form-item> -->





            </el-col>


          </el-row>

          <el-row>
            <el-col :sm="12">
              <el-form-item label="形象克隆协议:" prop="protocol" >
                <tinymce-editor v-model="form.protocol" :init="editorConfig" />
              </el-form-item>
            </el-col>

            <el-col :sm="12">
              <el-form-item label="高级视频生成使用须知:" prop="video_notice">
                <tinymce-editor v-model="form.video_notice" :init="editorConfig" />
              </el-form-item>
            </el-col>

          </el-row>

          <el-row>



            <el-col :sm="12">
              <el-form-item label="线路三视频生成使用须知:" prop="composite_notice">
                <tinymce-editor v-model="form.composite_notice" :init="editorConfig" />
              </el-form-item>
            </el-col>


          </el-row>

          <!-- <el-row>

            <el-col :sm="12">
              <el-form-item label="形象克隆使用须知:" prop="avatar_clone_notice">
                <tinymce-editor v-model="form.avatar_clone_notice" :init="editorConfig" />
              </el-form-item>
            </el-col>
            <el-col :sm="12">
              <el-form-item  label="照片克隆使用须知:" prop="quick_clone_notice" >
                <tinymce-editor v-model="form.quick_clone_notice" :init="editorConfig" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :sm="12">
              <el-form-item  label="声音克隆标准版使用须知:" prop="voice_notice" >
                <tinymce-editor v-model="form.voice_notice" :init="editorConfig" />
              </el-form-item>
            </el-col>

            <el-col :sm="12">
              <el-form-item  label="声音克隆高保真使用须知:" prop="voice_high_notice" >
                <tinymce-editor v-model="form.voice_high_notice" :init="editorConfig" />
              </el-form-item>
            </el-col>

          </el-row>  -->

          <el-form-item>
            <el-button type="primary" :loading="loading" @click="submit">
              提交
            </el-button>
          </el-form-item>
        </el-card>

       <uploadPictures
          :isChoice="isChoice"
          :visible.sync="modalPic"
           @getPic="getPic"
          :gridBtn="gridBtn"
          :gridPic="gridPic"
          :title="modalTitle"
        ></uploadPictures>

      </el-form>
    </div>
  </div>

</template>

<script>
  import uploadPictures from "@/components/uploadPictures";
  import EleImageUpload from 'ele-admin/es/ele-image-upload';
  import TinymceEditor from '@/components/TinymceEditor';
  import request from '@/utils/request';
  import { query ,save } from '@/api/cloneSet';
  const DEFAULT_FORM = {
    protocol_name: '',
    protocol: '',
    quick_clone_notice: '',
    avatar_clone_notice: '',
    video_notice: '',
    voice_notice: '',
    voice_high_notice: '',
    voice_check:2,
    voice_high_check:2,
    avatar_check:2,
    avatar_high_check:2,
    photo_check:2,
    photo_high_check:2,
    video_expire:7,
    face_expire:7,
    composite_notice: '',
    face_check:2,


    avatar_open:2,
    avatar_high_open:2,
    voice_high_open:2,
    photo_high_open:2,
    face_open:2,
    video_open :1,
    niwo_video_open:1
  };

  export default {
    name: 'CloneSet',
    components: { EleImageUpload,uploadPictures,TinymceEditor },
    data() {
      return {
        modalTitle:'',
        modalPic: false,
        isChoice: "单选",
        gridBtn: {
        xl: 4,
        lg: 8,
        md: 8,
        sm: 8,
        xs: 8,
      },
      gridPic: {
        xl: 6,
        lg: 8,
        md: 12,
        sm: 12,
        xs: 12,
      },
        // 表单提交状态
        loading: false,
        // 表单数据
        form: {
          ...DEFAULT_FORM
        },
        // 表单验证规则
        rules: {
          name: [
            {
              required: true,
              message: '请输入仓库名',
              trigger: 'blur'
            }
          ]
        },
        // 表单验证信息
        validMsg: '',
        editorConfig:{
          height: 525,
          relative_urls : false,
          convert_urls: false,
          images_upload_handler: (blobInfo, success, error) => {
            const file = blobInfo.blob();
            // 使用 axios 上传，实际开发这段建议写在 api 中再调用 api
            const formData = new FormData();
            formData.append('file', file, file.name);
            request({
              url: '/common/uploadFile',
              method: 'post',
              data: formData,
              onUploadProgress: (e) => {  // 文件上传进度回调

              }
            }).then((res) => {
                if(res.data.code === 0) {
                    success(res.data.data.url);
                }else{
                  error(res.data.message);
                }
            }).catch((e) => {
                error('exception');
            });
          }
        },
      };
    },
    mounted() {
      query().then((msg) => {
        if(msg != null){
          this.form = msg;
        }
      })
      .catch((e) => {
        this.$message.error(e.message);
      });
    },
    methods: {
      // 选择图片
      modalPicTap(tit, picTit,openTitle) {
        this.modalTitle = openTitle;
        this.isChoice = tit === "dan" ? "单选" : "多选";
        this.picTit = picTit;
        this.modalPic = true;
      },
      // 选中图片
      getPic(pc) {
        switch (this.picTit) {
          case "apply_background":
            this.form.apply_background = pc.satt_dir;
            break;
        }
        this.modalPic = false;
      },
      /* 表单提交 */
      submit() {
        this.$refs['form'].validate((valid, obj) => {
          if (valid) {
            this.validMsg = '';
            this.loading = true;
            const data = {
            ...this.form
            };
            save(data).then((msg) => {
              this.loading = false;
              this.$message.success(msg);
            }).catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });
          } else {
            this.validMsg = ` 共有校验 ${Object.keys(obj).length} 项不通过`;
            return false;
          }
        });
      },
      onUpload(item){
         console.log('item:', item);
          item.status = 'uploading';
          const formData = new FormData();
          formData.append('file', item.file);
          request({
              url: '/common/uploadFile',
              method: 'post',
              data: formData,
              onUploadProgress: (e) => {  // 文件上传进度回调
                  if (e.lengthComputable) {
                      item.progress = e.loaded / e.total * 100;
                  }
              }
          }).then((res) => {
              if(res.data.code === 0) {
                  item.status = 'done';
                  item.url = res.data.data.url;
                  // 如果你上传的不是图片格式, 建议将 url 字段作为缩略图, 再添加其它字段作为最后提交数据
                  //item.url = res.data.data.thumbnail;  // 也可以不赋值 url 字段, 默认会显示为一个文件图标
                  item.fileUrl = res.data.data.url;
              }
          }).catch((e) => {
              item.status = 'exception';
          });
      }
    }
  };
</script>
