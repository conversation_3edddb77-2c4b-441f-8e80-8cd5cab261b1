<template>
  <ele-table-select
    ref="select"
    :clearable="true"
    size="small"
    :value="value"
    placeholder="请选择"
    value-key="id"
    label-key="name"
    prefix-icon="el-icon-search"
    :table-config="tableConfig"
    @select="updateValue"
  >
    <template v-slot:toolbar>
      <div style="max-width: 300px">
        <el-input
          clearable
          size="small"
          v-model="keywords"
          placeholder="输入关键字搜索"
          prefix-icon="el-icon-search"
          @blur="search"
        />
      </div>
    </template>
  
  </ele-table-select>
  
</template>

<script>
  import { cardList } from '@/api/voiceTwin';
  export default {
    name: 'CardList',
    props: {
      // 修改回显的数据
      value: undefined
    },
    data() {
      return {
        keywords: '',
        // 搜索表单
        where: {
          keywords: ''
        },
        tableConfig: {
          datasource: ({ page, limit, where, order }) => {
            where.keywords = this.keywords;
            return cardList({ ...where, ...order, page, limit });
          },
          columns: [
            {
              columnKey: 'index',
              type: 'index',
              width: 45,
              align: 'center'
            },
            {
              prop: 'card',
              label: '卡密',
              align: 'center',
              showOverflowTooltip: true,
              minWidth: 110,
            },
            {
              prop: 'due_time',
              label: '到期时间',
              align: 'center',
              showOverflowTooltip: true,
              minWidth: 110
            },
          ],
          rowClickChecked: true,
          rowClickCheckedIntelligent: false,
          toolkit: ['reload', 'columns'],
          size: 'small',
          toolStyle: { padding: '0 10px' }
        }
      };
    },
    methods: {
      updateValue(data) {
        this.$emit('done', data);
      },
      // 搜索
      search(where) {
        where.keywords = this.keywords;
        this.$refs.select.reload({
          where: where,
          page: 1
        });
      }
    }
  };
</script>
