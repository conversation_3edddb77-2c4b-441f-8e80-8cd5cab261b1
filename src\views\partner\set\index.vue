<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">合伙人设置</div>
    </div>
    <div class="ele-body" style="padding-bottom: 71px;background: var(--color-white);">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="150px"
        @keyup.enter.native="submit"
        @submit.native.prevent
      >
        <el-card shadow="never" body-style="padding: 22px 22px 0 22px;">


          <el-row :gutter="15">
            <el-col :lg="10" :md="8">
              <el-form-item label="是否开启:" prop="is_open">
                <el-radio-group v-model="form.is_open" clearable>
                  <el-radio :label="1" value="1" >开启</el-radio>
                  <el-radio :label="2" value="2" >隐藏</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="申请金额:" prop="is_money">
                <el-radio-group v-model="form.is_money" clearable>
                  <el-radio :label="1" value="1" >展示</el-radio>
                  <el-radio :label="2" value="2" >隐藏</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="申请金额:" prop="money" v-if="form.is_money == 1">
                <el-input type="number" step="0.01" v-model="form.money" placeholder="请输入申请金额" clearable >
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>

            </el-col>
           
            <el-col :lg="10" :md="8">
              <el-form-item label="申请背景图:" prop="apply_bg">
                <span slot="label">
                  申请背景图
                  <el-tooltip placement="top">
                    <div slot="content">
                      建议上传750px * 3996px尺寸,或者等比例图片
                    </div>
                    <i class="el-icon-question" />
                  </el-tooltip>
                </span>  
                <div class="ele-image-upload-list">
                  <div class="ele-image-upload-item ele-image-upload-button" @click="modalPicTap('dan','apply_bg','申请背景图')">
                    <div>
                      <div tabindex="0" class="el-upload el-upload--text">
                        <div class="el-upload-dragger">
                          <i class="el-icon-plus ele-image-upload-icon"></i>
                        </div>
                          <div class="ele-image-upload-item" style="margin:0 0 0 0;" v-if="form.apply_bg!=''">
                          <div class="el-image" >
                            <img :src="form.apply_bg" width="100%" height="100%"  class="el-image__inner" style="object-fit: cover;">
                          </div>
                          <div class="ele-image-upload-close" @click="handleRemove('apply_bg')"><i class="el-icon-close"></i></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="申请按钮颜色:" prop="apply_button">
                <el-color-picker v-model="form.apply_button" show-alpha :predefine="predefineColors"></el-color-picker>
              </el-form-item>
              <el-form-item label="申请按钮文字颜色:" prop="apply_text">
                <el-color-picker v-model="form.apply_text" show-alpha :predefine="predefineColors"></el-color-picker>
              </el-form-item>
            </el-col>

            
          </el-row>
         
        
          <el-form-item>
            <el-button type="primary" :loading="loading" @click="submit">
              提交
            </el-button>
          </el-form-item>
        </el-card>
        
       <uploadPictures
          :isChoice="isChoice"
          :visible.sync="modalPic"
           @getPic="getPic"
          :gridBtn="gridBtn"
          :gridPic="gridPic"
          :title="modalTitle"
        ></uploadPictures>
        
      </el-form>
    </div>
  </div>
  
</template>

<script>
  import uploadPictures from "@/components/uploadPictures";
  import EleImageUpload from 'ele-admin/es/ele-image-upload';
  import TinymceEditor from '@/components/TinymceEditor';
  import request from '@/utils/request';
  import { query ,save } from '@/api/partner/set';
  const DEFAULT_FORM = {
    is_open:2,
    is_money:2,
    money: '',
    apply_bg: '',
    apply_button: '',
    apply_text: '',
  };

  export default {
    name: 'FormAdvanced',
    components: { EleImageUpload,uploadPictures,TinymceEditor },
    data() {
      return {
        modalTitle:'',
        modalPic: false,
        isChoice: "单选",
          gridBtn: {
          xl: 4,
          lg: 8,
          md: 8,
          sm: 8,
          xs: 8,
        },
        gridPic: {
          xl: 6,
          lg: 8,
          md: 12,
          sm: 12,
          xs: 12,
        },
        predefineColors: [
          '#ff4500',
          '#ff8c00',
          '#ffd700',
          '#90ee90',
          '#00ced1',
          '#1e90ff',
          '#c71585',
          'rgba(255, 69, 0, 0.68)',
          'rgb(255, 120, 0)',
          'hsv(51, 100, 98)',
          'hsva(120, 40, 94, 0.5)',
          'hsl(181, 100%, 37%)',
          'hsla(209, 100%, 56%, 0.73)',
          '#c7158577'
        ],
        // 表单提交状态
        loading: false,
        // 表单数据
        form: {
          ...DEFAULT_FORM
        },
        // 表单验证规则
        rules: {
          name: [
            {
              required: true,
              message: '请输入仓库名',
              trigger: 'blur'
            }
          ]
        },
        // 表单验证信息
        validMsg: '',
        editorConfig:{
          height: 525,
          relative_urls : false,
          convert_urls: false,
          images_upload_handler: (blobInfo, success, error) => {
            const file = blobInfo.blob();
            // 使用 axios 上传，实际开发这段建议写在 api 中再调用 api
            const formData = new FormData();
            formData.append('file', file, file.name);
            request({
              url: '/common/uploadFile',
              method: 'post',
              data: formData,
              onUploadProgress: (e) => {  // 文件上传进度回调
                     
              }
            }).then((res) => {
                if(res.data.code === 0) {
                    success(res.data.data.url);
                }else{
                  error(res.data.message);
                }
            }).catch((e) => {
                error('exception');
            });
          }
        },
      };
    },
    mounted() {
      query().then((msg) => {
        if(msg != null){
          this.form = msg;
        }
      })
      .catch((e) => {
        this.$message.error(e.message);
      });
    },
    methods: {
      // 选择图片
      modalPicTap(tit, picTit,openTitle) {
        this.modalTitle = openTitle;
        this.isChoice = tit === "dan" ? "单选" : "多选";
        this.picTit = picTit;
        this.modalPic = true;
      },
      // 选中图片
      getPic(pc) {
        switch (this.picTit) {
          case "apply_bg":
            this.form.apply_bg = pc.satt_dir;
            break;
        }
        this.modalPic = false;
      },
      //删除图片
      handleRemove(field) {
        this.form[field] = '';
      },
      /* 表单提交 */
      submit() {
        this.$refs['form'].validate((valid, obj) => {
          if (valid) {
            this.validMsg = '';
            this.loading = true;
            const data = {
            ...this.form
            };
            save(data).then((msg) => {
              this.loading = false;
              this.$message.success(msg);
            }).catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });
          } else {
            this.validMsg = ` 共有校验 ${Object.keys(obj).length} 项不通过`;
            return false;
          }
        });
      },
      onUpload(item){
         console.log('item:', item);
          item.status = 'uploading';
          const formData = new FormData();
          formData.append('file', item.file);
          request({
              url: '/common/uploadFile',
              method: 'post',
              data: formData,
              onUploadProgress: (e) => {  // 文件上传进度回调
                  if (e.lengthComputable) {
                      item.progress = e.loaded / e.total * 100;
                  }
              }
          }).then((res) => {
              if(res.data.code === 0) {
                  item.status = 'done';
                  item.url = res.data.data.url;
                  // 如果你上传的不是图片格式, 建议将 url 字段作为缩略图, 再添加其它字段作为最后提交数据
                  //item.url = res.data.data.thumbnail;  // 也可以不赋值 url 字段, 默认会显示为一个文件图标
                  item.fileUrl = res.data.data.url;
              }
          }).catch((e) => {
              item.status = 'exception';
          });
      }
    }
  };
</script>
