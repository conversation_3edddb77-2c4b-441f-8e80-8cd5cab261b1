import axios from '@/utils/request';

/**
 * 普通订单查询
 * @param params 查询条件
 */
export async function getCategoryListApi(params) {
 
  const res = await axios.get('/system.uploadFile/catelist', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 订单导出
 * @param params 查询条件
 */
 export async function createApi(params) {
  const res = await axios.get('/order/listOrderRecords', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 对应的图片
 * @param params 查询条件
 */
 export async function fileListApi(params) {
  const res = await axios.get('/system.uploadFile/index', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 订单导出
 * @param params 查询条件
 */
 export async function moveApi(params) {
  const res = await axios.get('/order/listFaceRecords', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 团购订单查询
 * @param params 查询条件
 */
 export async function fileUpdateApi(params) {
 
  const res = await axios.get('/order/pageMealLists', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
export async function uploadFileMp4(data) {
 
  const res = await axios.post('/common/uploadFile', data);
  if (res.data.code === 0) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 直付订单查询
 * @param params 查询条件
 */
 export async function pageFaceLists(params) {
 
  const res = await axios.get('/order/pageFaceLists', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

