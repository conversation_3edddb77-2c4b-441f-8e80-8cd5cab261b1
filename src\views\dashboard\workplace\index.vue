<template>
  <div class="ele-body ele-body-card">
    <profile-card />
    <link-card ref="linkCard" />
    <vue-draggable
      tag="el-row"
      v-model="data"
      handle=".el-card__header"
      :component-data="{ props: { gutter: 15 } }"
      :animation="300"
      :set-data="() => void 0"
      @end="onEnd"
    >
      <el-col
        v-for="(item, index) in data"
        :key="item.name"
        :md="item.md"
        :sm="item.sm"
      >
        <component
          :is="item.name"
          :title="item.title"
          @remove="onRemove(index)"
          @edit="onEdit(index)"
        />
      </el-col>
    </vue-draggable>

  </div>
</template>

<script>
  import VueDraggable from 'vuedraggable';
  import ProfileCard from './components/profile-card.vue';
  import LinkCard from './components/link-card.vue';
  import ActivitiesCard from './components/activities-card.vue';
  import TaskCard from './components/task-card.vue';
  import GoalCard from './components/goal-card.vue';
  import ProjectCard from './components/project-card.vue';
  import UserList from './components/user-list.vue';
  import StatisticsCard from './components/statistics-card.vue';
  import SaleCard from './components/sale-card.vue';
  export default {
    name: 'DashboardWorkplace',
    components: {
      VueDraggable,
      ProfileCard,
      LinkCard,
      ActivitiesCard,
      TaskCard,
      GoalCard,
      ProjectCard,
      UserList,
      StatisticsCard,
      SaleCard
    },
    data() {
      // 默认布局
      const defaultData = [
        {
          name: 'statistics-card',
          title: '最新动态',
          md: 24,
          sm: 24
        },
        {
          name: 'sale-card',
          title: '我的任务',
          md: 24,
          sm: 24
        },
        /*{
          name: 'goal-card',
          title: '本月目标',
          md: 8,
          sm: 24
        },
        {
          name: 'project-card',
          title: '项目进度',
          md: 16,
          sm: 24
        },
        {
          name: 'user-list',
          title: '小组成员',
          md: 8,
          sm: 24
        }*/
      ];
      // 获取缓存布局
     /* const cache = (() => {
        const str = localStorage.getItem('workplace-layout');
        try {
          return str ? JSON.parse(str) : null;
        } catch (e) {
          return null;
        }
      })();*/
      const cache = null;
      return {
        defaultData,
        data: [...(cache ?? defaultData)],
        visible: false
      };
    },
    computed: {
      // 未添加的数据
      notAddedData() {
        return this.defaultData.filter(
          (d) => !this.data.some((t) => t.name === d.name)
        );
      }
    },
    methods: {
      /* 添加 */
      add() {
        this.visible = true;
      },
      /* 重置布局 */
      reset() {
        this.data = [...this.defaultData];
        this.cacheData();
        this.$refs.linkCard.reset();
        this.$message.success('已重置');
      },
      /* 缓存布局 */
      cacheData() {
        //localStorage.setItem('workplace-layout', JSON.stringify(this.data));
      },
      /* 删除视图 */
      onRemove(index) {
        this.data = this.data.filter((_d, i) => i !== index);
        this.cacheData();
      },
      /* 编辑视图 */
      onEdit(index) {
        console.log('index:', index);
        this.$message.info('点击了编辑');
      },
      /* 添加视图 */
      addView(item) {
        this.data.push(item);
        this.cacheData();
        this.$message.success('已添加');
      },
      /* 排序改变 */
      onEnd() {
        this.cacheData();
      }
    }
  };
</script>

<style scoped>
  .ele-body ::v-deep .el-card__header {
    cursor: move;
    position: relative;
  }

  .ele-body ::v-deep .el-row > .el-col.sortable-chosen > .el-card {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
  }

  .workplace-bottom-btn {
    text-align: center;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .workplace-bottom-btn:hover {
    background: hsla(0, 0%, 60%, 0.05);
  }

  /* 添加弹窗 */
  .workplace-card-item {
    margin-bottom: 15px;
    border-width: 1px;
    border-style: solid;
    border-radius: 4px;
    position: relative;
    cursor: pointer;
    transition: box-shadow 0.3s;
  }

  .workplace-card-item:hover {
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
  }

  .workplace-card-item .workplace-card-header {
    border-bottom-width: 1px;
    border-bottom-style: solid;
    padding: 8px;
  }

  .workplace-card-body {
    font-size: 24px;
    padding: 40px 10px;
    text-align: center;
  }
</style>
