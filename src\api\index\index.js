import axios from '@/utils/request';

/**
 * 版本更新日志
 * @param data 配置数据
 */
export async function versionList(params) {
    const res = await axios.post('/index/versionList', params);
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 查询版本
 * @param data 配置数据
 */
 export async function getVersion(params) {
    const res = await axios.post('/index/getVersion', params);
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 更新版本
 * @param data 配置数据
 */
 export async function updateVersion(params) {
    const res = await axios.post('/index/updateVersion', params);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}