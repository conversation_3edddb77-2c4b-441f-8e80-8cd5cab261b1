import axios from '@/utils/request';


/**
 * 查询代理系统积分记录
 * @param data 配置数据
 */
 export async function balanceLog(params) {
    const res = await axios.post('/figure.balanceLog/index', params);
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 充值
 * @param data 记录信息
 */
 export async function addBalance(data) {
  const res = await axios.post('/figure.balanceLog/add', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 删除
 * @param id id
 */
export async function remove(id) {
    const res = await axios.get('/figure.balanceLog/delete?id=' + id);
    if (res.data.code === 0) {
      return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
  


/**
 * 查询代理系统积分配置
 * @param params 查询条件
 */
export async function queryBalanceSet(params) {
  const res = await axios.get('/figure.balanceLog/queryBalanceSet', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 保存代理系统积分配置信息
 * @param data 配置数据
 */
export async function saveBalanceSet(data) {
    const res = await axios.post('/figure.balanceLog/saveBalanceSet', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
