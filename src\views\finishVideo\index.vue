<template>
  <div class="ele-body">
    <el-card shadow="never">
      <ele-split-layout
        width="300px"
        allow-collapse
        :right-style="{ overflow: 'hidden' }"
      >
        <!-- 左侧分类管理 -->
        <category-list
          @category-change="onCategoryChange"
          @category-updated="onCategoryUpdated"
          ref="categoryList"
        />

        <!-- 右侧视频管理 -->
        <template v-slot:content>
          <div v-if="currentCategory">
            <!-- 搜索表单 -->
            <video-search @search="reload" ref="videoSearch" />

            <!-- 数据表格 -->
            <ele-pro-table
              ref="table"
              :columns="columns"
              :datasource="datasource"
              :selection.sync="selection"
            >
              <!-- 表头工具栏 -->
              <template v-slot:toolbar>
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-upload"
                  class="ele-btn-icon"
                  @click="openUpload"
                >
                  上传视频
                </el-button>
                <el-button
                  size="small"
                  type="success"
                  icon="el-icon-refresh"
                  class="ele-btn-icon"
                  @click="reload"
                >
                  刷新
                </el-button>
              </template>

              <!-- 封面列 -->
              <template v-slot:cover="{ row }">
                <el-image
                  v-if="row.cover"
                  :src="row.cover"
                  fit="cover"
                  style="width: 60px; height: 107px; border-radius: 4px;"
                  :preview-src-list="[row.cover]"
                />
                <div v-else style="width: 60px; height: 107px; border-radius: 4px; background: #f5f7fa; display: flex; align-items: center; justify-content: center; border: 1px dashed #dcdfe6;">
                  <span style="color: #999; font-size: 11px; text-align: center;">加载中...</span>
                </div>
              </template>

              <!-- 视频预览列 -->
              <template v-slot:video="{ row }">
                <video
                  width="320"
                  height="240"
                  controls
                  :src="row.url"
                  v-if="row.url"
                >
                  您的浏览器不支持视频播放
                </video>
                <span v-else>--</span>
              </template>

              <!-- 类型列 -->
              <template v-slot:type="{ row }">
                <el-tag type="primary" size="mini">
                  {{ currentCategory.name }}
                </el-tag>
              </template>

              <!-- 操作列 -->
              <template v-slot:action="{ row }">
                <el-button
                  type="text"
                  size="small"
                  icon="el-icon-view"
                  @click="previewVideo(row)"
                >
                  预览
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  icon="el-icon-edit"
                  @click="openEdit(row)"
                >
                  修改
                </el-button>
                <el-popconfirm
                  class="ele-action"
                  title="确定要删除此视频吗？"
                  @confirm="remove(row)"
                >
                  <template v-slot:reference>
                    <el-button type="text" size="small" icon="el-icon-delete">
                      删除
                    </el-button>
                  </template>
                </el-popconfirm>
              </template>
            </ele-pro-table>
          </div>

          <!-- 未选择分类提示 -->
          <div v-else class="no-category">
            <el-empty description="请先选择或创建视频分类">
              <el-button type="primary" @click="$refs.categoryList.openEdit()">
                创建分类
              </el-button>
            </el-empty>
          </div>
        </template>
      </ele-split-layout>
    </el-card>

    <!-- 视频上传弹窗 -->
    <video-upload
      :visible.sync="showUpload"
      :type-id="currentCategory ? currentCategory.id : null"
      @done="onUploadDone"
    />

    <!-- 视频编辑弹窗 -->
    <video-edit
      :visible.sync="showEdit"
      :data="currentVideo"
      @done="onEditDone"
    />

    <!-- 视频预览弹窗 -->
    <el-dialog
      title="视频预览"
      :visible.sync="showPreview"
      width="60%"
      center
    >
      <div style="text-align: center;">
        <video
          width="100%"
          height="400"
          controls
          :src="previewUrl"
          v-if="previewUrl"
        >
          您的浏览器不支持视频播放
        </video>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import VideoSearch from './components/video-search';
  import CategoryList from './components/category-list';
  import VideoUpload from './components/video-upload';
  import VideoEdit from './components/video-edit';
  import { getFinishVideoList, deleteFinishVideo } from '@/api/finishVideo';

  export default {
    name: 'FinishVideo',
    components: { VideoSearch, CategoryList, VideoUpload, VideoEdit },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            align: 'center',
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'id',
            label: '视频ID',
            showOverflowTooltip: true,
            width: 80,
            align: 'center'
          },
          {
            prop: 'name',
            label: '视频标题',
            showOverflowTooltip: true,
            minWidth: 150
          },
          {
            prop: 'cover',
            label: '视频封面',
            showOverflowTooltip: true,
            width: 100,
            align: 'center',
            slot: 'cover'
          },
          {
            prop: 'video',
            label: '视频预览',
            showOverflowTooltip: true,
            minWidth: 350,
            align: 'center',
            slot: 'video'
          },
          {
            prop: 'type_id',
            label: '视频分类',
            align: 'center',
            showOverflowTooltip: true,
            width: 120,
            slot: 'type'
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 200,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            showOverflowTooltip: true
          }
        ],
        // 选中数据
        selection: [],
        // 当前选中的分类
        currentCategory: null,
        // 是否显示上传弹窗
        showUpload: false,
        // 是否显示编辑弹窗
        showEdit: false,
        // 当前编辑的视频数据
        currentVideo: null,
        // 是否显示预览弹窗
        showPreview: false,
        // 预览视频地址
        previewUrl: ''
      };
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where }) {
        if (!this.currentCategory) {
          return Promise.resolve({ list: [], total: 0 });
        }

        return getFinishVideoList({
          page,
          limit,
          type_id: this.currentCategory.id
        });
      },
      /* 分类切换事件 */
      onCategoryChange(category) {
        this.currentCategory = category;
        if (category && this.$refs.table) {
          this.reload();
        }
      },
      /* 分类更新事件（增删改后） */
      onCategoryUpdated() {
        // 刷新搜索组件的分类列表
        if (this.$refs.videoSearch) {
          this.$refs.videoSearch.refresh();
        }
      },
      /* 刷新表格 */
      reload(where) {
        if (this.$refs.table) {
          this.$refs.table.reload({ page: 1, where: where || {} });
        }
      },
      /* 打开上传弹窗 */
      openUpload() {
        this.showUpload = true;
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.currentVideo = { ...row };
        this.showEdit = true;
      },
      /* 上传完成事件 */
      onUploadDone() {
        // 刷新视频列表
        this.reload();
        // 刷新分类列表（可能有新建分类）
        if (this.$refs.categoryList) {
          this.$refs.categoryList.refresh();
        }
        // 刷新搜索组件的分类列表
        if (this.$refs.videoSearch) {
          this.$refs.videoSearch.refresh();
        }
      },
      /* 编辑完成事件 */
      onEditDone() {
        // 刷新视频列表
        this.reload();
        // 刷新分类列表（可能有新建分类）
        if (this.$refs.categoryList) {
          this.$refs.categoryList.refresh();
        }
        // 刷新搜索组件的分类列表
        if (this.$refs.videoSearch) {
          this.$refs.videoSearch.refresh();
        }
      },
      /* 预览视频 */
      previewVideo(row) {
        this.previewUrl = row.url;
        this.showPreview = true;
      },
      /* 删除视频 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        deleteFinishVideo(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .ele-action + .ele-action {
    margin-left: 12px;
  }

  .ele-body {
    padding: 15px;
  }

  .no-category {
    display: flex;
    align-items: center;
    justify-content: center;
    height: calc(100vh - 300px);
    text-align: center;
  }
</style>
