<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <avatar-search @search="reload"/>
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            新增
          </el-button> 
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            class="ele-btn-icon"
            @click="removeBatch"
          >
            删除
          </el-button>
          
        </template>

        <template v-slot:mold="{ row }">
          <el-tag v-if="row.mold==1" type="primary" effect="plain">小程序端</el-tag>
          <el-tag v-if="row.mold==2" type="primary" effect="plain">H5端</el-tag>
        </template>

        <template v-slot:type="{ row }">
          <el-tag v-if="row.type==1" type="primary" effect="plain">内部链接</el-tag>
          <el-tag v-if="row.type==2" type="primary" effect="plain">外部链接</el-tag>
        </template>


        <template v-slot:avatar="{ row }">
        
          <div class="ele-cell" v-if="row.figureAvatar">
            <el-avatar v-if="row.avatar_img" :src="row.avatar_img" shape="square" :size="40" />
            <div class="ele-cell-content">
              <div class="ele-cell-title">{{ row.figureAvatar.name }} </div>
            </div>
          </div>

          <div v-else>--</div>
          
        </template>

        <template v-slot:avatar_video="{ row }">
          <video width="320" height="200" controls :src="row.avatar_video"  v-if="row.avatar_video"> </video>
          <div v-else>--</div>
        </template>


        <template v-slot:voice="{ row }">
          <div v-if="row.figureVoice">{{ row.figureVoice.name }} </div>
          <div v-else>--</div>
        </template>

        <template v-slot:voice_mode="{ row }">
          <el-tag v-if="row.voice_mode==1" type="success" effect="plain">入门版版</el-tag>
          <el-tag v-if="row.voice_mode==2" type="primary" effect="plain">高保真</el-tag>
               <el-tag v-if="row.voice_mode==3" type="primary" effect="plain">专业版</el-tag>
        </template>

        <!-- 状态列 -->
        <template v-slot:is_display="{ row }">
          <el-switch
            :active-value="1"
            :inactive-value="2"
            v-model="row.is_display"
            @change="editStatus(row)"
          />
        </template>


        <template v-slot:sort>
          <i class="sort-handle el-icon-rank ele-text-secondary" style="cursor: move;"></i>
        </template>

        
        <!-- 操作列 -->
        <template v-slot:action="{ row }">
          <el-link
            type="primary"
            :underline="false"
            icon="el-icon-edit"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-popconfirm
            class="ele-action"
            title="确定要删除此数据吗？"
            @confirm="remove(row)"
          >
            <template v-slot:reference>
              <el-link type="danger" :underline="false" icon="el-icon-delete">
                删除
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
    <!-- 编辑弹窗 -->
    <avatar-edit :visible.sync="showEdit" :data="current"  @done="reload"/>
  </div>
</template>

<script>
  import AvatarSearch from './components/avatar-search';
  import AvatarEdit from './components/avatar-edit';
  import { list, remove, modify,sortChange  } from '@/api/screen/avatar';
  import Sortable from 'sortablejs';

  export default {
    name: 'Avatar',
    components: {
      AvatarSearch,
      AvatarEdit
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'selection',
            type: 'selection',
            width: 45,
            align: 'center',
            fixed: 'left'
          },
          {
            prop: 'sort',
            label: '排序',
            showOverflowTooltip: true,
            minWidth: 60,
            slot: 'sort'
          },
         
          {
            prop: 'avatar',
            label: '形象',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'avatar'
          },
          {
            prop: 'avatar_video',
            label: '形象视频',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 200,
            slot: 'avatar_video'
          },
          {
            prop: 'voice',
            label: '声音',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'voice'
          },
          {
            prop: 'voice_mode',
            label: '声音模式',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'voice_mode'
          },
          {
            prop: 'is_display',
            label: '展示状态',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'is_display'
          },
          {
            prop: 'create_time',
            label: '创建时间',
            align: 'center',
            showOverflowTooltip: true,
            minWidth: 100,
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 240,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            showOverflowTooltip: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示分配回收员
        showCollector: false,
        //弹框名称
        id:0,
      };
    },
    methods: {
      rowDrop() {
        const tbody = document.querySelector('.el-table__body-wrapper tbody')
        const _this = this
        Sortable.create(tbody, {
          forceFallback: true,
          dragClass: 'drag',
          delay:100,
          animation: 1000,
          onEnd({ newIndex, oldIndex }) {
            console.log(newIndex);
            console.log(oldIndex);
            //更换表格数据 后续拖动坐标才会正确
            let data = _this.$refs.table.getData();
             
            let nid = data[newIndex].id;
            let oid = data[oldIndex].id;

            console.log(nid);
            console.log(oid);
            
            const currRow = data.splice(oldIndex, 1)[0];
            data.splice(newIndex, 0, currRow);

            sortChange(data).then((msg) => {
              
            }).catch((e) => {
              _this.$refs.table.reload();
            });
            //去数据库更换排序
            console.log(_this.$refs.table.getData());
          }
        })
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return list({ ...where, ...order, page, limit });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id).then((msg) => {
          loading.close();
          this.$message.success(msg);
          this.reload();
        })
        .catch((e) => {
          loading.close();
          this.$message.error(e.message);
        });
      },
      
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的数据吗?', '提示', {
          type: 'warning'
        }).then(() => {
          const loading = this.$loading({ lock: true });
          remove(this.selection.map((d) => d.id))
            .then((msg) => {
              loading.close();
              this.$message.success(msg);
              this.reload();
            })
            .catch((e) => {
              loading.close();
              this.$message.error(e.message);
            });
        }).catch(() => {});
      },
      
      /* 更改状态 */
      editStatus(row) {
        const loading = this.$loading({ lock: true });
        modify({id:row.id,field:'is_display',value: row.is_display})
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
          })
          .catch((e) => {
            loading.close();
            row.status = !row.status ? 1 : 0;
            this.$message.error(e.message);
          });
      }
    },mounted() {
      this.rowDrop()
      //this.columnDrop()
    }
  };
</script>

<style>
 .drag {
    background: #000 !important;
    background-image: linear-gradient(#333, #999) !important;
  }
</style>
