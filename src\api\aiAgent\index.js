import axios from '@/utils/request';

/**
 * 分页查询智能体列表
 * @param data 查询条件
 */
export async function list(data) {
  const res = await axios.post('/figure.code/list', data);
  if (res.data.errno === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加智能体
 * @param data 智能体信息
 */
export async function add(data) {
  const res = await axios.post('/figure.code/add', data);
  if (res.data.errno === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 修改智能体
 * @param data 智能体信息
 */
export async function update(data) {
  const res = await axios.post('/figure.aiAgent/edit', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除智能体
 * @param id 智能体ID
 */
export async function remove(id) {
  const res = await axios.post('/figure.aiAgent/remove', { id });
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取空间列表
 * @param params 查询条件
 */
export async function getSpaceList(params) {
  const res = await axios.get('/figure.code/work', {
    params
  });
  if (res.data.errno === 0) {
    return res.data.data.data.workspaces;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据空间ID获取智能体ID列表
 * @param data 查询条件，包含work_id
 */
export async function getAgentList(data) {
  const res = await axios.post('/figure.code/bots', data);
  if (res.data.errno === 0) {
    return res.data.data.data.items;
  }
  return Promise.reject(new Error(res.data.message));
}
