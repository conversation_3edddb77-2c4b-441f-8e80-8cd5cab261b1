<!-- 权益描述编辑弹窗 -->
<template>
  <el-dialog
    title="编辑权益描述"
    :visible.sync="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    :append-to-body="true"
    :modal-append-to-body="true"
    custom-class="benefits-edit-dialog"
  >
    <div class="benefits-editor">
      <!-- 权益列表 -->
      <div v-if="benefitsList.length > 0" class="benefits-list">
        <div 
          v-for="(item, index) in benefitsList" 
          :key="index"
          class="benefit-item"
        >
          <div class="benefit-header">
            <span class="benefit-index">权益 {{ index + 1 }}</span>
            <el-button 
              type="danger" 
              size="mini" 
              icon="el-icon-delete"
              @click="removeBenefit(index)"
            >
              删除
            </el-button>
          </div>
          
          <el-form :model="item" label-width="100px" class="benefit-form">
            <el-form-item label="权益标题:">
              <el-input
                v-model="item.title"
                placeholder="请输入权益标题"
                clearable
              />
            </el-form-item>
            
            <el-form-item label="权益图片:">
              <div class="image-upload-section">
                <div v-if="item.img_url" class="image-preview">
                  <img :src="item.img_url" alt="权益图片" class="preview-img" />
                  <div class="image-actions">
                    <el-button 
                      size="mini" 
                      type="primary"
                      @click="selectImage(index)"
                    >
                      更换图片
                    </el-button>
                    <el-button 
                      size="mini" 
                      type="danger"
                      @click="removeImage(index)"
                    >
                      删除图片
                    </el-button>
                  </div>
                </div>
                <div v-else class="image-upload">
                  <el-button 
                    type="primary" 
                    icon="el-icon-plus"
                    @click="selectImage(index)"
                  >
                    选择图片
                  </el-button>
                </div>
              </div>
            </el-form-item>
            
            <el-form-item label="权益描述:">
              <el-input
                v-model="item.desc"
                type="textarea"
                :rows="3"
                placeholder="请输入权益详细描述"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="empty-state">
        <el-empty description="暂无权益描述">
          <el-button type="primary" @click="addBenefit">添加权益</el-button>
        </el-empty>
      </div>
      
      <!-- 添加按钮 -->
      <div v-if="benefitsList.length > 0" class="add-section">
        <el-button 
          type="primary" 
          icon="el-icon-plus"
          @click="addBenefit"
        >
          添加权益
        </el-button>
      </div>
    </div>
    
    <!-- 弹窗底部按钮 -->
    <template v-slot:footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </template>
    
    <!-- 图片选择组件 -->
    <uploadPictures
      :isChoice="isChoice"
      :visible.sync="modalPic"
      @getPic="getPic"
      :gridBtn="gridBtn"
      :gridPic="gridPic"
      :title="modalTitle"
    />
  </el-dialog>
</template>

<script>
import uploadPictures from '@/components/uploadPictures';

export default {
  name: 'MemberBenefitsEdit',
  components: {
    uploadPictures
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 权益描述数据，JSON字符串或数组
    value: {
      type: [String, Array],
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      benefitsList: [],
      currentEditIndex: -1, // 当前编辑的权益索引
      
      // 图片上传相关
      modalTitle: '选择图片',
      modalPic: false,
      isChoice: '单选',
      gridBtn: {
        xl: 4,
        lg: 8,
        md: 8,
        sm: 8,
        xs: 8,
      },
      gridPic: {
        xl: 6,
        lg: 8,
        md: 12,
        sm: 12,
        xs: 12,
      }
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.initBenefitsList();
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    }
  },
  methods: {
    // 初始化权益列表
    initBenefitsList() {
      console.log('初始化权益列表，接收到的数据:', this.value, typeof this.value);

      if (typeof this.value === 'string') {
        try {
          this.benefitsList = this.value ? JSON.parse(this.value) : [];
        } catch (e) {
          console.error('解析权益描述数据失败:', e, this.value);
          this.benefitsList = [];
        }
      } else if (Array.isArray(this.value)) {
        this.benefitsList = JSON.parse(JSON.stringify(this.value));
      } else {
        this.benefitsList = [];
      }

      // 确保每个权益项都有必要的字段
      this.benefitsList = this.benefitsList.map(item => ({
        title: item.title || '',
        img_url: item.img_url || '',
        desc: item.desc || ''
      }));

      console.log('初始化后的权益列表:', this.benefitsList);
    },
    
    // 添加权益
    addBenefit() {
      this.benefitsList.push({
        title: '',
        img_url: '',
        desc: ''
      });
    },
    
    // 删除权益
    removeBenefit(index) {
      this.$confirm('确定要删除这个权益描述吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.benefitsList.splice(index, 1);
      }).catch(() => {});
    },
    
    // 选择图片
    selectImage(index) {
      this.currentEditIndex = index;
      this.modalPic = true;
    },
    
    // 删除图片
    removeImage(index) {
      this.benefitsList[index].img_url = '';
    },
    
    // 获取选中的图片
    getPic(pic) {
      if (this.currentEditIndex >= 0) {
        this.benefitsList[this.currentEditIndex].img_url = pic.satt_dir;
      }
      this.modalPic = false;
      this.currentEditIndex = -1;
    },
    
    // 确定保存
    handleConfirm() {
      // 验证数据
      for (let i = 0; i < this.benefitsList.length; i++) {
        const item = this.benefitsList[i];
        if (!item.title.trim()) {
          this.$message.warning(`请填写第${i + 1}个权益的标题`);
          return;
        }
      }
      
      // 返回权益描述数组
      this.$emit('input', this.benefitsList);
      this.$emit('confirm', this.benefitsList);
      this.dialogVisible = false;
    },
    
    // 取消关闭
    handleClose() {
      this.dialogVisible = false;
    }
  }
};
</script>

<style scoped>
.benefits-editor {
  max-height: 500px;
  overflow-y: auto;
}

/* 确保权益编辑弹窗层级正确，在主编辑弹窗之上 */
::v-deep .benefits-edit-dialog {
  z-index: 2100 !important;
}

::v-deep .benefits-edit-dialog .el-dialog__wrapper {
  z-index: 2100 !important;
}

/* 确保图片选择弹窗在权益编辑弹窗之上 */
::v-deep .ele-modal {
  z-index: 2200 !important;
}

::v-deep .ele-modal .el-dialog__wrapper {
  z-index: 2200 !important;
}

.benefits-list {
  margin-bottom: 20px;
}

.benefit-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: #fafafa;
}

.benefit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.benefit-index {
  font-weight: bold;
  color: #409eff;
}

.benefit-form {
  margin: 0;
}

.image-upload-section {
  width: 100%;
}

.image-preview {
  display: flex;
  align-items: center;
  gap: 15px;
}

.preview-img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.image-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.image-upload {
  display: flex;
  align-items: center;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.add-section {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #e4e7ed;
}
</style>
