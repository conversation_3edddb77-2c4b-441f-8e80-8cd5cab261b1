<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">短信配置</div>
      <div class="ele-page-desc">
        用于短信配置。
      </div>
    </div>
    <div class="ele-body">
      <el-card shadow="never">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="230px"
          style="max-width: 900px; margin: 10px auto"
        >

        <el-form-item label="是否开启:" prop="note_open">
          <el-radio-group v-model="form.note_open">
            <el-radio label='1' value='1'>开启</el-radio>
            <el-radio label='2' value='2'>隐藏</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="短信类型:" prop="note_type">
          <el-radio-group v-model="form.note_type">
            <el-radio label='1' value='1'>内置短信</el-radio>
            <el-radio label='2' value='2'>阿里云短信</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="form.note_type == 1" label="账号:"  prop="note_user">
          <el-input v-model="form.note_user" placeholder="请输入短信账号" class="my_input" clearable />
        </el-form-item>

        <el-form-item v-if="form.note_type == 1" label="密码:"  prop="note_pass">
          <el-input v-model="form.note_pass" show-password type="password" placeholder="请输入短信密码" class="my_input" clearable />
        </el-form-item>

        <el-form-item v-if="form.note_type == 1" label="签名:"  prop="note_signature">
          <el-input v-model="form.note_signature" placeholder="请输入短信签名" class="my_input" clearable />
        </el-form-item>

        <el-form-item v-if="form.note_type == 2" label="AccessKey ID:" prop="note_key_id">
          <el-input v-model="form.note_key_id"  placeholder="AccessKey ID" class="my_input" clearable />
        </el-form-item>

        <el-form-item v-if="form.note_type == 2" label="AccessKey Secret:" prop="note_key_secret">
          <el-input v-model="form.note_key_secret" show-password type="password"  placeholder="AccessKey ID" class="my_input" clearable />
        </el-form-item>

        <el-form-item v-if="form.note_type == 2" label="签名名称:" prop="note_sign_name">
          <el-input v-model="form.note_sign_name"  placeholder="签名名称" class="my_input" clearable />
        </el-form-item>

                
        <el-form-item  label="验证码模版CODE:" prop="note_verify_code">
          <el-input v-model="form.note_verify_code"  placeholder="验证码模版CODE" class="my_input" clearable />
          <el-button class="content" size="small"  style="margin-left: 10px;" type="primary" @click="showImgViewer(0) ">查看</el-button> 
            <el-image-viewer 
              v-if="imgViewerVisible" 
              :on-close="closeImgViewer" 
              :url-list="form.note_type == 2 ? alyList : ztyList" />
        </el-form-item>

     

        <el-form-item>
          <el-button type="primary" :loading="loading" @click="submit">
            提交
          </el-button>
        </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
  import EleImageUpload from 'ele-admin/es/ele-image-upload';
  import request from '@/utils/request';
  import { save ,query } from '@/api/system/config';
  export default {
    name: 'SetNote',
    components:{ 'el-image-viewer': () => import('element-ui/packages/image/src/image-viewer')},
    data() {
      return {
        url:window.location.protocol + "//" +location.host + '/a/123456',
        imgViewerVisible:false,
        ztyList:[
          '/imgs/note/zty-verify.jpg',
        ],
        alyList:[
          '/imgs/note/aly-verify.jpg',
        ],
        // 提交状态
        fileList:[],
        // 提交状态
        loading: false,
        disabled: false,
        files:1,
        is_key_upload:1,
        // 表单数据
        form: {
          note_open:'2',
          note_type:'1',
          note_user: '',
          note_pass: '',
          note_signature:'',
          note_key_id: '',
          note_key_secret: '',
          note_sign_name: '',
          note_verify_code: '',
        },

        // 表单验证规则
        rules: {
         
        },
        
      };
    },
    methods: {
      showImgViewer(index){
        this.imgViewerVisible = true;
        const m = (e) => { e.preventDefault() };
        document.body.style.overflow = 'hidden';
        document.addEventListener("touchmove", m, false); // 禁止页面滑动

        let tempImgList = this.form.note_type == 2 ?  [...this.alyList] : [...this.ztyList];
        let temp = [];
        for (let i = 0; i < index; i++) {
            temp.push(tempImgList.shift());
        }

        if(this.form.note_type == 2 ){
          this.alyList = tempImgList.concat(temp);
        }else{
          this.ztyList = tempImgList.concat(temp);
        }
      },
      
      closeImgViewer(){
        this.ztyList = [
          '/imgs/note/zty-verify.jpg',
        ];
        this.alyList = [
          '/imgs/note/aly-verify.jpg',
        ];
        this.imgViewerVisible = false;
        const m = (e) => { e.preventDefault() };
        document.body.style.overflow = 'auto';
        document.removeEventListener("touchmove", m, true);
      },
     
      /* 提交 */
      submit() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            this.loading = true;
            save(this.form).then((msg) => {
              this.loading = false;
              this.$message.success(msg);
            }).catch((e) => {
              this.loading = false;
              this.$message.error(e.message);
            });

          } else {
            return false;
          }
        });
      },
    },
     mounted() {
      query({group:'note'}).then((msg) => {
        if(msg != null){
          this.form = msg;  
        }
        
      }).catch((e) => {
        this.$message.error(e.message);
      });
     }
    
  };
</script>
<style>
 .my_input{
  width: 400px;
 }
 .pay_header{
    margin-bottom: 30px;
    font-size: 17px;
  }
</style>
