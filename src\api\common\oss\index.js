import axios from '@/utils/request';

/**
 * 查询用户
 * @param params 查询条件
 */
export async function queryOss() {
  const res = await axios.get('/common/queryOss', {
    
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 添加默认配置
 * @param data 配置信息
 */
 export async function updateOss(data) {
  const res = await axios.post('/common/updateOss', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}






