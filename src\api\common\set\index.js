import axios from '@/utils/request';

/**
 * 阿里云OSS修改
 * @param data form表单数据
 */
export async function ossUpdate(data) {
  const res = await axios.post('/common/ossUpdate', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 阿里云OSS修改
 * @param data form表单数据
 */
 export async function distributionSetUpdate(data) {
  const res = await axios.post('/common/distributionSetUpdate', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 支付配置修改
 * @param data form表单数据
 */
 export async function payUpdate(data) {
  const res = await axios.post('/common/payUpdate', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 基础配置修改
 * @param data form表单数据
 */
 export async function baseUpdate(data) {
  const res = await axios.post('/common/baseUpdate', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 刷新中转点数
 * @param data form表单数据
 */
 export async function refZgNum() {
  const res = await axios.post('/common/refZgNum');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}





/**
 * 优惠券修改
 * @param data form表单数据
 */
 export async function couponUpdate(data) {
  const res = await axios.post('/common/couponUpdate', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 中转修改
 * @param data form表单数据
 */
 export async function zgUpdate(data) {
  const res = await axios.post('/common/zgUpdate', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}






/**
 * 查询阿里云配置
 * @param params 查询条件
 */
 export async function queryWithdraw() {
  const res = await axios.get('/common/queryWithdraw', {
    
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 查询中转收费明细
 * @param params 查询条件
 */
 export async function getZgDebitList() {
  const res = await axios.get('/common/getZgDebitList', {
    
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 重新生成二维码
 */
 export async function reloadQrcode(params) {
  const res = await axios.get('/common/reloadQrcode',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 重新生成二维码
 */
 export async function checkAccount(params) {
  const res = await axios.get('/common/checkAccount',{params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 获取oem代理名下代理新增商户排行榜
 */
 export async function getOemShRank() {
  const res = await axios.get('/common/getOemShRank');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}





/**
 * 生成scheme码
 * @param params 查询条件
 */
 export async function addScheme(data) {
  const res = await axios.post('/common/addScheme', data);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 查询阿里云配置
 * @param params 查询条件
 */
 export async function queryCoupon() {
  const res = await axios.get('/common/queryCoupon', {
    
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询阿里云配置
 * @param params 查询条件
 */
 export async function queryShQrcode() {
  const res = await axios.get('/common/queryOssSet', {
    
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询商户新增图表数据
 * @param params 查询条件
 */
 export async function getAddShData() {
  const res = await axios.get('/common/getAddShData', {
    
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 查询支付配置
 * @param params 查询条件
 */
 export async function queryPaySet() {
  const res = await axios.get('/common/queryPaySet', {
    
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 查询中转配置
 * @param params 查询条件
 */
 export async function queryZgSet() {
  const res = await axios.get('/common/queryZgSet', {
    
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询基础配置
 * @param params 查询条件
 */
 export async function queryBaseSet() {
  const res = await axios.get('/common/queryBaseSet', {
    
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 查询服务端首页
 * @param params 查询条件
 */
 export async function getCurShData() {
  const res = await axios.get('/common/getCurShData', {
    
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询服务端首页
 * @param params 查询条件
 */
 export async function getShRank() {
  const res = await axios.get('/common/getShRank', {
    
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}








