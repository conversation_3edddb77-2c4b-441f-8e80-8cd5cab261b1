import axios from '@/utils/request';


/**
 * 首页设置
 * @param params 查询条件
 */
 export async function query(params) {
    const res = await axios.get('/figure.indexSet/query', {
      params
    });
    if (res.data.code === 0) {
      return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
  }
  
  
  /**
   * 首页设置
   * @param data 配置数据
   */
  export async function save(data) {
      const res = await axios.post('/figure.indexSet/save', data);
      if (res.data.code === 0) {
          return res.data.message;
      }
      return Promise.reject(new Error(res.data.message));
  }